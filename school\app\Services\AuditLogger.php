<?php

namespace App\Services;

use App\Models\AuditLogModel;
use App\Models\ActivityLogModel;
use App\Models\SuperAdminLogModel;

class AuditLogger
{
    protected $auditLogModel;
    protected $activityLogModel;
    protected $superAdminLogModel;

    public function __construct()
    {
        $this->auditLogModel = new AuditLogModel();
        $this->activityLogModel = new ActivityLogModel();
        $this->superAdminLogModel = new SuperAdminLogModel();
    }

    /**
     * Log user login
     */
    public function logLogin($userId, $schoolId = null, $userType = 'user')
    {
        $this->auditLogModel->logAction([
            'school_id' => $schoolId,
            'user_id' => $userId,
            'action' => 'login',
            'entity_type' => 'user',
            'entity_id' => $userId,
            'description' => ucfirst($userType) . ' logged in',
            'severity' => 'low',
            'status' => 'success'
        ]);

        $this->activityLogModel->logActivity([
            'school_id' => $schoolId,
            'user_id' => $userId,
            'activity_type' => 'login',
            'activity_description' => ucfirst($userType) . ' login'
        ]);
    }

    /**
     * Log user logout
     */
    public function logLogout($userId, $schoolId = null, $sessionDuration = null)
    {
        $this->auditLogModel->logAction([
            'school_id' => $schoolId,
            'user_id' => $userId,
            'action' => 'logout',
            'entity_type' => 'user',
            'entity_id' => $userId,
            'description' => 'User logged out',
            'severity' => 'low',
            'status' => 'success'
        ]);

        $this->activityLogModel->logActivity([
            'school_id' => $schoolId,
            'user_id' => $userId,
            'activity_type' => 'logout',
            'activity_description' => 'User logout',
            'duration' => $sessionDuration
        ]);
    }

    /**
     * Log entity creation
     */
    public function logCreate($entityType, $entityId, $entityName, $newData, $userId = null, $schoolId = null, $description = null)
    {
        $this->auditLogModel->logAction([
            'school_id' => $schoolId,
            'user_id' => $userId,
            'action' => 'create',
            'entity_type' => $entityType,
            'entity_id' => $entityId,
            'entity_name' => $entityName,
            'description' => $description ?: "Created {$entityType}: {$entityName}",
            'new_values' => $newData,
            'severity' => $this->getSeverityForAction('create', $entityType),
            'status' => 'success'
        ]);
    }

    /**
     * Log entity update
     */
    public function logUpdate($entityType, $entityId, $entityName, $oldData, $newData, $userId = null, $schoolId = null, $description = null)
    {
        $this->auditLogModel->logAction([
            'school_id' => $schoolId,
            'user_id' => $userId,
            'action' => 'update',
            'entity_type' => $entityType,
            'entity_id' => $entityId,
            'entity_name' => $entityName,
            'description' => $description ?: "Updated {$entityType}: {$entityName}",
            'old_values' => $oldData,
            'new_values' => $newData,
            'severity' => $this->getSeverityForAction('update', $entityType),
            'status' => 'success'
        ]);
    }

    /**
     * Log entity deletion
     */
    public function logDelete($entityType, $entityId, $entityName, $oldData, $userId = null, $schoolId = null, $description = null)
    {
        $this->auditLogModel->logAction([
            'school_id' => $schoolId,
            'user_id' => $userId,
            'action' => 'delete',
            'entity_type' => $entityType,
            'entity_id' => $entityId,
            'entity_name' => $entityName,
            'description' => $description ?: "Deleted {$entityType}: {$entityName}",
            'old_values' => $oldData,
            'severity' => $this->getSeverityForAction('delete', $entityType),
            'status' => 'success'
        ]);
    }

    /**
     * Log approval action
     */
    public function logApproval($entityType, $entityId, $entityName, $userId = null, $schoolId = null, $description = null)
    {
        $this->auditLogModel->logAction([
            'school_id' => $schoolId,
            'user_id' => $userId,
            'action' => 'approve',
            'entity_type' => $entityType,
            'entity_id' => $entityId,
            'entity_name' => $entityName,
            'description' => $description ?: "Approved {$entityType}: {$entityName}",
            'severity' => 'medium',
            'status' => 'success'
        ]);
    }

    /**
     * Log rejection action
     */
    public function logRejection($entityType, $entityId, $entityName, $reason = null, $userId = null, $schoolId = null)
    {
        $description = "Rejected {$entityType}: {$entityName}";
        if ($reason) {
            $description .= " - Reason: {$reason}";
        }

        $this->auditLogModel->logAction([
            'school_id' => $schoolId,
            'user_id' => $userId,
            'action' => 'reject',
            'entity_type' => $entityType,
            'entity_id' => $entityId,
            'entity_name' => $entityName,
            'description' => $description,
            'new_values' => $reason ? ['rejection_reason' => $reason] : null,
            'severity' => 'medium',
            'status' => 'success'
        ]);
    }

    /**
     * Log super admin action
     */
    public function logSuperAdminAction($action, $adminUserId, $description, $targetSchoolId = null, $targetUserId = null, $dataBefore = null, $dataAfter = null, $severity = 'medium')
    {
        return $this->superAdminLogModel->logSuperAdminAction([
            'admin_user_id' => $adminUserId,
            'action' => $action,
            'target_school_id' => $targetSchoolId,
            'target_user_id' => $targetUserId,
            'description' => $description,
            'data_before' => $dataBefore,
            'data_after' => $dataAfter,
            'severity' => $severity
        ]);
    }

    /**
     * Log failed action
     */
    public function logFailedAction($action, $entityType, $entityId, $entityName, $error, $userId = null, $schoolId = null)
    {
        $this->auditLogModel->logAction([
            'school_id' => $schoolId,
            'user_id' => $userId,
            'action' => $action,
            'entity_type' => $entityType,
            'entity_id' => $entityId,
            'entity_name' => $entityName,
            'description' => "Failed to {$action} {$entityType}: {$entityName} - Error: {$error}",
            'severity' => 'high',
            'status' => 'failed'
        ]);
    }

    /**
     * Log user activity
     */
    public function logActivity($activityType, $description, $userId = null, $schoolId = null, $url = null)
    {
        $this->activityLogModel->logActivity([
            'school_id' => $schoolId,
            'user_id' => $userId,
            'activity_type' => $activityType,
            'activity_description' => $description,
            'url' => $url
        ]);
    }

    /**
     * Log security event
     */
    public function logSecurityEvent($event, $description, $userId = null, $schoolId = null, $severity = 'high')
    {
        $this->auditLogModel->logAction([
            'school_id' => $schoolId,
            'user_id' => $userId,
            'action' => 'security_event',
            'entity_type' => 'security',
            'description' => "Security Event - {$event}: {$description}",
            'severity' => $severity,
            'status' => 'warning'
        ]);
    }

    /**
     * Get severity level based on action and entity type
     */
    private function getSeverityForAction($action, $entityType)
    {
        $criticalEntities = ['user', 'school', 'subscription', 'payment'];
        $highRiskActions = ['delete', 'approve', 'reject'];

        if (in_array($entityType, $criticalEntities) && in_array($action, $highRiskActions)) {
            return 'high';
        }

        if (in_array($entityType, $criticalEntities) || in_array($action, $highRiskActions)) {
            return 'medium';
        }

        return 'low';
    }

    /**
     * Batch log multiple actions (for bulk operations)
     */
    public function logBatchAction($action, $entityType, $entities, $userId = null, $schoolId = null)
    {
        $entityCount = count($entities);
        $entityNames = array_column($entities, 'name');
        $description = "Batch {$action} on {$entityCount} {$entityType}(s): " . implode(', ', array_slice($entityNames, 0, 5));
        
        if ($entityCount > 5) {
            $description .= " and " . ($entityCount - 5) . " more";
        }

        $this->auditLogModel->logAction([
            'school_id' => $schoolId,
            'user_id' => $userId,
            'action' => "batch_{$action}",
            'entity_type' => $entityType,
            'description' => $description,
            'new_values' => ['entity_count' => $entityCount, 'entity_ids' => array_column($entities, 'id')],
            'severity' => $entityCount > 10 ? 'high' : 'medium',
            'status' => 'success'
        ]);
    }

    /**
     * Log system event
     */
    public function logSystemEvent($event, $description, $severity = 'low')
    {
        $this->auditLogModel->logAction([
            'action' => 'system_event',
            'entity_type' => 'system',
            'description' => "System Event - {$event}: {$description}",
            'severity' => $severity,
            'status' => 'success'
        ]);
    }
}
