{"url": "http://localhost:8080/index.php/admin/schools/all", "method": "GET", "isAJAX": false, "startTime": **********.483269, "totalTime": 107.7, "totalMemory": "7.218", "segmentDuration": 20, "segmentCount": 6, "CI_VERSION": "4.6.1", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.491918, "duration": 0.*****************}, {"name": "Required Before Filters", "component": "Timer", "start": **********.530422, "duration": 0.005060911178588867}, {"name": "Routing", "component": "Timer", "start": **********.535492, "duration": 0.0029671192169189453}, {"name": "Before Filters", "component": "Timer", "start": **********.538732, "duration": 0.010382890701293945}, {"name": "Controller", "component": "Timer", "start": **********.549121, "duration": 0.*****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.549123, "duration": 0.007673025131225586}, {"name": "After Filters", "component": "Timer", "start": **********.590448, "duration": 1.1205673217773438e-05}, {"name": "Required After Filters", "component": "Timer", "start": **********.590481, "duration": 0.0005168914794921875}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(1 total Query, 1  unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "0.5 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `schools`\n<strong>WHERE</strong> `schools`.`deleted_at` <strong>IS</strong> <strong>NULL</strong>\n<strong>ORDER</strong> <strong>BY</strong> `created_at` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\SchoolController.php:122", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\SchoolController->all()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php"], "function": "        require_once()", "index": " 11    "}], "trace-file": "APPPATH\\Controllers\\SchoolController.php:122", "qid": "304f6d5f2b98dfb2e356514a7f769ffc"}]}, "badgeValue": 1, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.571614, "duration": "0.014533"}, {"name": "Query", "component": "Database", "start": **********.587173, "duration": "0.000499", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `schools`\n<strong>WHERE</strong> `schools`.`deleted_at` <strong>IS</strong> <strong>NULL</strong>\n<strong>ORDER</strong> <strong>BY</strong> `created_at` <strong>DESC</strong>"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": [{"level": "debug", "msg": "Session: Class initialized using 'CodeIgniter\\Session\\Handlers\\FileHandler' driver."}, {"level": "warning", "msg": "[DEPRECATED] Creation of dynamic property App\\Controllers\\SchoolController::$session is deprecated in APPPATH\\Controllers\\BaseController.php on line 37.\n 1 SYSTEMPATH\\CodeIgniter.php(904): App\\Controllers\\BaseController->initController(Object(CodeIgniter\\HTTP\\IncomingRequest), Object(CodeIgniter\\HTTP\\Response), Object(CodeIgniter\\Log\\Logger))\n 2 SYSTEMPATH\\CodeIgniter.php(498): CodeIgniter\\CodeIgniter->createController()\n 3 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 4 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n 5 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n 6 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))\n 7 SYSTEMPATH\\rewrite.php(44): require_once('C:\\\\xampp\\\\htdocs\\\\schoolquestionbank\\\\school\\\\public\\\\index.php')"}, {"level": "warning", "msg": "[DEPRECATED] Creation of dynamic property CodeIgniter\\HTTP\\IncomingRequest::$school_id is deprecated in APPPATH\\Controllers\\BaseController.php on line 43.\n 1 SYSTEMPATH\\CodeIgniter.php(904): App\\Controllers\\BaseController->initController(Object(CodeIgniter\\HTTP\\IncomingRequest), Object(CodeIgniter\\HTTP\\Response), Object(CodeIgniter\\Log\\Logger))\n 2 SYSTEMPATH\\CodeIgniter.php(498): CodeIgniter\\CodeIgniter->createController()\n 3 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 4 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n 5 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n 6 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))\n 7 SYSTEMPATH\\rewrite.php(44): require_once('C:\\\\xampp\\\\htdocs\\\\schoolquestionbank\\\\school\\\\public\\\\index.php')"}]}, "badgeValue": null, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 0, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": []}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 169 )", "display": {"coreFiles": [{"path": "SYSTEMPATH\\API\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\Autoloader\\Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH\\BaseModel.php", "name": "BaseModel.php"}, {"path": "SYSTEMPATH\\Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH\\Cache\\CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH\\Cache\\CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Cache\\ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH\\CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH\\Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH\\Config\\AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH\\Config\\DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH\\Config\\Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH\\Config\\Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH\\Config\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH\\Cookie\\CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH\\Database\\BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "SYSTEMPATH\\Database\\BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH\\Database\\BaseResult.php", "name": "BaseResult.php"}, {"path": "SYSTEMPATH\\Database\\Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH\\Database\\ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH\\Database\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Builder.php", "name": "Builder.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Result.php", "name": "Result.php"}, {"path": "SYSTEMPATH\\Database\\Query.php", "name": "Query.php"}, {"path": "SYSTEMPATH\\Database\\QueryInterface.php", "name": "QueryInterface.php"}, {"path": "SYSTEMPATH\\Database\\ResultInterface.php", "name": "ResultInterface.php"}, {"path": "SYSTEMPATH\\Debug\\Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH\\Debug\\Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH\\Events\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Filters\\DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH\\Filters\\FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH\\Filters\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Filters\\ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH\\Filters\\PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH\\Filters\\PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH\\Format\\Format.php", "name": "Format.php"}, {"path": "SYSTEMPATH\\Format\\FormatterInterface.php", "name": "FormatterInterface.php"}, {"path": "SYSTEMPATH\\Format\\JSONFormatter.php", "name": "JSONFormatter.php"}, {"path": "SYSTEMPATH\\HTTP\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH\\HTTP\\Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH\\HTTP\\IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH\\HTTP\\URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH\\HTTP\\UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH\\Helpers\\array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\filesystem_helper.php", "name": "filesystem_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\form_helper.php", "name": "form_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH\\I18n\\Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH\\I18n\\TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\HandlerInterface.php", "name": "HandlerInterface.php"}, {"path": "SYSTEMPATH\\Log\\Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH\\Model.php", "name": "Model.php"}, {"path": "SYSTEMPATH\\Modules\\Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH\\Router\\Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH\\Router\\RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Session\\Session.php", "name": "Session.php"}, {"path": "SYSTEMPATH\\Session\\SessionInterface.php", "name": "SessionInterface.php"}, {"path": "SYSTEMPATH\\Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init.php", "name": "init.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH\\Traits\\ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "SYSTEMPATH\\Validation\\FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH\\View\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\View\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\View\\ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}, {"path": "SYSTEMPATH\\rewrite.php", "name": "rewrite.php"}], "userFiles": [{"path": "APPPATH\\Common.php", "name": "Common.php"}, {"path": "APPPATH\\Config\\App.php", "name": "App.php"}, {"path": "APPPATH\\Config\\Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH\\Config\\Boot\\development.php", "name": "development.php"}, {"path": "APPPATH\\Config\\Cache.php", "name": "Cache.php"}, {"path": "APPPATH\\Config\\Constants.php", "name": "Constants.php"}, {"path": "APPPATH\\Config\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH\\Config\\Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH\\Config\\Database.php", "name": "Database.php"}, {"path": "APPPATH\\Config\\Events.php", "name": "Events.php"}, {"path": "APPPATH\\Config\\Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH\\Config\\Feature.php", "name": "Feature.php"}, {"path": "APPPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "APPPATH\\Config\\Format.php", "name": "Format.php"}, {"path": "APPPATH\\Config\\Kint.php", "name": "Kint.php"}, {"path": "APPPATH\\Config\\Logger.php", "name": "Logger.php"}, {"path": "APPPATH\\Config\\Modules.php", "name": "Modules.php"}, {"path": "APPPATH\\Config\\Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH\\Config\\Paths.php", "name": "Paths.php"}, {"path": "APPPATH\\Config\\Routes.php", "name": "Routes.php"}, {"path": "APPPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "APPPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "APPPATH\\Config\\Session.php", "name": "Session.php"}, {"path": "APPPATH\\Config\\Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH\\Config\\UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH\\Config\\View.php", "name": "View.php"}, {"path": "APPPATH\\Controllers\\BaseController.php", "name": "BaseController.php"}, {"path": "APPPATH\\Controllers\\SchoolController.php", "name": "SchoolController.php"}, {"path": "APPPATH\\Filters\\AuthFilter.php", "name": "AuthFilter.php"}, {"path": "APPPATH\\Models\\SchoolModel.php", "name": "SchoolModel.php"}, {"path": "FCPATH\\index.php", "name": "index.php"}, {"path": "VENDORPATH\\autoload.php", "name": "autoload.php"}, {"path": "VENDORPATH\\composer\\ClassLoader.php", "name": "ClassLoader.php"}, {"path": "VENDORPATH\\composer\\InstalledVersions.php", "name": "InstalledVersions.php"}, {"path": "VENDORPATH\\composer\\autoload_real.php", "name": "autoload_real.php"}, {"path": "VENDORPATH\\composer\\autoload_static.php", "name": "autoload_static.php"}, {"path": "VENDORPATH\\composer\\installed.php", "name": "installed.php"}, {"path": "VENDORPATH\\composer\\platform_check.php", "name": "platform_check.php"}, {"path": "VENDORPATH\\laminas\\laminas-escaper\\src\\Escaper.php", "name": "Escaper.php"}, {"path": "VENDORPATH\\laminas\\laminas-escaper\\src\\EscaperInterface.php", "name": "EscaperInterface.php"}, {"path": "VENDORPATH\\myclabs\\deep-copy\\src\\DeepCopy\\deep_copy.php", "name": "deep_copy.php"}, {"path": "VENDORPATH\\phpunit\\phpunit\\src\\Framework\\Assert\\Functions.php", "name": "Functions.php"}, {"path": "VENDORPATH\\psr\\log\\src\\LogLevel.php", "name": "LogLevel.php"}, {"path": "VENDORPATH\\psr\\log\\src\\LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "VENDORPATH\\psr\\log\\src\\LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "VENDORPATH\\razorpay\\razorpay\\Deprecated.php", "name": "Deprecated.php"}, {"path": "VENDORPATH\\rmccue\\requests\\library\\Deprecated.php", "name": "Deprecated.php"}, {"path": "VENDORPATH\\rmccue\\requests\\src\\Autoload.php", "name": "Autoload.php"}, {"path": "VENDORPATH\\symfony\\deprecation-contracts\\function.php", "name": "function.php"}]}, "badgeValue": 169, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\SchoolController", "method": "all", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Landing::index"}, {"method": "GET", "route": "register", "handler": "\\App\\Controllers\\Landing::register"}, {"method": "GET", "route": "login", "handler": "\\App\\Controllers\\Auth::schooladminLogin"}, {"method": "GET", "route": "login/superadmin", "handler": "\\App\\Controllers\\Auth::superadminLogin"}, {"method": "GET", "route": "login/schooladmin", "handler": "\\App\\Controllers\\Auth::schooladminLogin"}, {"method": "GET", "route": "logout", "handler": "\\App\\Controllers\\Auth::logout"}, {"method": "GET", "route": "auth/logout", "handler": "\\App\\Controllers\\Auth::logout"}, {"method": "GET", "route": "superadmin/dashboard", "handler": "\\App\\Controllers\\SuperAdmin::dashboard"}, {"method": "GET", "route": "schooladmin/dashboard", "handler": "\\App\\Controllers\\SchoolAdmin::dashboard"}, {"method": "GET", "route": "schooladmin/question-papers", "handler": "\\App\\Controllers\\SchoolAdmin::questionPapers"}, {"method": "GET", "route": "subscription/plans", "handler": "\\App\\Controllers\\Subscription::plans"}, {"method": "GET", "route": "subscription/current", "handler": "\\App\\Controllers\\Subscription::current"}, {"method": "GET", "route": "subscription/upgrade", "handler": "\\App\\Controllers\\Subscription::upgrade"}, {"method": "GET", "route": "subscription/usage", "handler": "\\App\\Controllers\\Subscription::getUsage"}, {"method": "GET", "route": "payment/process", "handler": "\\App\\Controllers\\Payment::process"}, {"method": "GET", "route": "payment/success", "handler": "\\App\\Controllers\\Payment::success"}, {"method": "GET", "route": "payment/failure", "handler": "\\App\\Controllers\\Payment::failure"}, {"method": "GET", "route": "payment/history", "handler": "\\App\\Controllers\\Payment::history"}, {"method": "GET", "route": "payment/transaction-details/([0-9]+)", "handler": "\\App\\Controllers\\Payment::transactionDetails/$1"}, {"method": "GET", "route": "payment/invoice/([0-9]+)", "handler": "\\App\\Controllers\\Payment::invoice/$1"}, {"method": "GET", "route": "payment/test-webhook/([^/]+)", "handler": "\\App\\Controllers\\Payment::testWebhook/$1"}, {"method": "GET", "route": "payment/debugCompletePending", "handler": "\\App\\Controllers\\Payment::debugCompletePending"}, {"method": "GET", "route": "payment/debugSession", "handler": "\\App\\Controllers\\Payment::debugSession"}, {"method": "GET", "route": "superadmin/school/([0-9]+)/details", "handler": "\\App\\Controllers\\SuperAdmin::viewSchoolDetails/$1"}, {"method": "GET", "route": "superadmin/getUsers", "handler": "\\App\\Controllers\\SuperAdmin::getUsers"}, {"method": "GET", "route": "superadmin/getUserDetails/([0-9]+)", "handler": "\\App\\Controllers\\SuperAdmin::getUserDetails/$1"}, {"method": "GET", "route": "superadmin/getAuditLogs", "handler": "\\App\\Controllers\\SuperAdmin::getAuditLogs"}, {"method": "GET", "route": "superadmin/getAuditStats", "handler": "\\App\\Controllers\\SuperAdmin::getAuditStats"}, {"method": "GET", "route": "superadmin/getSuperAdminLogs", "handler": "\\App\\Controllers\\SuperAdmin::getSuperAdminLogs"}, {"method": "GET", "route": "superadmin/exportAuditLogs", "handler": "\\App\\Controllers\\SuperAdmin::exportAuditLogs"}, {"method": "GET", "route": "superadmin/getAuditFilterOptions", "handler": "\\App\\Controllers\\SuperAdmin::getAuditFilterOptions"}, {"method": "GET", "route": "admin/schools/pending", "handler": "\\App\\Controllers\\SchoolController::pending"}, {"method": "GET", "route": "admin/schools/active", "handler": "\\App\\Controllers\\SchoolController::active"}, {"method": "GET", "route": "admin/schools/all", "handler": "\\App\\Controllers\\SchoolController::all"}, {"method": "GET", "route": "schooladmin/getUsers", "handler": "\\App\\Controllers\\SchoolAdmin::getUsers"}, {"method": "GET", "route": "schooladmin/getStaff", "handler": "\\App\\Controllers\\SchoolAdmin::getStaff"}, {"method": "GET", "route": "schooladmin/getDeletedUsers", "handler": "\\App\\Controllers\\SchoolAdmin::getDeletedUsers"}, {"method": "GET", "route": "schooladmin/getUserDetails/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::getUserDetails/$1"}, {"method": "GET", "route": "schooladmin/getStaffMember/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::getStaffMember/$1"}, {"method": "GET", "route": "schooladmin/testSoftDelete", "handler": "\\App\\Controllers\\SchoolAdmin::testSoftDelete"}, {"method": "GET", "route": "schooladmin/settings", "handler": "\\App\\Controllers\\SchoolAdmin::settings"}, {"method": "GET", "route": "schooladmin/getSchoolSettings", "handler": "\\App\\Controllers\\SchoolAdmin::getSchoolSettings"}, {"method": "GET", "route": "schooladmin/getQuestionsForReview", "handler": "\\App\\Controllers\\SchoolAdmin::getQuestionsForReview"}, {"method": "GET", "route": "schooladmin/getQuestionDetails/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::getQuestionDetails/$1"}, {"method": "GET", "route": "schooladmin/getApprovedQuestions", "handler": "\\App\\Controllers\\SchoolAdmin::getApprovedQuestions"}, {"method": "GET", "route": "schooladmin/getQuestionPapers", "handler": "\\App\\Controllers\\SchoolAdmin::getQuestionPapers"}, {"method": "GET", "route": "schooladmin/getQuestionPaper/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::getQuestionPaper/$1"}, {"method": "GET", "route": "schooladmin/testSession", "handler": "\\App\\Controllers\\SchoolAdmin::testSession"}, {"method": "GET", "route": "schooladmin/downloadQuestionPaperPDF/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::downloadQuestionPaperPDF/$1"}, {"method": "GET", "route": "schooladmin/getQuestionPaperForEdit/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::getQuestionPaperForEdit/$1"}, {"method": "GET", "route": "staff/login", "handler": "\\App\\Controllers\\Staff::login"}, {"method": "GET", "route": "staff/dashboard", "handler": "\\App\\Controllers\\Staff::dashboard"}, {"method": "GET", "route": "staff/profile", "handler": "\\App\\Controllers\\Staff::profile"}, {"method": "GET", "route": "staff/logout", "handler": "\\App\\Controllers\\Auth::logout"}, {"method": "GET", "route": "staff/getSubjects", "handler": "\\App\\Controllers\\Staff::getSubjects"}, {"method": "GET", "route": "staff/getQuestions", "handler": "\\App\\Controllers\\Staff::getQuestions"}, {"method": "GET", "route": "staff/getQuestionDetails/([0-9]+)", "handler": "\\App\\Controllers\\Staff::getQuestionDetails/$1"}, {"method": "GET", "route": "staff/getSubjectsWithCounts", "handler": "\\App\\Controllers\\Staff::getSubjectsWithCounts"}, {"method": "GET", "route": "staff/getQuestionsBySubject/([^/]+)", "handler": "\\App\\Controllers\\Staff::getQuestionsBySubject/$1"}, {"method": "GET", "route": "staff/getDetailedReports", "handler": "\\App\\Controllers\\Staff::getDetailedReports"}, {"method": "GET", "route": "staff/getRecentActivities", "handler": "\\App\\Controllers\\Staff::getRecentActivities"}, {"method": "GET", "route": "staff/testUpdate/([0-9]+)", "handler": "\\App\\Controllers\\Staff::testUpdate/$1"}, {"method": "POST", "route": "school/register", "handler": "\\App\\Controllers\\SchoolController::register"}, {"method": "POST", "route": "register/send-otp", "handler": "\\App\\Controllers\\Register::sendOtp"}, {"method": "POST", "route": "register/verify-otp", "handler": "\\App\\Controllers\\Register::verifyOtp"}, {"method": "POST", "route": "auth/login", "handler": "\\App\\Controllers\\Auth::login"}, {"method": "POST", "route": "subscription/select-plan", "handler": "\\App\\Controllers\\Subscription::selectPlan"}, {"method": "POST", "route": "subscription/upgrade", "handler": "\\App\\Controllers\\Subscription::processUpgrade"}, {"method": "POST", "route": "subscription/cancel", "handler": "\\App\\Controllers\\Subscription::cancel"}, {"method": "POST", "route": "payment/initiate", "handler": "\\App\\Controllers\\Payment::initiate"}, {"method": "POST", "route": "payment/verify", "handler": "\\App\\Controllers\\Payment::verify"}, {"method": "POST", "route": "payment/webhook/([^/]+)", "handler": "\\App\\Controllers\\Payment::webhook/$1"}, {"method": "POST", "route": "superadmin/approve/([0-9]+)", "handler": "\\App\\Controllers\\SuperAdmin::approveSchool/$1"}, {"method": "POST", "route": "superadmin/reject/([0-9]+)", "handler": "\\App\\Controllers\\SuperAdmin::rejectSchool/$1"}, {"method": "POST", "route": "superadmin/toggleUserStatus/([0-9]+)", "handler": "\\App\\Controllers\\SuperAdmin::toggleUserStatus/$1"}, {"method": "POST", "route": "admin/schools/approve/([0-9]+)", "handler": "\\App\\Controllers\\SchoolController::approve/$1"}, {"method": "POST", "route": "admin/schools/superadmin/reject/([0-9]+)", "handler": "\\App\\Controllers\\SuperAdmin::rejectSchool/$1"}, {"method": "POST", "route": "schooladmin/addUser", "handler": "\\App\\Controllers\\SchoolAdmin::addUser"}, {"method": "POST", "route": "schooladmin/deleteUser/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::deleteUser/$1"}, {"method": "POST", "route": "schooladmin/restoreUser/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::restoreUser/$1"}, {"method": "POST", "route": "schooladmin/updateStaffMember/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::updateStaffMember/$1"}, {"method": "POST", "route": "schooladmin/toggleStaffStatus/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::toggleStaffStatus/$1"}, {"method": "POST", "route": "schooladmin/updateSchoolProfile", "handler": "\\App\\Controllers\\SchoolAdmin::updateSchoolProfile"}, {"method": "POST", "route": "schooladmin/saveAllSettings", "handler": "\\App\\Controllers\\SchoolAdmin::saveAllSettings"}, {"method": "POST", "route": "schooladmin/approveQuestion/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::approveQuestion/$1"}, {"method": "POST", "route": "schooladmin/rejectQuestion/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::rejectQuestion/$1"}, {"method": "POST", "route": "schooladmin/createQuestionPaper", "handler": "\\App\\Controllers\\SchoolAdmin::createQuestionPaper"}, {"method": "POST", "route": "schooladmin/downloadQuestionPaperPDF", "handler": "\\App\\Controllers\\SchoolAdmin::downloadQuestionPaperPDF"}, {"method": "POST", "route": "staff/authenticate", "handler": "\\App\\Controllers\\Staff::authenticate"}, {"method": "POST", "route": "staff/updateProfile", "handler": "\\App\\Controllers\\Staff::updateProfile"}, {"method": "POST", "route": "staff/createQuestion", "handler": "\\App\\Controllers\\Staff::createQuestion"}, {"method": "POST", "route": "staff/saveDraft", "handler": "\\App\\Controllers\\Staff::saveDraft"}, {"method": "POST", "route": "staff/updateQuestion/([0-9]+)", "handler": "\\App\\Controllers\\Staff::updateQuestion/$1"}, {"method": "POST", "route": "school/check-email", "handler": "\\App\\Controllers\\SchoolController::checkEmail"}, {"method": "PUT", "route": "schooladmin/updateQuestionPaper/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::updateQuestionPaper/$1"}, {"method": "PUT", "route": "staff/updateQuestion/([0-9]+)", "handler": "\\App\\Controllers\\Staff::updateQuestion/$1"}, {"method": "DELETE", "route": "superadmin/deleteUser/([0-9]+)", "handler": "\\App\\Controllers\\SuperAdmin::deleteUser/$1"}, {"method": "DELETE", "route": "schooladmin/deleteStaffMember/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::deleteStaffMember/$1"}, {"method": "DELETE", "route": "schooladmin/deleteQuestionPaper/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::deleteQuestionPaper/$1"}, {"method": "DELETE", "route": "staff/deleteQuestion/([0-9]+)", "handler": "\\App\\Controllers\\Staff::deleteQuestion/$1"}]}, "badgeValue": 63, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "16.04", "count": 1}, "DBQuery": {"event": "DB<PERSON>uery", "duration": "0.03", "count": 1}}}, "badgeValue": 2, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.51437, "duration": 0.016039133071899414}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.587678, "duration": 3.314018249511719e-05}]}], "vars": {"varData": {"View Data": []}, "session": {"_ci_previous_url": "http://localhost:8080/index.php/superadmin/dashboard", "role": "superadmin", "isSuperAdmin": "<pre>1</pre>", "email": "<EMAIL>", "logged_in": "<pre>1</pre>", "user_id": "<pre>1</pre>"}, "headers": {"Host": "localhost:8080", "Connection": "keep-alive", "Sec-Ch-Ua-Platform": "&quot;Windows&quot;", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Sec-Ch-Ua": "&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;", "Sec-Ch-Ua-Mobile": "?0", "Accept": "*/*", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:8080/index.php/superadmin/dashboard", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "en-US,en;q=0.9", "Cookie": "csrf_cookie_name=d7c532fc44f5b82847f4efbd43afe67a; ci_session=f8e3280a465bfc5e51f03340dce2a84d"}, "cookies": {"csrf_cookie_name": "d7c532fc44f5b82847f4efbd43afe67a", "ci_session": "f8e3280a465bfc5e51f03340dce2a84d"}, "request": "HTTP/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "application/json; charset=UTF-8", "headers": {"Content-Type": "application/json; charset=UTF-8"}}}, "config": {"ciVersion": "4.6.1", "phpVersion": "8.2.12", "phpSAPI": "cli-server", "environment": "development", "baseURL": "http://localhost:8080/", "timezone": "UTC", "locale": "en", "cspEnabled": false}}