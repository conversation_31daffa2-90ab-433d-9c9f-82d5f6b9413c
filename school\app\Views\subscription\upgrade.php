<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upgrade Your Plan - School Question Bank</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .plan-card {
            transition: all 0.3s ease;
        }
        .plan-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .current-plan {
            border: 2px solid #10b981;
            background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
        }
        .recommended-plan {
            border: 2px solid #3b82f6;
            background: linear-gradient(135deg, #eff6ff 0%, #f0f9ff 100%);
        }
        .upgrade-animation {
            animation: upgradeGlow 2s ease-in-out infinite;
        }
        @keyframes upgradeGlow {
            0%, 100% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.5); }
            50% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.8); }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">

    <!-- Header -->
    <div class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <a href="<?= base_url('subscription/current') ?>" class="text-gray-500 hover:text-gray-700 mr-4">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <h1 class="text-2xl font-bold text-gray-900">Upgrade Your Plan</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="<?= base_url('schooladmin/dashboard') ?>" class="text-gray-600 hover:text-gray-800">
                        <i class="fas fa-home mr-1"></i>Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <!-- Current Plan Info -->
        <?php if (isset($current_subscription)): ?>
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-semibold text-gray-900 mb-2">Current Plan</h2>
                    <div class="flex items-center space-x-4">
                        <span class="text-2xl font-bold text-green-600"><?= $current_subscription['plan_display_name'] ?></span>
                        <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                            <?= ucfirst($current_subscription['status']) ?>
                        </span>
                        <span class="text-gray-500 capitalize"><?= $current_subscription['billing_cycle'] ?> billing</span>
                    </div>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-500">Next billing date</p>
                    <p class="text-lg font-semibold text-gray-900">
                        <?= $current_subscription['expires_at'] ? date('M d, Y', strtotime($current_subscription['expires_at'])) : 'N/A' ?>
                    </p>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Upgrade Benefits -->
        <div class="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl shadow-lg p-8 mb-8 text-white">
            <div class="text-center">
                <h2 class="text-3xl font-bold mb-4">Why Upgrade?</h2>
                <p class="text-xl mb-6 opacity-90">Unlock more features and grow your school's potential</p>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-infinity text-2xl"></i>
                        </div>
                        <h3 class="font-semibold mb-2">Unlimited Questions</h3>
                        <p class="text-sm opacity-80">Create unlimited questions for all subjects</p>
                    </div>
                    
                    <div class="text-center">
                        <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-users text-2xl"></i>
                        </div>
                        <h3 class="font-semibold mb-2">More Staff Access</h3>
                        <p class="text-sm opacity-80">Add unlimited staff members</p>
                    </div>
                    
                    <div class="text-center">
                        <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-headset text-2xl"></i>
                        </div>
                        <h3 class="font-semibold mb-2">Priority Support</h3>
                        <p class="text-sm opacity-80">Get priority customer support</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Plan Comparison -->
        <?php
        // Count visible plans
        $visiblePlans = [];
        if (isset($plans) && !empty($plans)) {
            foreach ($plans as $plan) {
                $currentPlanName = isset($current_subscription) ? $current_subscription['plan_name'] : '';
                $shouldHidePlan = ($currentPlanName === 'professional' && $plan['name'] === 'free');
                if (!$shouldHidePlan) {
                    $visiblePlans[] = $plan;
                }
            }
        }
        $gridClass = count($visiblePlans) === 1 ? 'grid-cols-1 max-w-md mx-auto' : 'grid-cols-1 lg:grid-cols-2';
        ?>
        <div class="grid <?= $gridClass ?> gap-8 mb-8">
            <?php if (!empty($visiblePlans)): ?>
                <?php foreach ($visiblePlans as $plan): ?>
                    <?php
                    $isCurrent = isset($current_subscription) && $current_subscription['plan_id'] == $plan['id'];
                    $isRecommended = $plan['name'] === 'professional';
                    ?>
                    
                    <div class="plan-card bg-white rounded-2xl shadow-lg overflow-hidden <?= $isCurrent ? 'current-plan' : ($isRecommended ? 'recommended-plan upgrade-animation' : '') ?>">
                        
                        <!-- Plan Header -->
                        <div class="p-8 <?= $isCurrent ? 'bg-green-50' : ($isRecommended ? 'bg-blue-50' : 'bg-gray-50') ?>">
                            <div class="flex justify-between items-start mb-4">
                                <div>
                                    <h3 class="text-2xl font-bold text-gray-900"><?= $plan['display_name'] ?></h3>
                                    <p class="text-gray-600 mt-1"><?= $plan['description'] ?></p>
                                </div>
                                
                                <?php if ($isCurrent): ?>
                                    <span class="px-3 py-1 bg-green-500 text-white rounded-full text-sm font-medium">
                                        Current Plan
                                    </span>
                                <?php elseif ($isRecommended): ?>
                                    <span class="px-3 py-1 bg-blue-500 text-white rounded-full text-sm font-medium">
                                        Recommended
                                    </span>
                                <?php endif; ?>
                            </div>
                            
                            <!-- Pricing -->
                            <div class="mb-6">
                                <?php if ($plan['price_monthly'] > 0): ?>
                                    <div class="flex items-baseline space-x-2">
                                        <span class="text-4xl font-bold text-gray-900">₹<?= number_format($plan['price_monthly']) ?></span>
                                        <span class="text-gray-500">/month</span>
                                    </div>
                                    <?php if ($plan['price_yearly'] > 0): ?>
                                        <div class="mt-2">
                                            <span class="text-lg text-gray-700">₹<?= number_format($plan['price_yearly']) ?>/year</span>
                                            <span class="ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                                                Save <?= round((1 - ($plan['price_yearly'] / ($plan['price_monthly'] * 12))) * 100) ?>%
                                            </span>
                                        </div>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <div class="flex items-baseline space-x-2">
                                        <span class="text-4xl font-bold text-green-600">Free</span>
                                        <span class="text-gray-500">for 14 days</span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Features -->
                        <div class="p-8">
                            <h4 class="font-semibold text-gray-900 mb-4">Features included:</h4>
                            
                            <?php if (isset($plan['features']) && !empty($plan['features'])): ?>
                                <ul class="space-y-3 mb-8">
                                    <?php foreach ($plan['features'] as $feature): ?>
                                        <li class="flex items-center">
                                            <i class="fas fa-check text-green-500 mr-3"></i>
                                            <span class="text-gray-700"><?= $feature['feature_name'] ?? 'Feature' ?></span>
                                            <?php
                                            // Show feature value/limit if available and not unlimited
                                            if (isset($feature['is_unlimited']) && $feature['is_unlimited']): ?>
                                                <span class="ml-2 text-sm text-gray-500">(Unlimited)</span>
                                            <?php elseif (isset($feature['feature_value']) && $feature['feature_value'] && $feature['feature_value'] !== '1'): ?>
                                                <span class="ml-2 text-sm text-gray-500">(<?= $feature['feature_value'] ?>)</span>
                                            <?php endif; ?>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            <?php endif; ?>

                            <!-- Action Button -->
                            <?php if ($isCurrent): ?>
                                <button disabled class="w-full bg-gray-300 text-gray-500 py-3 px-6 rounded-lg font-semibold cursor-not-allowed">
                                    <i class="fas fa-check mr-2"></i>Current Plan
                                </button>
                            <?php else: ?>
                                <div class="space-y-3">
                                    <?php if ($plan['price_monthly'] > 0): ?>
                                        <!-- Monthly Upgrade -->
                                        <button onclick="upgradeToplan(<?= $plan['id'] ?>, 'monthly', <?= $plan['price_monthly'] ?>)" 
                                                class="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition duration-300">
                                            <i class="fas fa-arrow-up mr-2"></i>Upgrade Monthly - ₹<?= number_format($plan['price_monthly']) ?>
                                        </button>
                                        
                                        <?php if ($plan['price_yearly'] > 0): ?>
                                        <!-- Yearly Upgrade -->
                                        <button onclick="upgradeToplan(<?= $plan['id'] ?>, 'yearly', <?= $plan['price_yearly'] ?>)" 
                                                class="w-full bg-green-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-green-700 transition duration-300">
                                            <i class="fas fa-calendar-alt mr-2"></i>Upgrade Yearly - ₹<?= number_format($plan['price_yearly']) ?>
                                            <span class="text-xs block">Save <?= round((1 - ($plan['price_yearly'] / ($plan['price_monthly'] * 12))) * 100) ?>%</span>
                                        </button>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <button onclick="upgradeToplan(<?= $plan['id'] ?>, 'monthly', 0)" 
                                                class="w-full bg-green-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-green-700 transition duration-300">
                                            <i class="fas fa-gift mr-2"></i>Start Free Trial
                                        </button>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="text-center py-8">
                    <div class="bg-green-50 border border-green-200 rounded-lg p-6 max-w-lg mx-auto">
                        <div class="flex items-center justify-center mb-4">
                            <i class="fas fa-crown text-green-600 text-3xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-green-800 mb-2">You're on the Best Plan!</h3>
                        <p class="text-green-600 mb-6">You're currently subscribed to our highest tier plan with all premium features unlocked.</p>

                        <div class="flex flex-col sm:flex-row gap-3 justify-center">
                            <a href="<?= base_url() ?>/subscription/current" class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition duration-300">
                                <i class="fas fa-eye mr-2"></i>View Current Plan
                            </a>
                            <a href="<?= base_url() ?>/subscription/billing" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-300">
                                <i class="fas fa-credit-card mr-2"></i>Manage Billing
                            </a>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- FAQ Section -->
        <div class="bg-white rounded-xl shadow-lg p-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6 text-center">Frequently Asked Questions</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-semibold text-gray-900 mb-2">Can I downgrade later?</h3>
                    <p class="text-gray-600 text-sm">Yes, you can downgrade at any time. Changes will take effect at the end of your current billing cycle.</p>
                </div>
                
                <div>
                    <h3 class="font-semibold text-gray-900 mb-2">What happens to my data?</h3>
                    <p class="text-gray-600 text-sm">All your data remains safe. If you downgrade, some features may be limited but data is never lost.</p>
                </div>
                
                <div>
                    <h3 class="font-semibold text-gray-900 mb-2">Is there a setup fee?</h3>
                    <p class="text-gray-600 text-sm">No setup fees. You only pay the plan price. Cancel anytime without penalties.</p>
                </div>
                
                <div>
                    <h3 class="font-semibold text-gray-900 mb-2">Do you offer refunds?</h3>
                    <p class="text-gray-600 text-sm">Yes, we offer a 30-day money-back guarantee for all paid plans.</p>
                </div>
            </div>
        </div>

    </div>

    <!-- Upgrade Confirmation Modal -->
    <div id="upgradeModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg max-w-md w-full mx-4">
            <div class="p-6">
                <h3 class="text-lg font-semibold mb-4">Confirm Plan Upgrade</h3>
                <div id="upgradeDetails">
                    <!-- Upgrade details will be populated here -->
                </div>
                <div class="flex space-x-3 mt-6">
                    <button onclick="closeUpgradeModal()" class="flex-1 border border-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-50">
                        Cancel
                    </button>
                    <button onclick="confirmUpgrade()" class="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700">
                        Confirm Upgrade
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedUpgrade = null;

        function upgradeToplan(planId, billingCycle, amount) {
            selectedUpgrade = { planId, billingCycle, amount };
            
            // Show upgrade confirmation modal
            const planName = billingCycle === 'yearly' ? 'Professional (Yearly)' : 'Professional (Monthly)';
            const savings = billingCycle === 'yearly' ? '<div class="text-green-600 text-sm mt-1">You save ₹' + (amount * 12 - amount).toLocaleString() + ' per year!</div>' : '';
            
            document.getElementById('upgradeDetails').innerHTML = `
                <div class="text-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-arrow-up text-2xl text-blue-600"></i>
                    </div>
                    <h4 class="text-lg font-semibold mb-2">Upgrade to ${planName}</h4>
                    <p class="text-gray-600 mb-4">You will be charged ₹${amount.toLocaleString()} ${billingCycle === 'yearly' ? 'per year' : 'per month'}</p>
                    ${savings}
                    <div class="bg-blue-50 rounded-lg p-3 mt-4">
                        <p class="text-sm text-blue-800">Your upgrade will be effective immediately after payment confirmation.</p>
                    </div>
                </div>
            `;
            
            document.getElementById('upgradeModal').classList.remove('hidden');
            document.getElementById('upgradeModal').classList.add('flex');
        }

        function closeUpgradeModal() {
            document.getElementById('upgradeModal').classList.add('hidden');
            document.getElementById('upgradeModal').classList.remove('flex');
            selectedUpgrade = null;
        }

        function confirmUpgrade() {
            if (!selectedUpgrade) return;
            
            // Show loading
            document.getElementById('upgradeDetails').innerHTML = `
                <div class="text-center">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p class="text-gray-600">Processing upgrade...</p>
                </div>
            `;
            
            // Process upgrade
            fetch('<?= base_url('subscription/processUpgrade') ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `plan_id=${selectedUpgrade.planId}&billing_cycle=${selectedUpgrade.billingCycle}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (data.requires_payment) {
                        // Redirect to payment
                        window.location.href = data.redirect + `?plan_id=${selectedUpgrade.planId}&billing_cycle=${selectedUpgrade.billingCycle}`;
                    } else {
                        // Free upgrade successful
                        window.location.href = '<?= base_url('subscription/current') ?>?upgraded=1';
                    }
                } else {
                    alert('Error: ' + data.message);
                    closeUpgradeModal();
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred. Please try again.');
                closeUpgradeModal();
            });
        }

        // Close modal on outside click
        document.getElementById('upgradeModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeUpgradeModal();
            }
        });
    </script>
</body>
</html>
