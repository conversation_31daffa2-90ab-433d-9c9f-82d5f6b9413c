<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class PaymentSeeder extends Seeder
{
    public function run()
    {
        $paymentModel = new \App\Models\PaymentLogModel();
        
        // Get a school ID
        $schoolModel = new \App\Models\SchoolModel();
        $school = $schoolModel->first();
        
        if (!$school) {
            echo "No schools found. Please run SchoolSeeder first.\n";
            return;
        }
        
        // Sample payment data for current month
        $currentMonth = date('Y-m-01');
        $lastMonth = date('Y-m-01', strtotime('-1 month'));
        
        $payments = [
            // Current month payments
            [
                'school_id' => $school['id'],
                'subscription_id' => 1,
                'transaction_id' => 'TXN_' . uniqid(),
                'gateway_transaction_id' => 'RAZORPAY_' . uniqid(),
                'payment_gateway' => 'razorpay',
                'amount' => 2500.00,
                'currency' => 'INR',
                'status' => 'completed',
                'payment_method' => 'card',
                'gateway_response' => json_encode(['status' => 'success']),
                'notes' => 'Monthly subscription payment',
                'processed_at' => date('Y-m-d H:i:s', strtotime($currentMonth . ' +5 days')),
                'created_at' => date('Y-m-d H:i:s', strtotime($currentMonth . ' +5 days')),
                'updated_at' => date('Y-m-d H:i:s', strtotime($currentMonth . ' +5 days'))
            ],
            [
                'school_id' => $school['id'],
                'subscription_id' => 2,
                'transaction_id' => 'TXN_' . uniqid(),
                'gateway_transaction_id' => 'RAZORPAY_' . uniqid(),
                'payment_gateway' => 'razorpay',
                'amount' => 1800.00,
                'currency' => 'INR',
                'status' => 'completed',
                'payment_method' => 'upi',
                'gateway_response' => json_encode(['status' => 'success']),
                'notes' => 'Quarterly subscription payment',
                'processed_at' => date('Y-m-d H:i:s', strtotime($currentMonth . ' +10 days')),
                'created_at' => date('Y-m-d H:i:s', strtotime($currentMonth . ' +10 days')),
                'updated_at' => date('Y-m-d H:i:s', strtotime($currentMonth . ' +10 days'))
            ],
            [
                'school_id' => $school['id'],
                'subscription_id' => 3,
                'transaction_id' => 'TXN_' . uniqid(),
                'gateway_transaction_id' => 'RAZORPAY_' . uniqid(),
                'payment_gateway' => 'razorpay',
                'amount' => 3200.00,
                'currency' => 'INR',
                'status' => 'completed',
                'payment_method' => 'netbanking',
                'gateway_response' => json_encode(['status' => 'success']),
                'notes' => 'Annual subscription payment',
                'processed_at' => date('Y-m-d H:i:s', strtotime($currentMonth . ' +15 days')),
                'created_at' => date('Y-m-d H:i:s', strtotime($currentMonth . ' +15 days')),
                'updated_at' => date('Y-m-d H:i:s', strtotime($currentMonth . ' +15 days'))
            ],
            
            // Last month payments (for growth calculation)
            [
                'school_id' => $school['id'],
                'subscription_id' => 4,
                'transaction_id' => 'TXN_' . uniqid(),
                'gateway_transaction_id' => 'RAZORPAY_' . uniqid(),
                'payment_gateway' => 'razorpay',
                'amount' => 2000.00,
                'currency' => 'INR',
                'status' => 'completed',
                'payment_method' => 'card',
                'gateway_response' => json_encode(['status' => 'success']),
                'notes' => 'Monthly subscription payment',
                'processed_at' => date('Y-m-d H:i:s', strtotime($lastMonth . ' +8 days')),
                'created_at' => date('Y-m-d H:i:s', strtotime($lastMonth . ' +8 days')),
                'updated_at' => date('Y-m-d H:i:s', strtotime($lastMonth . ' +8 days'))
            ],
            [
                'school_id' => $school['id'],
                'subscription_id' => 5,
                'transaction_id' => 'TXN_' . uniqid(),
                'gateway_transaction_id' => 'RAZORPAY_' . uniqid(),
                'payment_gateway' => 'razorpay',
                'amount' => 1500.00,
                'currency' => 'INR',
                'status' => 'completed',
                'payment_method' => 'upi',
                'gateway_response' => json_encode(['status' => 'success']),
                'notes' => 'Quarterly subscription payment',
                'processed_at' => date('Y-m-d H:i:s', strtotime($lastMonth . ' +12 days')),
                'created_at' => date('Y-m-d H:i:s', strtotime($lastMonth . ' +12 days')),
                'updated_at' => date('Y-m-d H:i:s', strtotime($lastMonth . ' +12 days'))
            ]
        ];
        
        foreach ($payments as $payment) {
            $paymentModel->insert($payment);
        }
        
        echo "Created " . count($payments) . " sample payment records successfully!\n";
        echo "Current month revenue: ₹" . number_format(array_sum(array_column(array_slice($payments, 0, 3), 'amount')), 2) . "\n";
        echo "Last month revenue: ₹" . number_format(array_sum(array_column(array_slice($payments, 3, 2), 'amount')), 2) . "\n";
        
        // Calculate growth
        $currentTotal = array_sum(array_column(array_slice($payments, 0, 3), 'amount'));
        $lastTotal = array_sum(array_column(array_slice($payments, 3, 2), 'amount'));
        $growth = round((($currentTotal - $lastTotal) / $lastTotal) * 100, 1);
        echo "Revenue growth: {$growth}%\n";
    }
}
