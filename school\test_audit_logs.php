<?php

require_once 'vendor/autoload.php';

// Initialize CodeIgniter
$app = \Config\Services::codeigniter();
$app->initialize();

// Test audit logging functionality
use App\Services\AuditLogger;
use App\Models\AuditLogModel;

echo "🔍 Testing Audit Logs System\n";
echo "=" . str_repeat("=", 50) . "\n\n";

try {
    // Initialize services
    $auditLogger = new AuditLogger();
    $auditLogModel = new AuditLogModel();
    
    echo "✅ Audit Logger and Model initialized successfully\n\n";
    
    // Test 1: Log a sample action
    echo "📝 Test 1: Logging a sample action...\n";
    $result = $auditLogger->logCreate(
        'test_entity',
        123,
        'Test Entity Name',
        ['name' => 'Test', 'status' => 'active'],
        1,
        1,
        'Test audit log entry'
    );
    
    if ($result) {
        echo "✅ Sample audit log created successfully\n";
    } else {
        echo "❌ Failed to create sample audit log\n";
    }
    
    // Test 2: Retrieve audit logs
    echo "\n📊 Test 2: Retrieving audit logs...\n";
    $logs = $auditLogModel->getAuditLogs([], 1, 5);
    
    echo "✅ Retrieved {$logs['total']} total audit logs\n";
    echo "📄 Showing latest 5 logs:\n";
    
    foreach ($logs['logs'] as $log) {
        echo "  - [{$log['created_at']}] {$log['action']} on {$log['entity_type']} by " . 
             ($log['user_name'] ?? 'System') . "\n";
    }
    
    // Test 3: Get audit statistics
    echo "\n📈 Test 3: Getting audit statistics...\n";
    $stats = $auditLogModel->getAuditStats();
    
    echo "✅ Total actions: {$stats['total_actions']}\n";
    echo "📊 Actions by type:\n";
    foreach ($stats['actions_by_type'] as $action) {
        echo "  - {$action['action']}: {$action['count']}\n";
    }
    
    // Test 4: Test filter options
    echo "\n🔍 Test 4: Getting filter options...\n";
    $actions = $auditLogModel->getDistinctActions();
    $entityTypes = $auditLogModel->getDistinctEntityTypes();
    
    echo "✅ Available actions: " . implode(', ', $actions) . "\n";
    echo "✅ Available entity types: " . implode(', ', $entityTypes) . "\n";
    
    echo "\n🎉 All audit log tests completed successfully!\n";
    echo "🌐 You can now access the SuperAdmin dashboard to view audit logs.\n";
    echo "🔗 URL: " . site_url('superadmin/dashboard') . "\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "📍 File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "✨ Audit Logs System Test Complete ✨\n";
echo str_repeat("=", 60) . "\n";
