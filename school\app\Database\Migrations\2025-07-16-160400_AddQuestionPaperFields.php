<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddQuestionPaperFields extends Migration
{
    public function up()
    {
        // Add missing fields to question_papers table
        $fields = [
            'academic_year' => [
                'type' => 'VARCHAR',
                'constraint' => 20,
                'null' => true,
                'after' => 'status'
            ],
            'exam_type' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
                'after' => 'academic_year'
            ],
            'exam_date' => [
                'type' => 'DATE',
                'null' => true,
                'after' => 'exam_type'
            ],
            'instructions' => [
                'type' => 'TEXT',
                'null' => true,
                'after' => 'exam_date'
            ]
        ];

        $this->forge->addColumn('question_papers', $fields);

        // Add section_name field to paper_questions table
        $paperQuestionFields = [
            'section_name' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
                'after' => 'question_id'
            ]
        ];

        $this->forge->addColumn('paper_questions', $paperQuestionFields);
    }

    public function down()
    {
        // Remove the added fields
        $this->forge->dropColumn('question_papers', ['academic_year', 'exam_type', 'exam_date', 'instructions']);
        $this->forge->dropColumn('paper_questions', ['section_name']);
    }
}
