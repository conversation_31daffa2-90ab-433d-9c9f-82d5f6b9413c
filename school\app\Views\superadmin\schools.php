<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Schools Management - Super Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <meta name="csrf-token" content="<?= csrf_token() ?>">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/superadmin/dashboard" class="text-blue-600 hover:text-blue-800">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Dashboard
                    </a>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-gray-700">Super Admin</span>
                    <a href="/logout" class="text-red-600 hover:text-red-800">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Schools Management</h1>
            <p class="mt-2 text-gray-600">Manage school registrations and approvals</p>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 rounded-lg">
                        <i class="fas fa-school text-blue-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Schools</p>
                        <p class="text-2xl font-bold text-gray-900"><?= count($schools) ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 rounded-lg">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Active Schools</p>
                        <p class="text-2xl font-bold text-gray-900"><?= count($activeSchools) ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-orange-100 rounded-lg">
                        <i class="fas fa-clock text-orange-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Pending Approval</p>
                        <p class="text-2xl font-bold text-gray-900"><?= count($pendingSchools) ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-red-100 rounded-lg">
                        <i class="fas fa-times-circle text-red-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Rejected</p>
                        <p class="text-2xl font-bold text-gray-900"><?= count($rejectedSchools) ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter Tabs -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8 px-6">
                    <button onclick="showTab('all')" id="tab-all" class="tab-button border-b-2 border-blue-500 text-blue-600 py-4 px-1 text-sm font-medium">
                        All Schools (<?= count($schools) ?>)
                    </button>
                    <button onclick="showTab('pending')" id="tab-pending" class="tab-button border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-4 px-1 text-sm font-medium">
                        Pending (<?= count($pendingSchools) ?>)
                    </button>
                    <button onclick="showTab('active')" id="tab-active" class="tab-button border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-4 px-1 text-sm font-medium">
                        Active (<?= count($activeSchools) ?>)
                    </button>
                    <button onclick="showTab('rejected')" id="tab-rejected" class="tab-button border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-4 px-1 text-sm font-medium">
                        Rejected (<?= count($rejectedSchools) ?>)
                    </button>
                </nav>
            </div>

            <!-- Schools Table -->
            <div class="p-6">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">School</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Registration Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="schools-table-body" class="bg-white divide-y divide-gray-200">
                            <!-- Schools will be loaded here via JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Approval Modal -->
    <div id="approvalModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Confirm School Approval</h3>
            <p class="text-gray-600 mb-6">Are you sure you want to approve this school?</p>
            <div class="flex justify-end space-x-3">
                <button onclick="closeModal()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">Cancel</button>
                <button onclick="confirmApproval()" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">Approve</button>
            </div>
        </div>
    </div>

    <!-- Rejection Modal -->
    <div id="rejectionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Reject School Application</h3>
            <p class="text-gray-600 mb-4">Please provide a reason for rejection:</p>
            <textarea id="rejectionReason" class="w-full p-3 border border-gray-300 rounded-md" rows="4" placeholder="Enter rejection reason..."></textarea>
            <div class="flex justify-end space-x-3 mt-6">
                <button onclick="closeModal()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">Cancel</button>
                <button onclick="confirmRejection()" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">Reject</button>
            </div>
        </div>
    </div>

    <script>
        let currentSchoolId = null;
        let allSchools = <?= json_encode($schools) ?>;
        let currentFilter = 'all';

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            showTab('all');
        });

        function showTab(filter) {
            currentFilter = filter;
            
            // Update tab styles
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('border-blue-500', 'text-blue-600');
                btn.classList.add('border-transparent', 'text-gray-500');
            });
            
            document.getElementById(`tab-${filter}`).classList.remove('border-transparent', 'text-gray-500');
            document.getElementById(`tab-${filter}`).classList.add('border-blue-500', 'text-blue-600');
            
            // Filter and display schools
            let filteredSchools = allSchools;
            if (filter !== 'all') {
                filteredSchools = allSchools.filter(school => {
                    if (filter === 'pending') return school.status === 'inactive';
                    if (filter === 'active') return school.status === 'active';
                    if (filter === 'rejected') return school.status === 'rejected';
                    return true;
                });
            }
            
            displaySchools(filteredSchools);
        }

        function displaySchools(schools) {
            const tbody = document.getElementById('schools-table-body');
            tbody.innerHTML = '';
            
            if (schools.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                            No schools found for this filter.
                        </td>
                    </tr>
                `;
                return;
            }
            
            schools.forEach(school => {
                const row = createSchoolRow(school);
                tbody.appendChild(row);
            });
        }

        function createSchoolRow(school) {
            const tr = document.createElement('tr');
            tr.className = 'hover:bg-gray-50';
            
            const statusBadge = getStatusBadge(school.status);
            const actions = getActionButtons(school);
            
            tr.innerHTML = `
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10">
                            <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                <i class="fas fa-school text-blue-600"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <div class="text-sm font-medium text-gray-900">${school.name}</div>
                            <div class="text-sm text-gray-500">${school.address || 'No address provided'}</div>
                        </div>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">${school.email}</div>
                    <div class="text-sm text-gray-500">${school.phone || 'No phone provided'}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    ${statusBadge}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    ${new Date(school.created_at).toLocaleDateString()}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    ${actions}
                </td>
            `;
            
            return tr;
        }

        function getStatusBadge(status) {
            const badges = {
                'active': '<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Active</span>',
                'inactive': '<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800">Pending</span>',
                'rejected': '<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Rejected</span>'
            };
            return badges[status] || '<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">Unknown</span>';
        }

        function getActionButtons(school) {
            if (school.status === 'inactive') {
                return `
                    <button onclick="approveSchool(${school.id})" class="text-green-600 hover:text-green-900 mr-3">
                        <i class="fas fa-check"></i> Approve
                    </button>
                    <button onclick="rejectSchool(${school.id})" class="text-red-600 hover:text-red-900">
                        <i class="fas fa-times"></i> Reject
                    </button>
                `;
            } else if (school.status === 'rejected') {
                return `
                    <button onclick="approveSchool(${school.id})" class="text-green-600 hover:text-green-900">
                        <i class="fas fa-check"></i> Approve
                    </button>
                `;
            } else {
                return `
                    <span class="text-gray-400">No actions available</span>
                `;
            }
        }

        function approveSchool(id) {
            currentSchoolId = id;
            document.getElementById('approvalModal').classList.remove('hidden');
            document.getElementById('approvalModal').classList.add('flex');
        }

        function rejectSchool(id) {
            currentSchoolId = id;
            document.getElementById('rejectionModal').classList.remove('hidden');
            document.getElementById('rejectionModal').classList.add('flex');
        }

        function closeModal() {
            document.getElementById('approvalModal').classList.add('hidden');
            document.getElementById('rejectionModal').classList.add('hidden');
            currentSchoolId = null;
            document.getElementById('rejectionReason').value = '';
        }

        function confirmApproval() {
            if (!currentSchoolId) return;
            
            fetch(`/superadmin/approve/${currentSchoolId}`, {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('School approved successfully!');
                    location.reload();
                } else {
                    alert(data.message || 'Failed to approve school');
                }
            })
            .catch(error => {
                alert('Network error occurred');
            })
            .finally(() => {
                closeModal();
            });
        }

        function confirmRejection() {
            if (!currentSchoolId) return;
            
            const reason = document.getElementById('rejectionReason').value.trim();
            if (!reason) {
                alert('Please provide a reason for rejection');
                return;
            }
            
            fetch(`/superadmin/reject/${currentSchoolId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ reason: reason })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('School rejected successfully!');
                    location.reload();
                } else {
                    alert(data.message || 'Failed to reject school');
                }
            })
            .catch(error => {
                alert('Network error occurred');
            })
            .finally(() => {
                closeModal();
            });
        }
    </script>
</body>
</html>
