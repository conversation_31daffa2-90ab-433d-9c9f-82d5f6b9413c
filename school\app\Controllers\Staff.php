<?php

namespace App\Controllers;

use App\Models\UserModel;
use App\Models\UserProfileModel;
use App\Models\SchoolModel;
use App\Models\RoleModel;
use App\Models\UserRoleModel;
use App\Models\QuestionModel;
use App\Models\SubjectModel;

class Staff extends BaseController
{
    protected $userModel;
    protected $userProfileModel;
    protected $schoolModel;
    protected $roleModel;
    protected $userRoleModel;
    protected $questionModel;
    protected $subjectModel;

    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->userProfileModel = new UserProfileModel();
        $this->schoolModel = new SchoolModel();
        $this->roleModel = new RoleModel();
        $this->userRoleModel = new UserRoleModel();
        $this->questionModel = new QuestionModel();
        $this->subjectModel = new SubjectModel();
    }

    /**
     * Staff login page
     */
    public function login()
    {
        // If already logged in, redirect to dashboard
        if (session()->get('logged_in') && session()->get('role') === 'staff') {
            return redirect()->to('/staff/dashboard');
        }

        return view('staff/login');
    }

    /**
     * Handle staff login
     */
    public function authenticate()
    {
        $email = $this->request->getPost('email');
        $password = $this->request->getPost('password');
        $isAjax = $this->request->isAJAX();

        // Validate input
        if (!$email || !$password) {
            $message = 'Email and password are required';
            if ($isAjax) {
                return $this->response->setJSON(['success' => false, 'message' => $message]);
            }
            return redirect()->back()->with('error', $message);
        }

        // Find user by email
        $user = $this->userModel->where('email', $email)
                               ->where('is_deleted', 0)
                               ->first();

        if (!$user) {
            $message = 'Invalid email or password';
            if ($isAjax) {
                return $this->response->setJSON(['success' => false, 'message' => $message]);
            }
            return redirect()->back()->with('error', $message);
        }

        // Check password
        if (!password_verify($password, $user['password'])) {
            $message = 'Invalid email or password';
            if ($isAjax) {
                return $this->response->setJSON(['success' => false, 'message' => $message]);
            }
            return redirect()->back()->with('error', $message);
        }

        // Check if user is active
        if ($user['status'] !== 'active') {
            $message = 'Your account is inactive. Please contact your administrator.';
            if ($isAjax) {
                return $this->response->setJSON(['success' => false, 'message' => $message]);
            }
            return redirect()->back()->with('error', $message);
        }

        // Get user profile and school information
        $profile = $this->userProfileModel->where('user_id', $user['id'])->first();
        $school = $this->schoolModel->find($user['school_id']);

        // Get user roles
        $userRoles = $this->userRoleModel->getUserRoles($user['id']);

        // Set session data
        session()->set([
            'user_id' => $user['id'],
            'school_id' => $user['school_id'],
            'name' => $user['name'],
            'email' => $user['email'],
            'role' => 'staff',
            'designation' => $profile['designation'] ?? 'Staff',
            'school_name' => $school['name'] ?? 'Unknown School',
            'user_roles' => $userRoles,
            'logged_in' => true
        ]);

        // Log successful login
        log_message('info', "Staff login successful: {$email} from school ID: {$user['school_id']}");

        // Log audit trail for staff login
        try {
            $auditLogger = new \App\Services\AuditLogger();
            $auditLogger->logLogin($user['id'], $user['school_id'], 'staff');
        } catch (\Exception $e) {
            log_message('error', 'Failed to log staff login audit: ' . $e->getMessage());
        }

        if ($isAjax) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Login successful',
                'redirect' => site_url('staff/dashboard')
            ]);
        }

        return redirect()->to('/staff/dashboard');
    }

    /**
     * Staff dashboard
     */
    public function dashboard()
    {
        // Check authentication
        if (!session()->get('logged_in') || session()->get('role') !== 'staff') {
            return redirect()->to('/staff/login')->with('error', 'Please login first');
        }

        $userId = session()->get('user_id');
        $schoolId = session()->get('school_id');

        // Get user profile
        $profile = $this->userProfileModel->where('user_id', $userId)->first();
        
        // Get user roles and permissions
        $userRoles = $this->userRoleModel->getUserRoles($userId);
        
        // Get dashboard statistics based on user role
        $stats = $this->getDashboardStats($userId, $schoolId, $userRoles);

        $data = [
            'user' => [
                'id' => $userId,
                'name' => session()->get('name'),
                'email' => session()->get('email'),
                'designation' => session()->get('designation'),
                'profile' => $profile
            ],
            'school' => [
                'id' => $schoolId,
                'name' => session()->get('school_name')
            ],
            'roles' => $userRoles,
            'stats' => $stats,
            'permissions' => $this->getUserPermissions($userId)
        ];

        return view('staff/dashboard', $data);
    }

    /**
     * Get dashboard statistics based on user role
     */
    private function getDashboardStats($userId, $schoolId, $userRoles)
    {
        // Get question stats for this staff member
        $questionStats = $this->questionModel->getStaffStats($userId, $schoolId);

        // Get count of subjects that have questions created by this staff
        $subjectCount = $this->questionModel->select('subject')
                                           ->where('staff_id', $userId)
                                           ->where('school_id', $schoolId)
                                           ->groupBy('subject')
                                           ->countAllResults();

        // Basic stats that all staff can see
        $stats = [
            'questions_created' => $questionStats['questions_created'],
            'pending_reviews' => $questionStats['pending_reviews'],
            'approved_questions' => $questionStats['approved_questions'],
            'total_subjects' => $subjectCount
        ];

        // Add role-specific stats
        foreach ($userRoles as $role) {
            switch (strtolower($role['name'])) {
                case 'admin':
                case 'principal':
                    // Admin can see all school stats
                    $adminStats = $this->questionModel->getAdminStats($schoolId);
                    $stats = array_merge($stats, $adminStats);
                    $stats['total_users'] = $this->userModel->where('school_id', $schoolId)
                                                          ->where('is_deleted', 0)
                                                          ->countAllResults();
                    break;

                case 'teacher':
                case 'faculty':
                    // Teachers can see their subject-related stats
                    break;

                case 'reviewer':
                    // Reviewers can see review-related stats
                    break;
            }
        }

        return $stats;
    }

    /**
     * Get user permissions
     */
    private function getUserPermissions($userId)
    {
        // This would typically fetch from role_permissions table
        // For now, return basic permissions
        return [
            'can_create_questions' => true,
            'can_review_questions' => false,
            'can_approve_questions' => false,
            'can_manage_users' => false,
            'can_generate_papers' => true,
            'can_view_reports' => false
        ];
    }

    /**
     * Staff logout
     */
    public function logout()
    {
        // Log logout
        $email = session()->get('email');
        if ($email) {
            log_message('info', "Staff logout: {$email}");
        }

        // Destroy session
        session()->destroy();

        // Redirect to homepage with logout success message
        return redirect()->to('/')->with('logout_success', true);
    }

    /**
     * Profile page
     */
    public function profile()
    {
        // Check authentication
        if (!session()->get('logged_in') || session()->get('role') !== 'staff') {
            return redirect()->to('/staff/login');
        }

        $userId = session()->get('user_id');
        $user = $this->userModel->find($userId);
        $profile = $this->userProfileModel->where('user_id', $userId)->first();

        return view('staff/profile', [
            'user' => $user,
            'profile' => $profile
        ]);
    }

    /**
     * Update profile
     */
    public function updateProfile()
    {
        // Check authentication
        if (!session()->get('logged_in') || session()->get('role') !== 'staff') {
            return $this->response->setJSON(['success' => false, 'message' => 'Authentication required']);
        }

        $userId = session()->get('user_id');
        $schoolId = session()->get('school_id');

        // Validation rules
        $validation = \Config\Services::validation();
        $validation->setRules([
            'name' => 'required|min_length[3]|max_length[255]',
            'phone' => 'permit_empty|min_length[10]|max_length[20]',
            'designation' => 'permit_empty|max_length[255]',
            'department' => 'permit_empty|max_length[255]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return $this->response->setJSON([
                'success' => false,
                'errors' => $validation->getErrors()
            ]);
        }

        // Update user name
        $this->userModel->update($userId, [
            'name' => $this->request->getPost('name')
        ]);

        // Update or create profile
        $profileData = [
            'phone' => $this->request->getPost('phone'),
            'designation' => $this->request->getPost('designation'),
            'department' => $this->request->getPost('department'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $existingProfile = $this->userProfileModel->where('user_id', $userId)->first();
        
        if ($existingProfile) {
            $this->userProfileModel->update($existingProfile['id'], $profileData);
        } else {
            $profileData['user_id'] = $userId;
            $profileData['school_id'] = $schoolId;
            $profileData['created_at'] = date('Y-m-d H:i:s');
            $this->userProfileModel->insert($profileData);
        }

        // Update session
        session()->set('name', $this->request->getPost('name'));
        session()->set('designation', $this->request->getPost('designation'));

        return $this->response->setJSON([
            'success' => true,
            'message' => 'Profile updated successfully'
        ]);
    }

    /**
     * Get subjects for a standard (AJAX)
     */
    public function getSubjects()
    {
        $standard = $this->request->getGet('standard');
        $schoolId = session()->get('school_id');

        // Debug logging
        log_message('debug', 'getSubjects called with standard: ' . $standard . ', schoolId: ' . $schoolId);

        if (!$standard) {
            return $this->response->setJSON(['success' => false, 'message' => 'Standard is required']);
        }

        if (!$schoolId) {
            // For testing, use school ID 1 if session doesn't have it
            $schoolId = 1;
            log_message('debug', 'Using default school ID: 1');
        }

        $subjects = $this->subjectModel->getSubjectsByStandard($schoolId, $standard);

        log_message('debug', 'Found subjects: ' . json_encode($subjects));

        return $this->response->setJSON([
            'success' => true,
            'subjects' => $subjects
        ]);
    }

    /**
     * Create new question
     */
    public function createQuestion()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(404);
        }

        // Check authentication
        if (!session()->get('logged_in') || session()->get('role') !== 'staff') {
            return $this->response->setJSON(['success' => false, 'message' => 'Authentication required']);
        }

        $userId = session()->get('user_id');
        $schoolId = session()->get('school_id');

        // Validation rules
        $validation = \Config\Services::validation();
        $validation->setRules([
            'standard' => 'required|integer|greater_than[0]|less_than[13]',
            'subject' => 'required|string|max_length[100]',
            'question_type' => 'required|in_list[multiple_choice,short_answer,long_answer,essay]',
            'difficulty' => 'required|in_list[easy,medium,hard]',
            'marks' => 'required|integer|greater_than[0]|less_than[21]',
            'question_text' => 'required|string'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return $this->response->setJSON([
                'success' => false,
                'errors' => $validation->getErrors()
            ]);
        }

        $standard = $this->request->getPost('standard');
        $subject = $this->request->getPost('subject');
        $questionType = $this->request->getPost('question_type');

        // Check if staff can create questions for this subject and standard
        if (!$this->subjectModel->canStaffCreateQuestion($userId, $subject, $standard, $schoolId)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'You are not authorized to create questions for this subject and standard'
            ]);
        }

        // Prepare question data
        $questionData = [
            'school_id' => $schoolId,
            'staff_id' => $userId,
            'standard' => $standard,
            'subject' => $subject,
            'question_type' => $questionType,
            'difficulty' => $this->request->getPost('difficulty'),
            'marks' => $this->request->getPost('marks'),
            'question_text' => $this->request->getPost('question_text'),
            'chapter' => $this->request->getPost('chapter'),
            'chapter_name' => $this->request->getPost('chapter_name'),
            'topic_name' => $this->request->getPost('topic_name'),
            'status' => 'pending' // Submit directly for review
        ];

        // Debug: Log the chapter and topic data
        log_message('debug', 'Chapter data: ' . json_encode([
            'chapter' => $this->request->getPost('chapter'),
            'chapter_name' => $this->request->getPost('chapter_name'),
            'topic_name' => $this->request->getPost('topic_name')
        ]));

        // Handle MCQ options
        if ($questionType === 'multiple_choice') {
            $questionData['option_a'] = $this->request->getPost('option_a');
            $questionData['option_b'] = $this->request->getPost('option_b');
            $questionData['option_c'] = $this->request->getPost('option_c');
            $questionData['option_d'] = $this->request->getPost('option_d');
            $questionData['correct_answer'] = $this->request->getPost('correct_answer');

            // Validate MCQ options
            if (empty($questionData['option_a']) || empty($questionData['option_b']) ||
                empty($questionData['option_c']) || empty($questionData['option_d']) ||
                empty($questionData['correct_answer'])) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'All MCQ options and correct answer are required'
                ]);
            }
        } else {
            // Handle other question types
            $answer = $this->request->getPost('answer');
            if (empty($answer)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Answer/Solution is required'
                ]);
            }
            $questionData['answer'] = $answer;
        }

        // Insert question
        $questionId = $this->questionModel->insert($questionData);

        if ($questionId) {
            // Log question creation
            try {
                $auditLogger = new \App\Services\AuditLogger();
                $auditLogger->logCreate(
                    'question',
                    $questionId,
                    substr($questionData['question_text'], 0, 50) . '...',
                    $questionData,
                    $userId,
                    $schoolId,
                    "Question created by staff: {$questionData['question_type']} for {$questionData['standard']} {$questionData['subject']}"
                );
            } catch (\Exception $e) {
                log_message('error', 'Failed to log question creation: ' . $e->getMessage());
            }

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Question submitted successfully for review!',
                'question_id' => $questionId
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to create question. Please try again.'
            ]);
        }
    }

    /**
     * Save question as draft
     */
    public function saveDraft()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(404);
        }

        // Check authentication
        if (!session()->get('logged_in') || session()->get('role') !== 'staff') {
            return $this->response->setJSON(['success' => false, 'message' => 'Authentication required']);
        }

        $userId = session()->get('user_id');
        $schoolId = session()->get('school_id');

        // Prepare question data (minimal validation for draft)
        $questionData = [
            'school_id' => $schoolId,
            'staff_id' => $userId,
            'standard' => $this->request->getPost('standard'),
            'subject' => $this->request->getPost('subject'),
            'question_type' => $this->request->getPost('question_type'),
            'difficulty' => $this->request->getPost('difficulty'),
            'marks' => $this->request->getPost('marks'),
            'question_text' => $this->request->getPost('question_text'),
            'chapter' => $this->request->getPost('chapter'),
            'chapter_name' => $this->request->getPost('chapter_name'),
            'topic_name' => $this->request->getPost('topic_name'),
            'option_a' => $this->request->getPost('option_a'),
            'option_b' => $this->request->getPost('option_b'),
            'option_c' => $this->request->getPost('option_c'),
            'option_d' => $this->request->getPost('option_d'),
            'correct_answer' => $this->request->getPost('correct_answer'),
            'answer' => $this->request->getPost('answer'),
            'status' => 'draft'
        ];

        // Remove empty fields
        $questionData = array_filter($questionData, function($value) {
            return $value !== null && $value !== '';
        });

        // Insert question
        $questionId = $this->questionModel->insert($questionData);

        if ($questionId) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Question saved as draft!',
                'question_id' => $questionId
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to save draft. Please try again.'
            ]);
        }
    }

    /**
     * Get staff questions (AJAX)
     */
    public function getQuestions()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(404);
        }

        // Check authentication
        if (!session()->get('logged_in') || session()->get('role') !== 'staff') {
            return $this->response->setJSON(['success' => false, 'message' => 'Authentication required']);
        }

        $userId = session()->get('user_id');
        $schoolId = session()->get('school_id');

        // Get filters
        $filters = [
            'subject' => $this->request->getGet('subject'),
            'status' => $this->request->getGet('status'),
            'standard' => $this->request->getGet('standard')
        ];

        $questions = $this->questionModel->getQuestionsByStaff($userId, $schoolId, $filters);

        return $this->response->setJSON([
            'success' => true,
            'questions' => $questions
        ]);
    }

    /**
     * Get question details for editing
     */
    public function getQuestionDetails($questionId)
    {
        $staffId = session()->get('user_id');
        $schoolId = session()->get('school_id') ?: 1; // Fallback for testing

        if (!$questionId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Question ID is required'
            ]);
        }

        // Get question details - ensure it belongs to this staff member
        $question = $this->questionModel->where('id', $questionId)
                                       ->where('staff_id', $staffId)
                                       ->where('school_id', $schoolId)
                                       ->first();

        if (!$question) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Question not found or access denied'
            ]);
        }

        return $this->response->setJSON([
            'success' => true,
            'question' => $question
        ]);
    }

    /**
     * Update question
     */
    public function updateQuestion($questionId)
    {
        $staffId = session()->get('user_id') ?: 1; // Fallback for testing
        $schoolId = session()->get('school_id') ?: 1; // Fallback for testing

        if (!$questionId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Question ID is required'
            ]);
        }

        // Log the request for debugging
        log_message('debug', 'updateQuestion called with ID: ' . $questionId);
        log_message('debug', 'Request method: ' . $this->request->getMethod());
        log_message('debug', 'POST data: ' . json_encode($this->request->getPost()));

        // Verify question belongs to this staff member and can be edited
        $existingQuestion = $this->questionModel->where('id', $questionId)
                                               ->where('staff_id', $staffId)
                                               ->where('school_id', $schoolId)
                                               ->first();

        if (!$existingQuestion) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Question not found or access denied'
            ]);
        }

        // Only allow editing of draft and rejected questions
        if (!in_array($existingQuestion['status'], ['draft', 'rejected'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Only draft and rejected questions can be edited'
            ]);
        }

        // Validate input
        $validation = \Config\Services::validation();
        $validation->setRules([
            'standard' => 'required|integer|greater_than[0]|less_than[13]',
            'subject' => 'required|max_length[100]',
            'question_type' => 'required|in_list[multiple_choice,short_answer,long_answer,essay]',
            'difficulty' => 'required|in_list[easy,medium,hard]',
            'marks' => 'required|integer|greater_than[0]|less_than[21]',
            'question_text' => 'required|min_length[10]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return $this->response->setJSON([
                'success' => false,
                'errors' => $validation->getErrors()
            ]);
        }

        // Prepare update data
        $updateData = [
            'standard' => $this->request->getPost('standard'),
            'subject' => $this->request->getPost('subject'),
            'question_type' => $this->request->getPost('question_type'),
            'difficulty' => $this->request->getPost('difficulty'),
            'marks' => $this->request->getPost('marks'),
            'question_text' => $this->request->getPost('question_text'),
            'chapter' => $this->request->getPost('chapter'),
            'chapter_name' => $this->request->getPost('chapter_name'),
            'topic_name' => $this->request->getPost('topic_name'),
            'answer' => $this->request->getPost('answer'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Handle MCQ options
        if ($this->request->getPost('question_type') === 'multiple_choice') {
            $options = [
                'option_a' => $this->request->getPost('option_a'),
                'option_b' => $this->request->getPost('option_b'),
                'option_c' => $this->request->getPost('option_c'),
                'option_d' => $this->request->getPost('option_d')
            ];
            $updateData['options'] = json_encode($options);
            $updateData['correct_answer'] = $this->request->getPost('correct_answer');
        } else {
            $updateData['options'] = null;
            $updateData['correct_answer'] = null;
        }

        // Determine status based on request
        $requestedStatus = $this->request->getPost('status');
        log_message('debug', 'Requested status: ' . $requestedStatus);

        if ($requestedStatus === 'draft') {
            // Explicitly saving as draft
            $updateData['status'] = 'draft';
        } else if ($existingQuestion['status'] === 'rejected') {
            // Rejected question being resubmitted
            $updateData['status'] = 'pending'; // Re-submit for review
            $updateData['admin_feedback'] = null; // Clear previous feedback
            $updateData['reviewed_by'] = null;
            $updateData['reviewed_at'] = null;
        } else if ($existingQuestion['status'] === 'draft') {
            // Draft being submitted for review
            $updateData['status'] = 'pending';
        } else {
            // Keep existing status if not specified
            $updateData['status'] = $existingQuestion['status'];
        }

        // Update question
        if ($this->questionModel->update($questionId, $updateData)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Question updated successfully',
                'question_id' => $questionId
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to update question'
            ]);
        }
    }

    /**
     * Delete question
     */
    public function deleteQuestion($questionId)
    {
        $staffId = session()->get('user_id');
        $schoolId = session()->get('school_id') ?: 1; // Fallback for testing

        if (!$questionId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Question ID is required'
            ]);
        }

        // Verify question belongs to this staff member and can be deleted
        $question = $this->questionModel->where('id', $questionId)
                                       ->where('staff_id', $staffId)
                                       ->where('school_id', $schoolId)
                                       ->first();

        if (!$question) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Question not found or access denied'
            ]);
        }

        // Only allow deletion of draft and rejected questions
        if (!in_array($question['status'], ['draft', 'rejected'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Only draft and rejected questions can be deleted'
            ]);
        }

        // Delete question
        if ($this->questionModel->delete($questionId)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Question deleted successfully'
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to delete question'
            ]);
        }
    }

    /**
     * Get subjects with question counts for staff
     */
    public function getSubjectsWithCounts()
    {
        $staffId = session()->get('user_id');
        $schoolId = session()->get('school_id') ?: 1; // Fallback for testing

        if (!$staffId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ]);
        }

        // Get subjects with question counts
        $subjects = $this->questionModel->select('subject, COUNT(*) as question_count,
                                                 SUM(CASE WHEN status = "approved" THEN 1 ELSE 0 END) as approved_count,
                                                 SUM(CASE WHEN status = "pending" THEN 1 ELSE 0 END) as pending_count,
                                                 SUM(CASE WHEN status = "rejected" THEN 1 ELSE 0 END) as rejected_count,
                                                 SUM(CASE WHEN status = "draft" THEN 1 ELSE 0 END) as draft_count')
                                        ->where('staff_id', $staffId)
                                        ->where('school_id', $schoolId)
                                        ->groupBy('subject')
                                        ->orderBy('question_count', 'DESC')
                                        ->findAll();

        return $this->response->setJSON([
            'success' => true,
            'subjects' => $subjects
        ]);
    }

    /**
     * Get questions by subject for staff
     */
    public function getQuestionsBySubject($subject)
    {
        $staffId = session()->get('user_id');
        $schoolId = session()->get('school_id') ?: 1; // Fallback for testing

        if (!$staffId || !$subject) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid parameters'
            ]);
        }

        // Get questions for this subject
        $questions = $this->questionModel->where('staff_id', $staffId)
                                        ->where('school_id', $schoolId)
                                        ->where('subject', urldecode($subject))
                                        ->orderBy('created_at', 'DESC')
                                        ->findAll();

        return $this->response->setJSON([
            'success' => true,
            'subject' => urldecode($subject),
            'questions' => $questions
        ]);
    }

    /**
     * Get detailed reports for staff
     */
    public function getDetailedReports()
    {
        $staffId = session()->get('user_id');
        $schoolId = session()->get('school_id') ?: 1; // Fallback for testing

        if (!$staffId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ]);
        }

        // Get overall statistics
        $overallStats = $this->questionModel->getStaffStats($staffId, $schoolId);

        // Get subject-wise statistics
        $subjectStats = $this->questionModel->select('subject,
                                                     COUNT(*) as total_questions,
                                                     SUM(CASE WHEN status = "approved" THEN 1 ELSE 0 END) as approved,
                                                     SUM(CASE WHEN status = "pending" THEN 1 ELSE 0 END) as pending,
                                                     SUM(CASE WHEN status = "rejected" THEN 1 ELSE 0 END) as rejected,
                                                     SUM(CASE WHEN status = "draft" THEN 1 ELSE 0 END) as draft,
                                                     AVG(marks) as avg_marks')
                                            ->where('staff_id', $staffId)
                                            ->where('school_id', $schoolId)
                                            ->groupBy('subject')
                                            ->orderBy('total_questions', 'DESC')
                                            ->findAll();

        // Get question type distribution
        $typeStats = $this->questionModel->select('question_type, COUNT(*) as count')
                                        ->where('staff_id', $staffId)
                                        ->where('school_id', $schoolId)
                                        ->groupBy('question_type')
                                        ->findAll();

        // Get difficulty distribution
        $difficultyStats = $this->questionModel->select('difficulty, COUNT(*) as count')
                                              ->where('staff_id', $staffId)
                                              ->where('school_id', $schoolId)
                                              ->groupBy('difficulty')
                                              ->findAll();

        // Get monthly statistics (last 6 months)
        $monthlyStats = $this->questionModel->select('DATE_FORMAT(created_at, "%Y-%m") as month,
                                                     COUNT(*) as questions_created,
                                                     SUM(CASE WHEN status = "approved" THEN 1 ELSE 0 END) as approved')
                                           ->where('staff_id', $staffId)
                                           ->where('school_id', $schoolId)
                                           ->where('created_at >=', date('Y-m-d', strtotime('-6 months')))
                                           ->groupBy('month')
                                           ->orderBy('month', 'DESC')
                                           ->findAll();

        return $this->response->setJSON([
            'success' => true,
            'reports' => [
                'overall' => $overallStats,
                'subjects' => $subjectStats,
                'question_types' => $typeStats,
                'difficulty' => $difficultyStats,
                'monthly' => $monthlyStats
            ]
        ]);
    }

    /**
     * Get recent activities for staff
     */
    public function getRecentActivities()
    {
        $staffId = session()->get('user_id') ?: 1; // Fallback for testing
        $schoolId = session()->get('school_id') ?: 1; // Fallback for testing

        // Get recent questions with their activities
        $recentQuestions = $this->questionModel->select('id, subject, question_text, status, created_at, updated_at, admin_feedback')
                                              ->where('staff_id', $staffId)
                                              ->where('school_id', $schoolId)
                                              ->orderBy('updated_at', 'DESC')
                                              ->limit(10)
                                              ->findAll();

        $activities = [];

        foreach ($recentQuestions as $question) {
            $questionText = strlen($question['question_text']) > 50 ?
                           substr($question['question_text'], 0, 50) . '...' :
                           $question['question_text'];

            // Determine activity type and message
            $activity = [
                'id' => $question['id'],
                'question_text' => $questionText,
                'subject' => $question['subject'],
                'timestamp' => $question['updated_at'],
                'time_ago' => $this->timeAgo($question['updated_at'])
            ];

            switch ($question['status']) {
                case 'draft':
                    $activity['type'] = 'draft';
                    $activity['icon'] = 'fas fa-save';
                    $activity['color'] = 'gray';
                    $activity['message'] = "Saved as draft in {$question['subject']}";
                    break;
                case 'pending':
                    $activity['type'] = 'pending';
                    $activity['icon'] = 'fas fa-clock';
                    $activity['color'] = 'yellow';
                    $activity['message'] = "Submitted for review in {$question['subject']}";
                    break;
                case 'approved':
                    $activity['type'] = 'approved';
                    $activity['icon'] = 'fas fa-check';
                    $activity['color'] = 'green';
                    $activity['message'] = "Question approved in {$question['subject']}";
                    break;
                case 'rejected':
                    $activity['type'] = 'rejected';
                    $activity['icon'] = 'fas fa-times';
                    $activity['color'] = 'red';
                    $activity['message'] = "Question rejected in {$question['subject']}";
                    if ($question['admin_feedback']) {
                        $activity['feedback'] = $question['admin_feedback'];
                    }
                    break;
                default:
                    $activity['type'] = 'unknown';
                    $activity['icon'] = 'fas fa-question';
                    $activity['color'] = 'gray';
                    $activity['message'] = "Activity in {$question['subject']}";
            }

            $activities[] = $activity;
        }

        return $this->response->setJSON([
            'success' => true,
            'activities' => $activities
        ]);
    }

    /**
     * Helper function to calculate time ago
     */
    private function timeAgo($datetime)
    {
        $time = time() - strtotime($datetime);

        if ($time < 60) return 'Just now';
        if ($time < 3600) return floor($time/60) . ' minutes ago';
        if ($time < 86400) return floor($time/3600) . ' hours ago';
        if ($time < 2592000) return floor($time/86400) . ' days ago';
        if ($time < 31536000) return floor($time/2592000) . ' months ago';
        return floor($time/31536000) . ' years ago';
    }

    /**
     * Test update method
     */
    public function testUpdate($questionId)
    {
        return $this->response->setJSON([
            'success' => true,
            'message' => 'Test update method called',
            'question_id' => $questionId,
            'method' => $this->request->getMethod(),
            'post_data' => $this->request->getPost()
        ]);
    }
}
