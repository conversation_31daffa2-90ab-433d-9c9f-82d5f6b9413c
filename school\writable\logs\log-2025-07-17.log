CRITICAL - 2025-07-17 11:58:50 --> Error: Class "Locale" not found
[Method: GET, Route: /]
in SYSTEMPATH\CodeIgniter.php on line 189.
 1 SYSTEMPATH\Boot.php(321): CodeIgniter\CodeIgniter->initialize()
 2 SYSTEMPATH\Boot.php(101): CodeIgniter\Boot::initializeCodeIgniter()
 3 ROOTPATH\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
CRITICAL - 2025-07-17 11:58:50 --> ErrorException: Uncaught Error: Class "Locale" not found in C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\I18n\TimeTrait.php:76
Stack trace:
#0 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\I18n\TimeTrait.php(117): CodeIgniter\I18n\Time->__construct(NULL, NULL, NULL)
#1 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\HTTP\ResponseTrait.php(398): CodeIgniter\I18n\Time::now()
#2 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\HTTP\ResponseTrait.php(376): CodeIgniter\HTTP\Response->sendHeaders()
#3 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\Debug\ExceptionHandler.php(85): CodeIgniter\HTTP\Response->send()
#4 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\Debug\Exceptions.php(162): CodeIgniter\Debug\ExceptionHandler->handle(Object(Error), Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), 500, 1)
#5 [internal function]: CodeIgniter\Debug\Exceptions->exceptionHandler(Object(Error))
#6 {main}
  thrown
【Previous Exception】
Error
Class "Locale" not found
#0 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\Boot.php(321): CodeIgniter\CodeIgniter->initialize()
#1 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\Boot.php(101): CodeIgniter\Boot::initializeCodeIgniter()
#2 C:\xampp\htdocs\New folder\schoolquestionbank\school\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#3 {main}
[Method: GET, Route: /]
in SYSTEMPATH\I18n\TimeTrait.php on line 76.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
CRITICAL - 2025-07-17 12:02:30 --> Error: Class "Locale" not found
[Method: GET, Route: /]
in SYSTEMPATH\CodeIgniter.php on line 189.
 1 SYSTEMPATH\Boot.php(321): CodeIgniter\CodeIgniter->initialize()
 2 SYSTEMPATH\Boot.php(101): CodeIgniter\Boot::initializeCodeIgniter()
 3 ROOTPATH\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
CRITICAL - 2025-07-17 12:02:30 --> ErrorException: Uncaught Error: Class "Locale" not found in C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\I18n\TimeTrait.php:76
Stack trace:
#0 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\I18n\TimeTrait.php(117): CodeIgniter\I18n\Time->__construct(NULL, NULL, NULL)
#1 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\HTTP\ResponseTrait.php(398): CodeIgniter\I18n\Time::now()
#2 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\HTTP\ResponseTrait.php(376): CodeIgniter\HTTP\Response->sendHeaders()
#3 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\Debug\ExceptionHandler.php(85): CodeIgniter\HTTP\Response->send()
#4 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\Debug\Exceptions.php(162): CodeIgniter\Debug\ExceptionHandler->handle(Object(Error), Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), 500, 1)
#5 [internal function]: CodeIgniter\Debug\Exceptions->exceptionHandler(Object(Error))
#6 {main}
  thrown
【Previous Exception】
Error
Class "Locale" not found
#0 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\Boot.php(321): CodeIgniter\CodeIgniter->initialize()
#1 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\Boot.php(101): CodeIgniter\Boot::initializeCodeIgniter()
#2 C:\xampp\htdocs\New folder\schoolquestionbank\school\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#3 {main}
[Method: GET, Route: /]
in SYSTEMPATH\I18n\TimeTrait.php on line 76.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
CRITICAL - 2025-07-17 12:02:38 --> Error: Class "Locale" not found
[Method: GET, Route: /]
in SYSTEMPATH\CodeIgniter.php on line 189.
 1 SYSTEMPATH\Boot.php(321): CodeIgniter\CodeIgniter->initialize()
 2 SYSTEMPATH\Boot.php(101): CodeIgniter\Boot::initializeCodeIgniter()
 3 ROOTPATH\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
CRITICAL - 2025-07-17 12:02:38 --> ErrorException: Uncaught Error: Class "Locale" not found in C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\I18n\TimeTrait.php:76
Stack trace:
#0 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\I18n\TimeTrait.php(117): CodeIgniter\I18n\Time->__construct(NULL, NULL, NULL)
#1 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\HTTP\ResponseTrait.php(398): CodeIgniter\I18n\Time::now()
#2 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\HTTP\ResponseTrait.php(376): CodeIgniter\HTTP\Response->sendHeaders()
#3 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\Debug\ExceptionHandler.php(85): CodeIgniter\HTTP\Response->send()
#4 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\Debug\Exceptions.php(162): CodeIgniter\Debug\ExceptionHandler->handle(Object(Error), Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), 500, 1)
#5 [internal function]: CodeIgniter\Debug\Exceptions->exceptionHandler(Object(Error))
#6 {main}
  thrown
【Previous Exception】
Error
Class "Locale" not found
#0 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\Boot.php(321): CodeIgniter\CodeIgniter->initialize()
#1 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\Boot.php(101): CodeIgniter\Boot::initializeCodeIgniter()
#2 C:\xampp\htdocs\New folder\schoolquestionbank\school\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#3 {main}
[Method: GET, Route: /]
in SYSTEMPATH\I18n\TimeTrait.php on line 76.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
CRITICAL - 2025-07-17 12:11:02 --> Error: Class "Locale" not found
[Method: GET, Route: /]
in SYSTEMPATH\CodeIgniter.php on line 189.
 1 SYSTEMPATH\Boot.php(321): CodeIgniter\CodeIgniter->initialize()
 2 SYSTEMPATH\Boot.php(101): CodeIgniter\Boot::initializeCodeIgniter()
 3 ROOTPATH\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
CRITICAL - 2025-07-17 12:11:02 --> ErrorException: Uncaught Error: Class "Locale" not found in C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\I18n\TimeTrait.php:76
Stack trace:
#0 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\I18n\TimeTrait.php(117): CodeIgniter\I18n\Time->__construct(NULL, NULL, NULL)
#1 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\HTTP\ResponseTrait.php(398): CodeIgniter\I18n\Time::now()
#2 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\HTTP\ResponseTrait.php(376): CodeIgniter\HTTP\Response->sendHeaders()
#3 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\Debug\ExceptionHandler.php(85): CodeIgniter\HTTP\Response->send()
#4 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\Debug\Exceptions.php(162): CodeIgniter\Debug\ExceptionHandler->handle(Object(Error), Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), 500, 1)
#5 [internal function]: CodeIgniter\Debug\Exceptions->exceptionHandler(Object(Error))
#6 {main}
  thrown
【Previous Exception】
Error
Class "Locale" not found
#0 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\Boot.php(321): CodeIgniter\CodeIgniter->initialize()
#1 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\Boot.php(101): CodeIgniter\Boot::initializeCodeIgniter()
#2 C:\xampp\htdocs\schoolquestionbank\school\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#3 {main}
[Method: GET, Route: /]
in SYSTEMPATH\I18n\TimeTrait.php on line 76.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
ERROR - 2025-07-17 10:14:22 --> mysqli_sql_exception: Table 'school.schools' doesn't exist in engine in C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\BaseModel.php(679): CodeIgniter\Model->doFindAll(0, 0)
#6 C:\xampp\htdocs\schoolquestionbank\school\app\Controllers\superadmin.php(22): CodeIgniter\BaseModel->findAll()
#7 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\SuperAdmin->dashboard()
#8 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\SuperAdmin))
#9 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#11 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 C:\xampp\htdocs\schoolquestionbank\school\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#14 {main}
CRITICAL - 2025-07-17 10:14:22 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'school.schools' doesn't exist in engine
[Method: GET, Route: superadmin/dashboard]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `schools`
WHERE `schools`.`deleted_at` IS NULL', [], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(679): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Controllers\superadmin.php(22): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\SuperAdmin->dashboard()
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\SuperAdmin))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
11 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
CRITICAL - 2025-07-17 10:14:22 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'school.schools' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `schools`
WHERE `schools`.`deleted_at` IS NULL')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `schools`
WHERE `schools`.`deleted_at` IS NULL')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `schools`
WHERE `schools`.`deleted_at` IS NULL', [], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(679): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Controllers\superadmin.php(22): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\SuperAdmin->dashboard()
 8 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\SuperAdmin))
 9 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
10 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
11 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
12 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
13 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
CRITICAL - 2025-07-17 10:14:22 --> [Caused by] mysqli_sql_exception: Table 'school.schools' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `schools`
WHERE `schools`.`deleted_at` IS NULL', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `schools`
WHERE `schools`.`deleted_at` IS NULL')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `schools`
WHERE `schools`.`deleted_at` IS NULL')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `schools`
WHERE `schools`.`deleted_at` IS NULL', [], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(679): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Controllers\superadmin.php(22): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\SuperAdmin->dashboard()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\SuperAdmin))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
