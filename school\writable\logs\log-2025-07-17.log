CRITICAL - 2025-07-17 11:58:50 --> Error: Class "Locale" not found
[Method: GET, Route: /]
in SYSTEMPATH\CodeIgniter.php on line 189.
 1 SYSTEMPATH\Boot.php(321): CodeIgniter\CodeIgniter->initialize()
 2 SYSTEMPATH\Boot.php(101): CodeIgniter\Boot::initializeCodeIgniter()
 3 ROOTPATH\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
CRITICAL - 2025-07-17 11:58:50 --> ErrorException: Uncaught Error: Class "Locale" not found in C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\I18n\TimeTrait.php:76
Stack trace:
#0 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\I18n\TimeTrait.php(117): CodeIgniter\I18n\Time->__construct(NULL, NULL, NULL)
#1 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\HTTP\ResponseTrait.php(398): CodeIgniter\I18n\Time::now()
#2 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\HTTP\ResponseTrait.php(376): CodeIgniter\HTTP\Response->sendHeaders()
#3 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\Debug\ExceptionHandler.php(85): CodeIgniter\HTTP\Response->send()
#4 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\Debug\Exceptions.php(162): CodeIgniter\Debug\ExceptionHandler->handle(Object(Error), Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), 500, 1)
#5 [internal function]: CodeIgniter\Debug\Exceptions->exceptionHandler(Object(Error))
#6 {main}
  thrown
【Previous Exception】
Error
Class "Locale" not found
#0 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\Boot.php(321): CodeIgniter\CodeIgniter->initialize()
#1 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\Boot.php(101): CodeIgniter\Boot::initializeCodeIgniter()
#2 C:\xampp\htdocs\New folder\schoolquestionbank\school\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#3 {main}
[Method: GET, Route: /]
in SYSTEMPATH\I18n\TimeTrait.php on line 76.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
CRITICAL - 2025-07-17 12:02:30 --> Error: Class "Locale" not found
[Method: GET, Route: /]
in SYSTEMPATH\CodeIgniter.php on line 189.
 1 SYSTEMPATH\Boot.php(321): CodeIgniter\CodeIgniter->initialize()
 2 SYSTEMPATH\Boot.php(101): CodeIgniter\Boot::initializeCodeIgniter()
 3 ROOTPATH\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
CRITICAL - 2025-07-17 12:02:30 --> ErrorException: Uncaught Error: Class "Locale" not found in C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\I18n\TimeTrait.php:76
Stack trace:
#0 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\I18n\TimeTrait.php(117): CodeIgniter\I18n\Time->__construct(NULL, NULL, NULL)
#1 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\HTTP\ResponseTrait.php(398): CodeIgniter\I18n\Time::now()
#2 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\HTTP\ResponseTrait.php(376): CodeIgniter\HTTP\Response->sendHeaders()
#3 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\Debug\ExceptionHandler.php(85): CodeIgniter\HTTP\Response->send()
#4 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\Debug\Exceptions.php(162): CodeIgniter\Debug\ExceptionHandler->handle(Object(Error), Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), 500, 1)
#5 [internal function]: CodeIgniter\Debug\Exceptions->exceptionHandler(Object(Error))
#6 {main}
  thrown
【Previous Exception】
Error
Class "Locale" not found
#0 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\Boot.php(321): CodeIgniter\CodeIgniter->initialize()
#1 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\Boot.php(101): CodeIgniter\Boot::initializeCodeIgniter()
#2 C:\xampp\htdocs\New folder\schoolquestionbank\school\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#3 {main}
[Method: GET, Route: /]
in SYSTEMPATH\I18n\TimeTrait.php on line 76.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
CRITICAL - 2025-07-17 12:02:38 --> Error: Class "Locale" not found
[Method: GET, Route: /]
in SYSTEMPATH\CodeIgniter.php on line 189.
 1 SYSTEMPATH\Boot.php(321): CodeIgniter\CodeIgniter->initialize()
 2 SYSTEMPATH\Boot.php(101): CodeIgniter\Boot::initializeCodeIgniter()
 3 ROOTPATH\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
CRITICAL - 2025-07-17 12:02:38 --> ErrorException: Uncaught Error: Class "Locale" not found in C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\I18n\TimeTrait.php:76
Stack trace:
#0 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\I18n\TimeTrait.php(117): CodeIgniter\I18n\Time->__construct(NULL, NULL, NULL)
#1 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\HTTP\ResponseTrait.php(398): CodeIgniter\I18n\Time::now()
#2 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\HTTP\ResponseTrait.php(376): CodeIgniter\HTTP\Response->sendHeaders()
#3 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\Debug\ExceptionHandler.php(85): CodeIgniter\HTTP\Response->send()
#4 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\Debug\Exceptions.php(162): CodeIgniter\Debug\ExceptionHandler->handle(Object(Error), Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), 500, 1)
#5 [internal function]: CodeIgniter\Debug\Exceptions->exceptionHandler(Object(Error))
#6 {main}
  thrown
【Previous Exception】
Error
Class "Locale" not found
#0 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\Boot.php(321): CodeIgniter\CodeIgniter->initialize()
#1 C:\xampp\htdocs\New folder\schoolquestionbank\school\vendor\codeigniter4\framework\system\Boot.php(101): CodeIgniter\Boot::initializeCodeIgniter()
#2 C:\xampp\htdocs\New folder\schoolquestionbank\school\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#3 {main}
[Method: GET, Route: /]
in SYSTEMPATH\I18n\TimeTrait.php on line 76.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
CRITICAL - 2025-07-17 12:11:02 --> Error: Class "Locale" not found
[Method: GET, Route: /]
in SYSTEMPATH\CodeIgniter.php on line 189.
 1 SYSTEMPATH\Boot.php(321): CodeIgniter\CodeIgniter->initialize()
 2 SYSTEMPATH\Boot.php(101): CodeIgniter\Boot::initializeCodeIgniter()
 3 ROOTPATH\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
CRITICAL - 2025-07-17 12:11:02 --> ErrorException: Uncaught Error: Class "Locale" not found in C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\I18n\TimeTrait.php:76
Stack trace:
#0 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\I18n\TimeTrait.php(117): CodeIgniter\I18n\Time->__construct(NULL, NULL, NULL)
#1 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\HTTP\ResponseTrait.php(398): CodeIgniter\I18n\Time::now()
#2 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\HTTP\ResponseTrait.php(376): CodeIgniter\HTTP\Response->sendHeaders()
#3 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\Debug\ExceptionHandler.php(85): CodeIgniter\HTTP\Response->send()
#4 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\Debug\Exceptions.php(162): CodeIgniter\Debug\ExceptionHandler->handle(Object(Error), Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), 500, 1)
#5 [internal function]: CodeIgniter\Debug\Exceptions->exceptionHandler(Object(Error))
#6 {main}
  thrown
【Previous Exception】
Error
Class "Locale" not found
#0 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\Boot.php(321): CodeIgniter\CodeIgniter->initialize()
#1 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\Boot.php(101): CodeIgniter\Boot::initializeCodeIgniter()
#2 C:\xampp\htdocs\schoolquestionbank\school\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#3 {main}
[Method: GET, Route: /]
in SYSTEMPATH\I18n\TimeTrait.php on line 76.
 1 [internal function]: CodeIgniter\Debug\Exceptions->shutdownHandler()
ERROR - 2025-07-17 10:14:22 --> mysqli_sql_exception: Table 'school.schools' doesn't exist in engine in C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *\nFROM `...', 0)
#1 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *\nFROM `...')
#2 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *\nFROM `...')
#3 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
#5 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\BaseModel.php(679): CodeIgniter\Model->doFindAll(0, 0)
#6 C:\xampp\htdocs\schoolquestionbank\school\app\Controllers\superadmin.php(22): CodeIgniter\BaseModel->findAll()
#7 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\CodeIgniter.php(933): App\Controllers\SuperAdmin->dashboard()
#8 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\SuperAdmin))
#9 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\Boot.php(334): CodeIgniter\CodeIgniter->run()
#11 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 C:\xampp\htdocs\schoolquestionbank\school\public\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\rewrite.php(44): require_once('C:\\xampp\\htdocs...')
#14 {main}
CRITICAL - 2025-07-17 10:14:22 --> CodeIgniter\Database\Exceptions\DatabaseException: Table 'school.schools' doesn't exist in engine
[Method: GET, Route: superadmin/dashboard]
in SYSTEMPATH\Database\BaseConnection.php on line 692.
 1 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `schools`
WHERE `schools`.`deleted_at` IS NULL', [], false)
 2 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 3 SYSTEMPATH\BaseModel.php(679): CodeIgniter\Model->doFindAll(0, 0)
 4 APPPATH\Controllers\superadmin.php(22): CodeIgniter\BaseModel->findAll()
 5 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\SuperAdmin->dashboard()
 6 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\SuperAdmin))
 7 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 8 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 9 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
10 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
11 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
CRITICAL - 2025-07-17 10:14:22 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Table 'school.schools' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 332.
 1 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `schools`
WHERE `schools`.`deleted_at` IS NULL')
 2 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `schools`
WHERE `schools`.`deleted_at` IS NULL')
 3 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `schools`
WHERE `schools`.`deleted_at` IS NULL', [], false)
 4 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 5 SYSTEMPATH\BaseModel.php(679): CodeIgniter\Model->doFindAll(0, 0)
 6 APPPATH\Controllers\superadmin.php(22): CodeIgniter\BaseModel->findAll()
 7 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\SuperAdmin->dashboard()
 8 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\SuperAdmin))
 9 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
10 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
11 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
12 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
13 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
CRITICAL - 2025-07-17 10:14:22 --> [Caused by] mysqli_sql_exception: Table 'school.schools' doesn't exist in engine
in SYSTEMPATH\Database\MySQLi\Connection.php on line 327.
 1 SYSTEMPATH\Database\MySQLi\Connection.php(327): mysqli->query('SELECT *
FROM `schools`
WHERE `schools`.`deleted_at` IS NULL', 0)
 2 SYSTEMPATH\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('SELECT *
FROM `schools`
WHERE `schools`.`deleted_at` IS NULL')
 3 SYSTEMPATH\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('SELECT *
FROM `schools`
WHERE `schools`.`deleted_at` IS NULL')
 4 SYSTEMPATH\Database\BaseBuilder.php(1649): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `schools`
WHERE `schools`.`deleted_at` IS NULL', [], false)
 5 SYSTEMPATH\Model.php(286): CodeIgniter\Database\BaseBuilder->get()
 6 SYSTEMPATH\BaseModel.php(679): CodeIgniter\Model->doFindAll(0, 0)
 7 APPPATH\Controllers\superadmin.php(22): CodeIgniter\BaseModel->findAll()
 8 SYSTEMPATH\CodeIgniter.php(933): App\Controllers\SuperAdmin->dashboard()
 9 SYSTEMPATH\CodeIgniter.php(507): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\SuperAdmin))
10 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
11 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
13 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
14 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:20:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:20:38 --> [DEPRECATED] Creation of dynamic property App\Controllers\Staff::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:20:38 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:20:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:20:39 --> [DEPRECATED] Creation of dynamic property App\Controllers\Staff::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:20:39 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:20:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:20:43 --> [DEPRECATED] Creation of dynamic property App\Controllers\Landing::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:20:43 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:20:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:20:44 --> [DEPRECATED] Creation of dynamic property App\Controllers\Landing::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:20:44 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:20:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:20:46 --> [DEPRECATED] Creation of dynamic property App\Controllers\Landing::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:20:46 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:20:46 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:20:46 --> [DEPRECATED] Creation of dynamic property App\Controllers\Landing::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:20:46 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:20:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:20:49 --> [DEPRECATED] Creation of dynamic property App\Controllers\Landing::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:20:49 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:20:49 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:20:49 --> [DEPRECATED] Creation of dynamic property App\Controllers\Landing::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:20:49 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:20:56 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:20:56 --> [DEPRECATED] Creation of dynamic property App\Controllers\Landing::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:20:56 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:20:57 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:20:57 --> [DEPRECATED] Creation of dynamic property App\Controllers\Landing::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:20:57 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:20:59 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:20:59 --> [DEPRECATED] Creation of dynamic property App\Controllers\Landing::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:20:59 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:21:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:21:21 --> [DEPRECATED] Creation of dynamic property App\Controllers\SchoolController::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:21:21 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:21:21 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:21:21 --> [DEPRECATED] Creation of dynamic property App\Controllers\SchoolController::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:21:21 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:21:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:21:22 --> [DEPRECATED] Creation of dynamic property App\Controllers\Register::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:21:22 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:22:04 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:22:04 --> [DEPRECATED] Creation of dynamic property App\Controllers\Register::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:22:04 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:22:04 --> User OTP: 349145, Session OTP: 349145
DEBUG - 2025-07-17 15:22:04 --> User Email: <EMAIL>, Session Email: <EMAIL>
DEBUG - 2025-07-17 15:23:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:23:05 --> [DEPRECATED] Creation of dynamic property App\Controllers\SchoolController::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:23:05 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:23:05 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:23:05 --> [DEPRECATED] Creation of dynamic property App\Controllers\Landing::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:23:05 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:23:39 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:23:39 --> [DEPRECATED] Creation of dynamic property App\Controllers\Auth::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:23:39 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:23:40 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:23:40 --> [DEPRECATED] Creation of dynamic property App\Controllers\SuperAdmin::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:23:40 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:23:45 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:23:45 --> [DEPRECATED] Creation of dynamic property App\Controllers\SchoolController::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:23:45 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:27:48 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:27:48 --> [DEPRECATED] Creation of dynamic property App\Controllers\SuperAdmin::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:27:48 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:27:53 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:27:53 --> [DEPRECATED] Creation of dynamic property App\Controllers\SchoolController::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:27:53 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:28:12 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:28:12 --> [DEPRECATED] Creation of dynamic property App\Controllers\SuperAdmin::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:28:12 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:28:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:28:15 --> [DEPRECATED] Creation of dynamic property App\Controllers\SchoolController::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:28:15 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:28:19 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:28:19 --> [DEPRECATED] Creation of dynamic property App\Controllers\SuperAdmin::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:28:19 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:31:06 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:31:06 --> [DEPRECATED] Creation of dynamic property App\Controllers\SuperAdmin::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:31:06 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:31:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:31:08 --> [DEPRECATED] Creation of dynamic property App\Controllers\SuperAdmin::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:31:08 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:32:58 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:32:58 --> [DEPRECATED] Creation of dynamic property App\Controllers\SuperAdmin::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:32:58 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:33:00 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:33:00 --> [DEPRECATED] Creation of dynamic property App\Controllers\SuperAdmin::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:33:00 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:33:20 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:33:20 --> [DEPRECATED] Creation of dynamic property App\Controllers\SuperAdmin::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:33:20 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:33:22 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:33:22 --> [DEPRECATED] Creation of dynamic property App\Controllers\SuperAdmin::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:33:22 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:34:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:34:38 --> [DEPRECATED] Creation of dynamic property App\Controllers\SuperAdmin::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:34:38 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:34:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:34:41 --> [DEPRECATED] Creation of dynamic property App\Controllers\SchoolController::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:34:41 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:38:43 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:38:43 --> [DEPRECATED] Creation of dynamic property App\Controllers\SuperAdmin::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:38:43 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:41:41 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:41:41 --> [DEPRECATED] Creation of dynamic property App\Controllers\SuperAdmin::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:41:41 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
ERROR - 2025-07-17 15:48:57 --> mysqli_sql_exception: Duplicate column name 'academic_year' in C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php:327
Stack trace:
#0 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\Database\MySQLi\Connection.php(327): mysqli->query('ALTER TABLE `qu...', 0)
#1 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\Database\BaseConnection.php(738): CodeIgniter\Database\MySQLi\Connection->execute('ALTER TABLE `qu...')
#2 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\Database\BaseConnection.php(652): CodeIgniter\Database\BaseConnection->simpleQuery('ALTER TABLE `qu...')
#3 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\Database\Forge.php(772): CodeIgniter\Database\BaseConnection->query('ALTER TABLE `qu...')
#4 C:\xampp\htdocs\schoolquestionbank\school\app\Database\Migrations\2025-07-16-160400_AddQuestionPaperFields.php(37): CodeIgniter\Database\Forge->addColumn('question_papers', Array)
#5 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(876): App\Database\Migrations\AddQuestionPaperFields->up()
#6 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\Database\MigrationRunner.php(182): CodeIgniter\Database\MigrationRunner->migrate('up', Object(stdClass))
#7 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\Commands\Database\Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest(NULL)
#8 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\CLI\Commands.php(74): CodeIgniter\Commands\Database\Migrate->run(Array)
#9 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\CLI\Console.php(47): CodeIgniter\CLI\Commands->run('migrate', Array)
#10 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\Boot.php(360): CodeIgniter\CLI\Console->run()
#11 C:\xampp\htdocs\schoolquestionbank\school\vendor\codeigniter4\framework\system\Boot.php(104): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
#12 C:\xampp\htdocs\schoolquestionbank\school\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
#13 {main}
CRITICAL - 2025-07-17 15:53:15 --> Error: Call to undefined method CodeIgniter\HTTP\CLIRequest::getUserAgent()
[Method: CLI, Route: test:audit]
in APPPATH\Models\AuditLogModel.php on line 256.
 1 APPPATH\Models\AuditLogModel.php(59): App\Models\AuditLogModel->getUserAgent()
 2 APPPATH\Services\AuditLogger.php(76): App\Models\AuditLogModel->logAction([...])
 3 APPPATH\Commands\TestAuditLogs.php(31): App\Services\AuditLogger->logCreate('test_entity', 123, 'Test Entity Name', [...], 1, 1, 'Test audit log entry from CLI')
 4 SYSTEMPATH\CLI\Commands.php(74): App\Commands\TestAuditLogs->run([])
 5 SYSTEMPATH\CLI\Console.php(47): CodeIgniter\CLI\Commands->run('test:audit', [])
 6 SYSTEMPATH\Boot.php(360): CodeIgniter\CLI\Console->run()
 7 SYSTEMPATH\Boot.php(104): CodeIgniter\Boot::runCommand(Object(CodeIgniter\CLI\Console))
 8 ROOTPATH\spark(87): CodeIgniter\Boot::bootSpark(Object(Config\Paths))
DEBUG - 2025-07-17 15:54:14 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:54:14 --> [DEPRECATED] Creation of dynamic property App\Controllers\SuperAdmin::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:54:14 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:54:15 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:54:15 --> [DEPRECATED] Creation of dynamic property App\Controllers\SuperAdmin::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:54:15 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:54:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:54:16 --> [DEPRECATED] Creation of dynamic property App\Controllers\SuperAdmin::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:54:16 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:54:16 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:54:16 --> [DEPRECATED] Creation of dynamic property App\Controllers\SuperAdmin::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:54:16 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:55:08 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:55:08 --> [DEPRECATED] Creation of dynamic property App\Controllers\SuperAdmin::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:55:08 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:55:09 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:55:09 --> [DEPRECATED] Creation of dynamic property App\Controllers\SuperAdmin::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:55:09 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:55:10 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:55:10 --> [DEPRECATED] Creation of dynamic property App\Controllers\SuperAdmin::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:55:10 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:55:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:55:11 --> [DEPRECATED] Creation of dynamic property App\Controllers\SuperAdmin::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:55:11 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:55:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:55:11 --> [DEPRECATED] Creation of dynamic property App\Controllers\SuperAdmin::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:55:11 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:55:11 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:55:11 --> [DEPRECATED] Creation of dynamic property App\Controllers\SuperAdmin::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:55:11 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:55:27 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:55:27 --> [DEPRECATED] Creation of dynamic property App\Controllers\SuperAdmin::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:55:27 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:55:29 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:55:29 --> [DEPRECATED] Creation of dynamic property App\Controllers\SuperAdmin::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:55:29 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:55:32 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:55:32 --> [DEPRECATED] Creation of dynamic property App\Controllers\SuperAdmin::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:55:32 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:55:36 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:55:36 --> [DEPRECATED] Creation of dynamic property App\Controllers\SuperAdmin::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:55:36 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:55:38 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:55:38 --> [DEPRECATED] Creation of dynamic property App\Controllers\SuperAdmin::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:55:38 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:55:42 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:55:42 --> [DEPRECATED] Creation of dynamic property App\Controllers\SuperAdmin::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:55:42 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
DEBUG - 2025-07-17 15:55:44 --> Session: Class initialized using 'CodeIgniter\Session\Handlers\FileHandler' driver.
WARNING - 2025-07-17 15:55:44 --> [DEPRECATED] Creation of dynamic property App\Controllers\SuperAdmin::$session is deprecated in APPPATH\Controllers\BaseController.php on line 37.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
WARNING - 2025-07-17 15:55:44 --> [DEPRECATED] Creation of dynamic property CodeIgniter\HTTP\IncomingRequest::$school_id is deprecated in APPPATH\Controllers\BaseController.php on line 41.
 1 SYSTEMPATH\CodeIgniter.php(904): App\Controllers\BaseController->initController(Object(CodeIgniter\HTTP\IncomingRequest), Object(CodeIgniter\HTTP\Response), Object(CodeIgniter\Log\Logger))
 2 SYSTEMPATH\CodeIgniter.php(498): CodeIgniter\CodeIgniter->createController()
 3 SYSTEMPATH\CodeIgniter.php(354): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 4 SYSTEMPATH\Boot.php(334): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH\Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
 6 FCPATH\index.php(59): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
 7 SYSTEMPATH\rewrite.php(44): require_once('C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php')
