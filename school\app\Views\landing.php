
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuestionBank Pro - SaaS Question Management System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .feature-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .tech-badge {
            transition: all 0.3s ease;
        }

        .tech-badge:hover {
            transform: scale(1.05);
        }
    </style>
</head>

<body class="bg-gray-50">
     <!-- logout message -->
      <?php if (session()->getFlashdata('message')) : ?>
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-2 rounded mb-4 text-center">
        <?= session()->getFlashdata('message') ?>
    </div>
<?php endif; ?>

    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed w-full top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <i class="fas fa-graduation-cap text-3xl text-indigo-600 mr-3"></i>
                    <span class="text-2xl font-bold text-gray-800">QuestionBank Pro</span>
                </div>
                <div class="hidden md:flex space-x-8">
                    <a href="#features" class="text-gray-600 hover:text-indigo-600 transition duration-300">Features</a>
                    <a href="#technology" class="text-gray-600 hover:text-indigo-600 transition duration-300">Technology</a>
                    <a href="#pricing" class="text-gray-600 hover:text-indigo-600 transition duration-300">Pricing</a>
                    <a href="#contact" class="text-gray-600 hover:text-indigo-600 transition duration-300">Contact</a>
                </div>
                <div class="flex space-x-4">
                    <div class="relative inline-block text-left">
                        <button id="loginToggle"
                                class="inline-flex justify-center w-full rounded-md bg-indigo-600 text-white px-4 py-2 text-sm font-medium hover:bg-indigo-700">
                            Login
                            <svg class="ml-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none"
                                 viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>

                        <div id="loginMenu"
                             class="hidden absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded shadow-lg z-50">
                            <button onclick="openLoginModal('superadmin')"
                                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-user-shield mr-2"></i>SuperAdmin Login
                            </button>
                            <button onclick="openLoginModal('schooladmin')"
                                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-school mr-2"></i>School Admin Login
                            </button>
                            <button onclick="openLoginModal('staff')"
                                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-user-tie mr-2"></i>Staff Login
                            </button>
                        </div>
                    </div>
                    <button data-register-modal class="bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition duration-300">
    Register
</button>
                </div>
            </div>
        </div>
    </nav>
    <!-- Hero Section -->
    <section class="gradient-bg text-white pt-24 pb-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-5xl md:text-6xl font-bold mb-6">
                    Streamline Your Question Bank Management
                </h1>
                <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto opacity-90">
                    A comprehensive SaaS platform for educational institutions to create, manage, and organize
                    question banks with multi-tenant architecture and role-based access control.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button data-register-modal class="bg-white text-indigo-600 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-gray-100 transition duration-300">
                        <i class="fas fa-play mr-2"></i>Start Free Trial
                    </button>
                    <button class="border-2 border-white text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white hover:text-indigo-600 transition duration-300">
                        <i class="fas fa-info-circle mr-2"></i>Learn More
                    </button>
                </div>
            </div>
        </div>
    </section>
    <!-- Key Benefits -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-bold text-gray-800 mb-4">Why Choose QuestionBank Pro?</h2>
                <p class="text-xl text-gray-600">Built specifically for educational institutions with security and
                    scalability in mind</p>
            </div>
            <div class="grid md:grid-cols-3 gap-8">
                <div class="text-center p-6">
                    <div class="bg-indigo-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-building text-2xl text-indigo-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">Multi-Tenant Architecture</h3>
                    <p class="text-gray-600">Each school operates independently with complete data isolation and
                        customized settings.</p>
                </div>
                <div class="text-center p-6">
                    <div class="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-shield-alt text-2xl text-green-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">Role-Based Security</h3>
                    <p class="text-gray-600">Granular permissions for Super Admins, School Admins, and staff with
                        audit logging.</p>
                </div>
                <div class="text-center p-6">
                    <div class="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-question-circle text-2xl text-purple-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">Comprehensive Question Management</h3>
                    <p class="text-gray-600">Support for MCQs and descriptive questions with subject categorization
                        and difficulty levels.</p>
                </div>
            </div>
        </div>
    </section>
    <!-- Features Section -->
    <section id="features" class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-bold text-gray-800 mb-4">Powerful Features</h2>
                <p class="text-xl text-gray-600">Everything you need to manage your institution's question banks
                    effectively</p>
            </div>
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="feature-card bg-white p-6 rounded-xl shadow-md">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-users text-2xl text-indigo-600 mr-3"></i>
                        <h3 class="text-xl font-semibold">User & Role Management</h3>
                    </div>
                    <p class="text-gray-600">Dynamic permission assignment with easy user onboarding for schools and
                        staff members.</p>
                </div>
                <div class="feature-card bg-white p-6 rounded-xl shadow-md">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-book text-2xl text-green-600 mr-3"></i>
                        <h3 class="text-xl font-semibold">Subject Management</h3>
                    </div>
                    <p class="text-gray-600">Create and organize subjects within each school's scope with hierarchical
                        categorization.</p>
                </div>
                <div class="feature-card bg-white p-6 rounded-xl shadow-md">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-history text-2xl text-purple-600 mr-3"></i>
                        <h3 class="text-xl font-semibold">Audit Logging</h3>
                    </div>
                    <p class="text-gray-600">Complete tracking of user actions including create, update, and delete
                        operations for accountability.</p>
                </div>
                <div class="feature-card bg-white p-6 rounded-xl shadow-md">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-credit-card text-2xl text-blue-600 mr-3"></i>
                        <h3 class="text-xl font-semibold">Subscription Management</h3>
                    </div>
                    <p class="text-gray-600">Flexible trial and paid subscription system with automatic access control
                        based on subscription status.</p>
                </div>
                <div class="feature-card bg-white p-6 rounded-xl shadow-md">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-lock text-2xl text-red-600 mr-3"></i>
                        <h3 class="text-xl font-semibold">Secure Authentication</h3>
                    </div>
                    <p class="text-gray-600">Robust login system with session management and middleware-based access
                        control.</p>
                </div>
                <div class="feature-card bg-white p-6 rounded-xl shadow-md">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-chart-line text-2xl text-orange-600 mr-3"></i>
                        <h3 class="text-xl font-semibold">Scalable Architecture</h3>
                    </div>
                    <p class="text-gray-600">Built on CodeIgniter 4 with MySQL for high performance and scalability
                        across multiple institutions.</p>
                </div>
            </div>
        </div>
    </section>
    <!-- User Roles Section -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-bold text-gray-800 mb-4">User Roles & Permissions</h2>
                <p class="text-xl text-gray-600">Designed exclusively for internal academic content management</p>
            </div>
            <div class="grid md:grid-cols-3 gap-8">
                <div class="bg-gradient-to-br from-purple-500 to-purple-600 text-white p-8 rounded-xl">
                    <div class="text-center">
                        <i class="fas fa-crown text-4xl mb-4"></i>
                        <h3 class="text-2xl font-bold mb-4">Super Admin</h3>
                        <ul class="text-left space-y-2">
                            <li><i class="fas fa-check mr-2"></i>Manages all schools</li>
                            <li><i class="fas fa-check mr-2"></i>Global role management</li>
                            <li><i class="fas fa-check mr-2"></i>System settings control</li>
                            <li><i class="fas fa-check mr-2"></i>Subscription oversight</li>
                        </ul>
                    </div>
                </div>
                <div class="bg-gradient-to-br from-blue-500 to-blue-600 text-white p-8 rounded-xl">
                    <div class="text-center">
                        <i class="fas fa-user-tie text-4xl mb-4"></i>
                        <h3 class="text-2xl font-bold mb-4">School Admin</h3>
                        <ul class="text-left space-y-2">
                            <li><i class="fas fa-check mr-2"></i>Manages school staff</li>
                            <li><i class="fas fa-check mr-2"></i>Academic data oversight</li>
                            <li><i class="fas fa-check mr-2"></i>Subject management</li>
                            <li><i class="fas fa-check mr-2"></i>School-level reporting</li>
                        </ul>
                    </div>
                </div>
                <div class="bg-gradient-to-br from-green-500 to-green-600 text-white p-8 rounded-xl">
                    <div class="text-center">
                        <i class="fas fa-chalkboard-teacher text-4xl mb-4"></i>
                        <h3 class="text-2xl font-bold mb-4">Staff/Faculty</h3>
                        <ul class="text-left space-y-2">
                            <li><i class="fas fa-check mr-2"></i>Add & manage questions</li>
                            <li><i class="fas fa-check mr-2"></i>Subject content creation</li>
                            <li><i class="fas fa-check mr-2"></i>Question categorization</li>
                            <li><i class="fas fa-check mr-2"></i>Content collaboration</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="text-center mt-8 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                <p class="text-yellow-800 font-semibold">
                    <i class="fas fa-info-circle mr-2"></i>
                    Note: This platform is designed exclusively for internal academic content management. No student
                    access is provided.
                </p>
            </div>
        </div>
    </section>
    <!-- Technology Stack -->
    <section id="technology" class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-bold text-gray-800 mb-4">Built with Modern Technology</h2>
                <p class="text-xl text-gray-600">Reliable, secure, and scalable technology stack</p>
            </div>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                <div class="tech-badge bg-white p-6 rounded-xl shadow-md text-center">
                    <i class="fab fa-php text-4xl text-purple-600 mb-3"></i>
                    <h4 class="font-semibold">PHP</h4>
                    <p class="text-sm text-gray-600">CodeIgniter 4</p>
                </div>
                <div class="tech-badge bg-white p-6 rounded-xl shadow-md text-center">
                    <i class="fas fa-database text-4xl text-blue-600 mb-3"></i>
                    <h4 class="font-semibold">MySQL</h4>
                    <p class="text-sm text-gray-600">Database</p>
                </div>
                <div class="tech-badge bg-white p-6 rounded-xl shadow-md text-center">
                    <i class="fab fa-bootstrap text-4xl text-purple-500 mb-3"></i>
                    <h4 class="font-semibold">Bootstrap</h4>
                    <p class="text-sm text-gray-600">Frontend</p>
                </div>
                <div class="tech-badge bg-white p-6 rounded-xl shadow-md text-center">
                    <i class="fab fa-git-alt text-4xl text-orange-600 mb-3"></i>
                    <h4 class="font-semibold">Git</h4>
                    <p class="text-sm text-gray-600">Version Control</p>
                </div>
            </div>
        </div>
    </section>
    <!-- Pricing Section -->
    <section id="pricing" class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-bold text-gray-800 mb-4">Simple, Transparent Pricing</h2>
                <p class="text-xl text-gray-600">Start with a free trial, upgrade when you're ready</p>
            </div>
            <div class="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
                <div class="bg-gray-50 p-8 rounded-xl border-2 border-gray-200">
                    <div class="text-center">
                        <h3 class="text-2xl font-bold mb-4">Free Trial</h3>
                        <div class="text-4xl font-bold text-gray-800 mb-2">₹0</div>
                        <p class="text-gray-600 mb-6">30 days free trial</p>
                        <ul class="text-left space-y-3 mb-8">
                            <li><i class="fas fa-check text-green-500 mr-2"></i>Up to 50 questions</li>
                            <li><i class="fas fa-check text-green-500 mr-2"></i>5 subjects maximum</li>
                            <li><i class="fas fa-check text-green-500 mr-2"></i>5 users maximum</li>
                            <li><i class="fas fa-check text-green-500 mr-2"></i>Basic printing</li>
                            <li><i class="fas fa-check text-green-500 mr-2"></i>Email support</li>
                        </ul>
                        <button data-register-modal data-plan="free" class="w-full bg-gray-600 text-white py-3 rounded-lg hover:bg-gray-700 transition duration-300">
                            Start Free Trial
                        </button>
                    </div>
                </div>
                <div class="bg-indigo-50 p-8 rounded-xl border-2 border-indigo-500 relative">
                    <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                        <span class="bg-indigo-500 text-white px-4 py-1 rounded-full text-sm font-semibold">Most Popular</span>
                    </div>
                    <div class="text-center">
                        <h3 class="text-2xl font-bold mb-4">Professional</h3>
                        <div class="text-4xl font-bold text-indigo-600 mb-2">₹999</div>
                        <p class="text-gray-600 mb-6">per school/month</p>
                        <ul class="text-left space-y-3 mb-8">
                            <li><i class="fas fa-check text-green-500 mr-2"></i>Unlimited questions</li>
                            <li><i class="fas fa-check text-green-500 mr-2"></i>Unlimited subjects</li>
                            <li><i class="fas fa-check text-green-500 mr-2"></i>Up to 50 users</li>
                            <li><i class="fas fa-check text-green-500 mr-2"></i>Export & Print</li>
                            <li><i class="fas fa-check text-green-500 mr-2"></i>Audit logging</li>
                            <li><i class="fas fa-check text-green-500 mr-2"></i>Priority support</li>
                        </ul>
                        <button data-register-modal data-plan="professional" class="w-full bg-indigo-600 text-white py-3 rounded-lg hover:bg-indigo-700 transition duration-300">
                            Get Started
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- CTA Section -->
    <section class="gradient-bg text-white py-16">
        <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
            <h2 class="text-4xl font-bold mb-6">Ready to Transform Your Question Bank Management?</h2>
            <p class="text-xl mb-8 opacity-90">Join educational institutions worldwide who trust QuestionBank Pro for
                their academic content management needs.</p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button data-register-modal class="bg-white text-indigo-600 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-gray-100 transition duration-300">
                    <i class="fas fa-rocket mr-2"></i>Start Your Free Trial
                </button>
                <button class="border-2 border-white text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white hover:text-indigo-600 transition duration-300">
                    <i class="fas fa-calendar mr-2"></i>Schedule Demo
                </button>
            </div>
        </div>
    </section>
    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center mb-4">
                        <i class="fas fa-graduation-cap text-2xl text-indigo-400 mr-2"></i>
                        <span class="text-xl font-bold">QuestionBank Pro</span>
                    </div>
                    <p class="text-gray-400">Streamlining question bank management for educational institutions
                        worldwide.</p>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4">Product</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white transition duration-300">Features</a></li>
                        <li><a href="#" class="hover:text-white transition duration-300">Pricing</a></li>
                        <li><a href="#" class="hover:text-white transition duration-300">Security</a></li>
                        <li><a href="#" class="hover:text-white transition duration-300">API</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4">Support</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white transition duration-300">Documentation</a></li>
                        <li><a href="#" class="hover:text-white transition duration-300">Help Center</a></li>
                        <li><a href="#" class="hover:text-white transition duration-300">Contact Us</a></li>
                        <li><a href="#" class="hover:text-white transition duration-300">Status</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4">Connect</h4>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition duration-300">
                            <i class="fab fa-twitter text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition duration-300">
                            <i class="fab fa-linkedin text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition duration-300">
                            <i class="fab fa-github text-xl"></i>
                        </a>
                    </div>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 QuestionBank Pro. All rights reserved. Built with CodeIgniter 4.</p>
            </div>
        </div>
    </footer>
        <!-- Registration Modal -->
<div id="registrationModal" class="fixed inset-0 z-50 hidden flex items-center justify-center p-4 bg-black bg-opacity-50">
    <div class="w-full max-w-2xl bg-white rounded-xl shadow-lg overflow-hidden max-h-[90vh] overflow-y-auto">
        <!-- Modal Header -->
        <div class="bg-gray-50 px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 class="text-xl font-bold text-indigo-800">Register Your School</h2>
            <button id="closeModalBtn" class="text-gray-500 hover:text-gray-700">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <!-- Modal Content - iframe loading your existing registration page -->
        <div class="h-[80vh]">
            <iframe id="registrationIframe" src="<?= base_url('register') ?>" class="w-full h-full border-0"></iframe>
        </div>
    </div>
</div>

<!-- Login Modal -->
<div id="loginModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <!-- Modal Header -->
        <div class="flex justify-between items-center p-6 border-b">
            <h2 id="loginModalTitle" class="text-xl font-semibold text-gray-800">Login</h2>
            <button id="closeLoginModalBtn" class="text-gray-400 hover:text-gray-600 text-2xl font-bold">
                &times;
            </button>
        </div>

        <!-- Modal Content -->
        <div class="p-6">
            <!-- SuperAdmin Login Form -->
            <div id="superadminLoginForm" class="hidden">
                <div class="text-center mb-6">
                    <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-bold text-gray-800">SuperAdmin Access</h3>
                    <p class="text-gray-500 text-sm">System administration portal</p>
                </div>

                <form id="superadminForm" method="post" action="<?= site_url('auth/login') ?>" class="space-y-5">
                    <input type="hidden" name="role" value="superadmin">

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <div class="relative">
                            <input type="email" name="email" placeholder="<EMAIL>" required
                                   class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-red-500 focus:border-red-500">
                            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8" />
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                        <div class="relative">
                            <input type="password" name="password" placeholder="••••••••" required
                                   class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-red-500 focus:border-red-500">
                            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                </svg>
                            </div>
                        </div>
                    </div>

                    <button type="submit"
                            class="w-full bg-red-600 text-white py-3 rounded-lg font-medium hover:bg-red-700 transition duration-300">
                        Sign In as SuperAdmin
                    </button>
                </form>
            </div>

            <!-- SchoolAdmin Login Form -->
            <div id="schooladminLoginForm" class="hidden">
                <div class="text-center mb-6">
                    <div class="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-bold text-gray-800">SchoolAdmin Access</h3>
                    <p class="text-gray-500 text-sm">Manage your school's content</p>
                </div>

                <form id="schooladminForm" method="post" action="<?= site_url('auth/login') ?>" class="space-y-5">
                    <input type="hidden" name="role" value="schooladmin">

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <div class="relative">
                            <input type="email" name="email" placeholder="<EMAIL>" required
                                   class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8" />
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                        <div class="relative">
                            <input type="password" name="password" placeholder="••••••••" required
                                   class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                </svg>
                            </div>
                        </div>
                    </div>

                    <button type="submit"
                            class="w-full bg-indigo-600 text-white py-3 rounded-lg font-medium hover:bg-indigo-700 transition duration-300">
                        Sign In as SchoolAdmin
                    </button>
                </form>
            </div>

            <!-- Staff Login Form -->
            <div id="staffLoginForm" class="hidden">
                <div class="text-center mb-6">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-bold text-gray-800">Staff Access</h3>
                    <p class="text-gray-500 text-sm">Access your question bank account</p>
                </div>

                <form id="staffForm" method="post" action="<?= site_url('staff/authenticate') ?>" class="space-y-5">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <div class="relative">
                            <input type="email" name="email" placeholder="<EMAIL>" required
                                   class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500">
                            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8" />
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                        <div class="relative">
                            <input type="password" name="password" placeholder="••••••••" required
                                   class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-green-500 focus:border-green-500">
                            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="flex items-center justify-between">
                        <label class="flex items-center">
                            <input type="checkbox" class="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50">
                            <span class="ml-2 text-sm text-gray-600">Remember me</span>
                        </label>
                        <a href="#" class="text-sm text-green-600 hover:text-green-500">Forgot password?</a>
                    </div>

                    <button type="submit"
                            class="w-full bg-green-600 text-white py-3 rounded-lg font-medium hover:bg-green-700 transition duration-300">
                        <i class="fas fa-sign-in-alt mr-2"></i>Sign In as Staff
                    </button>
                </form>
            </div>

            <!-- Error/Success Messages -->
            <div id="loginMessages" class="mt-4"></div>
        </div>
    </div>
</div>

    <!-- Logout Success Popup -->
    <div id="logoutPopup" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6 text-center">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">Logged Out Successfully</h3>
            <p class="text-gray-600 mb-6">You have been securely logged out of your account.</p>
            <button id="closeLogoutPopup" class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition duration-300">
                OK
            </button>
        </div>
    </div>

    <script>
        // Check for logout message and show popup
        <?php if (session()->getFlashdata('logout_success')): ?>
        document.addEventListener('DOMContentLoaded', function() {
            showLogoutPopup();
        });
        <?php endif; ?>

        function showLogoutPopup() {
            const popup = document.getElementById('logoutPopup');
            popup.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        // Close logout popup
        document.getElementById('closeLogoutPopup').addEventListener('click', function() {
            const popup = document.getElementById('logoutPopup');
            popup.classList.add('hidden');
            document.body.style.overflow = 'auto';
        });

        // Close popup when clicking outside
        document.getElementById('logoutPopup').addEventListener('click', function(e) {
            if (e.target === this) {
                this.classList.add('hidden');
                document.body.style.overflow = 'auto';
            }
        });

        // Close popup with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const popup = document.getElementById('logoutPopup');
                if (!popup.classList.contains('hidden')) {
                    popup.classList.add('hidden');
                    document.body.style.overflow = 'auto';
                }
            }
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        // Add scroll effect to navigation
        window.addEventListener('scroll', function () {
            const nav = document.querySelector('nav');
            if (window.scrollY > 100) {
                nav.classList.add('bg-white/95', 'backdrop-blur-sm');
            } else {
                nav.classList.remove('bg-white/95', 'backdrop-blur-sm');
            }
        });
        // Button click handlers
        document.querySelectorAll('button').forEach(button => {
            button.addEventListener('click', function () {
                if (this.textContent.includes('Schedule Demo')) {
                    alert('Demo scheduling would open calendar booking system!');
                } else if (this.textContent.includes('Learn More')) {
                    document.querySelector('#features').scrollIntoView({ behavior: 'smooth' });
                }
            });
        });


        // Login dropdown functionality
        document.addEventListener('click', function (event) {
            const toggle = document.getElementById('loginToggle');
            const menu = document.getElementById('loginMenu');

            if (toggle.contains(event.target)) {
                menu.classList.toggle('hidden');
            } else {
                menu.classList.add('hidden');
            }
        });

        // Login Modal functionality
        const loginModal = document.getElementById('loginModal');
        const closeLoginModalBtn = document.getElementById('closeLoginModalBtn');
        const superadminForm = document.getElementById('superadminLoginForm');
        const schooladminForm = document.getElementById('schooladminLoginForm');
        const staffForm = document.getElementById('staffLoginForm');
        const loginModalTitle = document.getElementById('loginModalTitle');

        function openLoginModal(type) {
            // Hide dropdown menu
            document.getElementById('loginMenu').classList.add('hidden');

            // Show modal
            loginModal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';

            // Hide all forms first
            superadminForm.classList.add('hidden');
            schooladminForm.classList.add('hidden');
            staffForm.classList.add('hidden');

            // Show appropriate form
            if (type === 'superadmin') {
                superadminForm.classList.remove('hidden');
                loginModalTitle.textContent = 'SuperAdmin Login';
            } else if (type === 'schooladmin') {
                schooladminForm.classList.remove('hidden');
                loginModalTitle.textContent = 'School Admin Login';
            } else if (type === 'staff') {
                staffForm.classList.remove('hidden');
                loginModalTitle.textContent = 'Staff Login';
            }
        }

        // Close login modal
        closeLoginModalBtn.addEventListener('click', function() {
            loginModal.classList.add('hidden');
            document.body.style.overflow = 'auto';
            // Clear any error messages
            document.getElementById('loginMessages').innerHTML = '';
        });

        // Close modal when clicking outside
        loginModal.addEventListener('click', function(e) {
            if (e.target === loginModal) {
                loginModal.classList.add('hidden');
                document.body.style.overflow = 'auto';
                document.getElementById('loginMessages').innerHTML = '';
            }
        });

        // Handle login form submissions with AJAX
        document.getElementById('superadminForm').addEventListener('submit', function(e) {
            e.preventDefault();
            handleLoginSubmit(this);
        });

        document.getElementById('schooladminForm').addEventListener('submit', function(e) {
            e.preventDefault();
            handleLoginSubmit(this);
        });

        document.getElementById('staffForm').addEventListener('submit', function(e) {
            e.preventDefault();
            handleStaffLoginSubmit(this);
        });

        function handleLoginSubmit(form) {
            const formData = new FormData(form);
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;

            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Signing in...';

            // Clear previous messages
            document.getElementById('loginMessages').innerHTML = '';

            fetch('<?= site_url('auth/login') ?>', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Successful login - redirect to dashboard
                    showLoginMessage('Login successful! Redirecting...', 'success');
                    setTimeout(() => {
                        window.location.href = data.redirect;
                    }, 1000);
                } else {
                    // Show error message
                    showLoginMessage(data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showLoginMessage('An error occurred. Please try again.', 'error');
            })
            .finally(() => {
                // Reset button state
                submitBtn.disabled = false;
                submitBtn.textContent = originalText;
            });
        }

        function handleStaffLoginSubmit(form) {
            const formData = new FormData(form);
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Signing in...';

            // Clear previous messages
            document.getElementById('loginMessages').innerHTML = '';

            fetch('<?= site_url('staff/authenticate') ?>', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Successful login - redirect to staff dashboard
                    showLoginMessage('Login successful! Redirecting...', 'success');
                    setTimeout(() => {
                        window.location.href = data.redirect;
                    }, 1000);
                } else {
                    // Show error message
                    showLoginMessage(data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showLoginMessage('An error occurred. Please try again.', 'error');
            })
            .finally(() => {
                // Reset button state
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            });
        }

        function showLoginMessage(message, type) {
            const messagesDiv = document.getElementById('loginMessages');
            const alertClass = type === 'error' ? 'bg-red-100 text-red-800 border-red-200' : 'bg-green-100 text-green-800 border-green-200';
            messagesDiv.innerHTML = `
                <div class="p-3 rounded border ${alertClass}">
                    ${message}
                </div>
            `;
        }

        // Registration Modal functionality
        const modal = document.getElementById('registrationModal');
        const openModalBtns = document.querySelectorAll('[data-register-modal]');
        const closeModalBtn = document.getElementById('closeModalBtn');
        const registrationIframe = document.getElementById('registrationIframe');

// Open modal when any register button is clicked
openModalBtns.forEach(btn => {
    btn.addEventListener('click', function() {
        const selectedPlan = this.getAttribute('data-plan') || 'free';

        // Store selected plan in session storage
        sessionStorage.setItem('selectedPlan', selectedPlan);

        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden'; // Prevent scrolling

        // Reset iframe source with plan parameter
        const baseUrl = registrationIframe.src.split('?')[0];
        registrationIframe.src = baseUrl + '?plan=' + selectedPlan;
    });
});

// Close modal when X button is clicked
closeModalBtn.addEventListener('click', function() {
    modal.classList.add('hidden');
    document.body.style.overflow = ''; // Re-enable scrolling
});

// Close modal when clicking outside the modal content
modal.addEventListener('click', function(e) {
    if (e.target === modal) {
        modal.classList.add('hidden');
        document.body.style.overflow = ''; // Re-enable scrolling
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
        modal.classList.add('hidden');
        document.body.style.overflow = ''; // Re-enable scrolling
    }
});

// Listen for messages from the iframe (optional - for closing modal after successful registration)
window.addEventListener('message', function(e) {
    if (e.data === 'registrationSuccess') {
        modal.classList.add('hidden');
        document.body.style.overflow = ''; // Re-enable scrolling
    }
});
    </script>
</body>
</html>