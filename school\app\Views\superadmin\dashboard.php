<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?= csrf_token() ?>">
    <title>Super Admin Dashboard QuestionBank Pro</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .sidebar-transition {
            transition: transform 0.3s ease-in-out;
        }

        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        /* Error styling for form inputs */
        .border-red-500 {
            border-color: #ef4444 !important;
            box-shadow: 0 0 0 1px #ef4444;
        }

        .error-message {
            font-size: 0.875rem;
            line-height: 1.25rem;
        }

        /* Modal animations */
        .modal-enter {
            animation: modalEnter 0.3s ease-out;
        }

        @keyframes modalEnter {
            from {
                opacity: 0;
                transform: scale(0.95);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .status-active {
            background-color: rgb(220 252 231);
            color: rgb(22 101 52);
        }

        .status-trial {
            background-color: rgb(254 249 195);
            color: rgb(133 77 14);
        }

        .status-expired {
            background-color: rgb(254 226 226);
            color: rgb(153 27 27);
        }

        .status-suspended {
            background-color: rgb(243 244 246);
            color: rgb(55 65 81);
        }

        .section {
            display: none;
        }

        .section.active {
            display: block;
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>

<body class="bg-gray-50">
    <!-- Sidebar -->
    <div id="sidebar" class="fixed left-0 top-0 h-full w-64 bg-white shadow-lg z-40 sidebar-transition">
        <div class="p-6 border-b">
            <div class="flex items-center">
                <i class="fas fa-graduation-cap text-2xl text-indigo-600 mr-3"></i>
                <div>
                    <h2 class="text-lg font-bold text-gray-800">Question Bank Pro</h2>
                    <p class="text-sm text-gray-500">Super Admin</p>
                </div>
            </div>
        </div>
        <nav class="mt-6">
            <a href="#" onclick="showSection('dashboard')"
                class="nav-item active flex items-center px-6 py-3 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 transition duration-200">
                <i class="fas fa-tachometer-alt mr-3"></i>
                Dashboard
            </a>
            <a href="#" onclick="showSection('schools')"
                class="nav-item flex items-center px-6 py-3 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 transition duration-200">
                <i class="fas fa-school mr-3"></i>
                Schools Management
            </a>
            <a href="#" onclick="showSection('users')"
                class="nav-item flex items-center px-6 py-3 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 transition duration-200">
                <i class="fas fa-users mr-3"></i>
                User Management
            </a>
            <a href="#" onclick="showSection('subscriptions')"
                class="nav-item flex items-center px-6 py-3 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 transition duration-200">
                <i class="fas fa-credit-card mr-3"></i>
                Subscriptions
            </a>
            <a href="#" onclick="showSection('analytics')"
                class="nav-item flex items-center px-6 py-3 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 transition duration-200">
                <i class="fas fa-chart-bar mr-3"></i>
                Analytics
            </a>
            <a href="#" onclick="showSection('system')"
                class="nav-item flex items-center px-6 py-3 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 transition duration-200">
                <i class="fas fa-cog mr-3"></i>
                System Settings
            </a>
            <a href="#" onclick="showSection('audit')"
                class="nav-item flex items-center px-6 py-3 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 transition duration-200">
                <i class="fas fa-history mr-3"></i>
                Audit Logs
            </a>
        </nav>
        <div class="absolute bottom-0 w-full p-6 border-t">
            <div class="flex items-center">
                <div
                    class="w-8 h-8 bg-indigo-600 rounded-full flex items-center justify-center text-white font-semibold">
                    SA</div>
                <div class="ml-3">
                    <p class="text-sm font-semibold text-gray-800">Super Admin</p>
                    <p class="text-xs text-gray-500"><EMAIL></p>
                </div>
            </div>
            <a href="<?= site_url('logout') ?>" class="mt-3 w-full bg-red-600 text-white py-2 rounded-lg hover:bg-red-700 transition duration-200 inline-block text-center">
                <i class="fas fa-sign-out-alt mr-2"></i>Logout
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="ml-64">
        <!-- Top Header -->
        <header class="bg-white shadow-sm border-b px-6 py-4">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800" id="pageTitle">Dashboard Overview</h1>
                    <p class="text-gray-600" id="pageSubtitle">Welcome back, manage your entire platform from here</p>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <button class="p-2 text-gray-400 hover:text-gray-600 relative">
                            <i class="fas fa-bell text-xl"></i>
                            <span
                                class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">3</span>
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Dashboard Content -->
        <main class="p-6">
            <!-- Dashboard Section -->
            <div id="dashboard-section" class="section active">
                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="card-hover bg-white p-6 rounded-xl shadow-md">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-500 text-sm">Total Schools</p>
                                <p class="text-3xl font-bold text-gray-800"><?= count($schools ?? []) ?></p>
                                <p class="text-green-600 text-sm mt-1">
                                    <i class="fa fa-arrow-up mr-1"></i>Registered schools
                                </p>
                            </div>
                            <div class="bg-blue-100 p-3 rounded-full">
                                <i class="fas fa-school text-2xl text-blue-600"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-hover bg-white p-6 rounded-xl shadow-md">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-500 text-sm">Active Users</p>
                                <p class="text-3xl font-bold text-gray-800"><?= $totalUsers ?? 0 ?></p>
                                <p class="text-green-600 text-sm mt-1">
                                    <i class="fa fa-arrow-up mr-1"></i>Total registered users
                                </p>
                            </div>
                            <div class="bg-green-100 p-3 rounded-full">
                                <i class="fas fa-users text-2xl text-green-600"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-hover bg-white p-6 rounded-xl shadow-md">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-500 text-sm">Total Questions</p>
                                <p class="text-3xl font-bold text-gray-800">45,892</p>
                                <p class="text-green-600 text-sm mt-1">
                                    <i class="fa fa-arrow-up mr-1"></i>15% from last month
                                </p>
                            </div>
                            <div class="bg-purple-100 p-3 rounded-full">
                                <i class="fa fa-question-circle text-2xl text-purple-600"></i>
                            </div>
                        </div>
                    </div>
                    <div class="card-hover bg-white p-6 rounded-xl shadow-md">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-500 text-sm">Monthly Revenue</p>
                                <p class="text-3xl font-bold text-gray-800">$12,094</p>
                                <p class="text-green-600 text-sm mt-1">
                                    <i class="fa fa-arrow-up mr-1"></i>23% from last month
                                </p>
                            </div>
                            <div class="bg-yellow-100 p-3 rounded-full">
                                <i class="fa fa-dollar-sign text-2xl text-yellow-600"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts Row -->
                <div class="grid lg:grid-cols-2 gap-6 mb-8">
                    <div class="bg-white p-6 rounded-xl shadow-md">
                        <h3 class="text-lg font-semibold mb-4">School Growth Trend</h3>
                        <div class="relative h-[250px]">
                            <canvas id="schoolGrowthChart"></canvas>
                        </div>
                    </div>
                    <div class="bg-white p-6 rounded-xl shadow-md">
                        <h3 class="text-lg font-semibold mb-4">Subscription Distribution</h3>
                        <div class="relative h-[250px]">
                            <canvas id="subscriptionChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="bg-white rounded-xl shadow-md">
                    <div class="p-6 border-b">
                        <h3 class="text-lg font-semibold">Recent Activity</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div class="flex items-center p-3 bg-blue-50 rounded-lg">
                                <div class="bg-blue-600 p-2 rounded-full mr-4">
                                    <i class="fas fa-school text-white"></i>
                                </div>
                                <div class="flex-1">
                                    <p class="font-semibold">New school registered</p>
                                    <p class="text-sm text-gray-600">Springfield High School joined the platform</p>
                                </div>
                                <span class="text-sm text-gray-500">2 hours ago</span>
                            </div>
                            <div class="flex items-center p-3 bg-green-50 rounded-lg">
                                <div class="bg-green-600 p-2 rounded-full mr-4">
                                    <i class="fas fa-credit-card text-white"></i>
                                </div>
                                <div class="flex-1">
                                    <p class="font-semibold">Subscription upgraded</p>
                                    <p class="text-sm text-gray-600">Lincoln Academy upgraded to Professional plan</p>
                                </div>
                                <span class="text-sm text-gray-500">4 hours ago</span>
                            </div>
                            <div class="flex items-center p-3 bg-yellow-50 rounded-lg">
                                <div class="bg-yellow-600 p-2 rounded-full mr-4">
                                    <i class="fa fa-exclamation-triangle text-white"></i>
                                </div>
                                <div class="flex-1">
                                    <p class="font-semibold">Trial expiring soon</p>
                                    <p class="text-sm text-gray-600">5 schools have trials expiring in 3 days</p>
                                </div>
                                <span class="text-sm text-gray-500">6 hours ago</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- schools-section -->
            <div id="schools-section" class="section hidden">
                <div class="bg-white rounded-xl shadow-md">
                    <div class="p-6 border-b flex justify-between items-center">
                        <div>
                            <h3 class="text-lg font-semibold">Schools Management</h3>
                            <div class="flex space-x-3 mt-2">
                                <button onclick="showSchoolTab('all')" class="px-4 py-1 rounded-lg border border-indigo-600 text-indigo-600 hover:bg-indigo-50">All Schools</button>
                                <button onclick="showSchoolTab('pending')" class="px-4 py-1 rounded-lg border border-yellow-600 text-yellow-600 hover:bg-yellow-50">Pending Approval</button>
                                <button onclick="showSchoolTab('active')" class="px-4 py-1 rounded-lg border border-green-600 text-green-600 hover:bg-green-50">Active</button>
                            </div>
                        </div>
                        <div class="flex space-x-3">
                            <input type="text" id="school-search" placeholder="Search schools..."
                                class="px-4 py-2 border rounded-lg focus:outline-none focus:border-indigo-500">
                            <button onclick="openAddSchoolModal()"
                                class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition duration-200">
                                <i class="fa fa-plus mr-2"></i>Add New School
                            </button>
                        </div>
                    </div>

                    <!-- Schools Table -->
                    <div id="schools-table" class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-4 font-semibold uppercase text-left">School Name</th>
                                    <th class="px-6 py-4 font-semibold uppercase text-left">Email</th>
                                    <th class="px-6 py-4 font-semibold uppercase text-left">Phone</th>
                                    <th class="px-6 py-4 font-semibold uppercase text-left">Address</th>
                                    <th class="px-6 py-4 font-semibold uppercase text-left">Status</th>
                                    <th class="px-6 py-4 font-semibold uppercase text-left">Registered At</th>
                                    <th class="px-6 py-4 font-semibold uppercase text-left">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="schools-tbody" class="divide-y divide-gray-200">
                                <!-- Dynamic content will be loaded here -->
                                <tr>
                                    <td colspan="7" class="px-6 py-8 text-center text-gray-500">
                                        <i class="fas fa-spinner fa-spin mr-2"></i>Loading schools...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Add School Modal -->
            <div id="addSchoolModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
                <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                    <!-- Modal Header -->
                    <div class="flex justify-between items-center p-6 border-b">
                        <h2 class="text-xl font-semibold text-gray-800">Add New School</h2>
                        <button id="closeAddSchoolModal" class="text-gray-400 hover:text-gray-600 text-2xl font-bold">
                            &times;
                        </button>
                    </div>

                    <!-- Modal Content -->
                    <div class="p-6">
                        <form id="addSchoolForm" class="space-y-6">
                            <div class="grid md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">School Name *</label>
                                    <input type="text" name="name" required
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                                    <input type="email" name="email" required
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                </div>
                            </div>

                            <div class="grid md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number *</label>
                                    <input type="tel" name="phone" required
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Subscription Plan *</label>
                                    <select name="plan_id" required
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                        <option value="">Select Plan</option>
                                        <option value="1">Free Trial (30 days)</option>
                                        <option value="2">Professional ($49/month)</option>
                                        <option value="3">Enterprise ($99/month)</option>
                                    </select>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">School Address *</label>
                                <textarea name="address" required rows="3"
                                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                          placeholder="Enter complete school address"></textarea>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Password *</label>
                                <input type="password" name="password" required minlength="8"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                       placeholder="Minimum 8 characters">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Confirm Password *</label>
                                <input type="password" name="confirm_password" required minlength="8"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                       placeholder="Re-enter password">
                            </div>

                            <!-- Status Selection -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Initial Status</label>
                                <select name="status"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                    <option value="inactive">Pending Approval</option>
                                    <option value="active">Active (Approved)</option>
                                </select>
                            </div>

                            <!-- Error/Success Messages -->
                            <div id="addSchoolMessages" class="hidden"></div>

                            <!-- Submit Button -->
                            <div class="flex justify-end space-x-3 pt-4">
                                <button type="button" onclick="closeAddSchoolModal()"
                                        class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                                    Cancel
                                </button>
                                <button type="submit"
                                        class="px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition duration-300">
                                    <i class="fas fa-plus mr-2"></i>Add School
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

          <!-- Users Management Section -->
            <div id="users-section" class="section hidden">
                <div class="bg-white rounded-xl shadow-md">
                    <div class="p-6 border-b flex justify-between items-center">
                        <h3 class="text-lg font-semibold">User Management</h3>
                        <div class="text-sm text-gray-600">
                            Total Users: <span class="font-semibold"><?= $totalUsers ?? 0 ?></span>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="grid md:grid-cols-3 gap-6 mb-6">
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-blue-800">Super Admins</h4>
                                <p class="text-2xl font-bold text-blue-600">1</p>
                            </div>
                            <div class="bg-green-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-green-800">School Admins</h4>
                                <p class="text-2xl font-bold text-green-600"><?= $schoolAdmins ?? 0 ?></p>
                            </div>
                            <div class="bg-purple-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-purple-800">Staff Members</h4>
                                <p class="text-2xl font-bold text-purple-600"><?= $staffMembers ?? 0 ?></p>
                            </div>
                        </div>
                        <div class="flex justify-between items-center mb-4">
                            <input type="text" id="user-search" placeholder="Search users..."
                                class="px-4 py-2 border rounded-lg focus:outline-none focus:border-indigo-500">
                            <select id="user-role-filter" class="px-4 py-2 border rounded-lg focus:outline-none focus:border-indigo-500">
                                <option value="">All Roles</option>
                                <option value="active">Active Users</option>
                                <option value="inactive">Inactive Users</option>
                            </select>
                        </div>
                        <div id="users-list" class="space-y-3">
                            <!-- Loading state -->
                            <div id="users-loading" class="text-center py-8">
                                <i class="fas fa-spinner fa-spin text-2xl text-indigo-600 mb-4"></i>
                                <p class="text-gray-600">Loading users...</p>
                            </div>
                            <!-- Users will be loaded here dynamically -->
                        </div>
                    </div>
                </div>
            </div>



            <!-- View School Details Modal -->
            <div id="viewSchoolModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
                <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                    <!-- Modal Header -->
                    <div class="flex justify-between items-center p-6 border-b">
                        <h2 class="text-xl font-semibold text-gray-800">School Details</h2>
                        <button id="closeViewSchoolModal" class="text-gray-400 hover:text-gray-600 text-2xl font-bold">
                            &times;
                        </button>
                    </div>

                    <!-- Modal Content -->
                    <div class="p-6">
                        <!-- Loading State -->
                        <div id="schoolDetailsLoading" class="text-center py-8">
                            <i class="fas fa-spinner fa-spin text-2xl text-indigo-600 mb-4"></i>
                            <p class="text-gray-600">Loading school details...</p>
                        </div>

                        <!-- School Details Content -->
                        <div id="schoolDetailsContent" class="hidden">
                            <!-- Basic Information -->
                            <div class="grid md:grid-cols-2 gap-6 mb-6">
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Basic Information</h3>
                                    <div class="space-y-3">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-600">School Name</label>
                                            <p id="school-name" class="text-gray-900 font-medium">-</p>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-600">Email Address</label>
                                            <p id="school-email" class="text-gray-900">-</p>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-600">Phone Number</label>
                                            <p id="school-phone" class="text-gray-900">-</p>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-600">Status</label>
                                            <span id="school-status" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">-</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Additional Details</h3>
                                    <div class="space-y-3">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-600">Plan ID</label>
                                            <p id="school-plan" class="text-gray-900">-</p>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-600">Registration Date</label>
                                            <p id="school-created" class="text-gray-900">-</p>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-600">Last Updated</label>
                                            <p id="school-updated" class="text-gray-900">-</p>
                                        </div>
                                        <div id="rejection-reason-container" class="hidden">
                                            <label class="block text-sm font-medium text-gray-600">Rejection Reason</label>
                                            <p id="school-rejection-reason" class="text-red-600">-</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Address -->
                            <div class="bg-gray-50 p-4 rounded-lg mb-6">
                                <h3 class="text-lg font-semibold text-gray-800 mb-3">Address</h3>
                                <p id="school-address" class="text-gray-900">-</p>
                            </div>

                            <!-- Action Buttons -->
                            <div id="school-actions" class="flex justify-center space-x-3 pt-4 border-t">
                                <button id="approve-school-btn" onclick="approveSchoolFromModal()"
                                        class="hidden px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition duration-300">
                                    <i class="fas fa-check mr-2"></i>Approve School
                                </button>
                                <button id="reject-school-btn" onclick="rejectSchoolFromModal()"
                                        class="hidden px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition duration-300">
                                    <i class="fas fa-times mr-2"></i>Reject School
                                </button>
                                <button onclick="closeViewSchoolModal()"
                                        class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                                    Close
                                </button>
                            </div>
                        </div>

                        <!-- Error State -->
                        <div id="schoolDetailsError" class="hidden text-center py-8">
                            <i class="fas fa-exclamation-triangle text-2xl text-red-600 mb-4"></i>
                            <p class="text-red-600">Failed to load school details. Please try again.</p>
                            <button onclick="closeViewSchoolModal()"
                                    class="mt-4 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700">
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Subscriptions Section -->
            <div id="subscriptions-section" class="section hidden">
                <div class="grid lg:grid-cols-3 gap-6 mb-6">
                    <div class="bg-white p-6 rounded-xl shadow-md">
                        <h4 class="font-semibold text-gray-800 mb-2">Active Subscriptions</h4>
                        <p class="text-3xl font-bold text-green-600">189</p>
                        <p class="text-sm text-gray-600">$9,261/month revenue</p>
                    </div>
                    <div class="bg-white p-6 rounded-xl shadow-md">
                        <h4 class="font-semibold text-gray-800 mb-2">Trial Users</h4>
                        <p class="text-3xl font-bold text-yellow-600">43</p>
                        <p class="text-sm text-gray-600">15 expiring this week</p>
                    </div>
                    <div class="bg-white p-6 rounded-xl shadow-md">
                        <h4 class="font-semibold text-gray-800 mb-2">Expired/Suspended</h4>
                        <p class="text-3xl font-bold text-red-600">15</p>
                        <p class="text-sm text-gray-600">Requires attention</p>
                    </div>
                </div>
                <div class="bg-white rounded-xl shadow-md">
                    <div class="p-6 border-b">
                        <h3 class="text-lg font-semibold">Subscription Details</h3>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">School
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Plan
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Next
                                        Billing</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Revenue
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                <tr>
                                    <td class="px-6 py-4">Springfield High School</td>
                                    <td class="px-6 py-4">Professional</td>
                                    <td class="px-6 py-4">
                                        <span
                                            class="status-active px-2 py-1 rounded-full text-xs font-medium">Active</span>
                                    </td>
                                    <td class="px-6 py-4">Dec 15, 2024</td>
                                    <td class="px-6 py-4">$49/month</td>
                                    <td class="px-6 py-4">
                                        <button class="text-indigo-600 hover:text-indigo-800">Manage</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Analytics Section -->
            <div id="analytics-section" class="section hidden">
                <div class="grid lg:grid-cols-2 gap-6">
                    <div class="bg-white p-6 rounded-xl shadow-md">
                        <h3 class="text-lg font-semibold mb-4">Platform Usage Analytics</h3>
                        <canvas id="usageChart" height="200"></canvas>
                    </div>
                    <div class="bg-white p-6 rounded-xl shadow-md">
                        <h3 class="text-lg font-semibold mb-4">Revenue Analytics</h3>
                        <canvas id="revenueChart" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- System Settings Section -->
            <div id="system-section" class="section hidden">
                <div class="bg-white rounded-xl shadow-md">
                    <div class="p-6 border-b">
                        <h3 class="text-lg font-semibold">System Settings</h3>
                    </div>
                    <div class="p-6 space-y-6">
                        <div>
                            <h4 class="font-semibold mb-3">Platform Configuration</h4>
                            <div class="grid md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Trial Period
                                        (days)</label>
                                    <input type="number" value="30"
                                        class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:border-indigo-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Max Questions
                                        (Trial)</label>
                                    <input type="number" value="50"
                                        class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:border-indigo-500">
                                </div>
                            </div>
                        </div>
                        <div>
                            <h4 class="font-semibold mb-3">Email Notifications</h4>
                            <div class="space-y-3">
                                <label class="flex items-center">
                                    <input type="checkbox" checked class="mr-3">
                                    <span>Trial expiration warnings</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" checked class="mr-3">
                                    <span>New school registrations</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="mr-3">
                                    <span>Daily usage reports</span>
                                </label>
                            </div>
                        </div>
                        <button class="bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700">
                            Save Settings
                        </button>
                    </div>
                </div>
            </div>

            <!-- Audit Logs Section -->
            <div id="audit-section" class="section hidden">
                <div class="bg-white rounded-xl shadow-md">
                    <div class="p-6 border-b">
                        <h3 class="text-lg font-semibold">Audit Logs</h3>
                    </div>
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <div class="flex space-x-3">
                                <input type="date"
                                    class="px-3 py-2 border rounded-lg focus:outline-none focus:border-indigo-500">
                                <select class="px-3 py-2 border rounded-lg focus:outline-none focus:border-indigo-500">
                                    <option>All Actions</option>
                                    <option>Create</option>
                                    <option>Update</option>
                                    <option>Delete</option>
                                </select>
                            </div>
                            <button class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700">
                                Export Logs
                            </button>
                        </div>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="font-semibold">School Created</p>
                                    <p class="text-sm text-gray-600">Springfield High School was created by Super Admin
                                    </p>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm text-gray-500">2024-12-10 14:30:25</p>
                                    <p class="text-xs text-gray-400">IP: *************</p>
                                </div>
                            </div>
                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="font-semibold">User Role Updated</p>
                                    <p class="text-sm text-gray-600">John Smith role changed from Staff to School Admin
                                    </p>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm text-gray-500">2024-12-10 13:15:42</p>
                                    <p class="text-xs text-gray-400">IP: *************</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
<script>
    function showSection(sectionName) {
        document.querySelectorAll('.section').forEach(section => {
            section.classList.remove('active');
            section.classList.add('hidden');
        });

        const targetSection = document.getElementById(sectionName + '-section');
        if (targetSection) {
            targetSection.classList.remove('hidden');
            targetSection.classList.add('active');
        }

        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active', 'bg-indigo-50', 'text-indigo-600');
        });

        const clickedItem = event.currentTarget;
        clickedItem.classList.add('active', 'bg-indigo-50', 'text-indigo-600');

        const titles = {
            'dashboard': 'Dashboard Overview',
            'schools': 'Schools Management',
            'users': 'User Management',
            'subscriptions': 'Subscription Management',
            'analytics': 'Platform Analytics',
            'system': 'System Settings',
            'audit': 'Audit Logs'
        };

        const subtitles = {
            'dashboard': 'Welcome back, manage your entire platform from here',
            'schools': 'Manage all educational institutions on the platform',
            'users': 'Oversee all user accounts and permissions',
            'subscriptions': 'Monitor and manage subscription plans',
            'analytics': 'View detailed platform usage and performance metrics',
            'system': 'Configure global platform settings',
            'audit': 'Review all system activities and changes'
        };

        document.getElementById('pageTitle').textContent = titles[sectionName] || 'Dashboard Overview';
        document.getElementById('pageSubtitle').textContent = subtitles[sectionName] || 'Welcome back';

        if (sectionName === 'schools') {
            filterSchools('all'); // Default tab
        } else if (sectionName === 'users') {
            loadUsers(); // Load users when users section is shown
        }
    }

    function filterSchools(status = 'all') {
        // Update tab styling
        updateTabStyling(status);

        // Determine the correct endpoint based on status
        let endpoint = '/admin/schools/all';
        if (status === 'active') {
            endpoint = '/admin/schools/active';
        } else if (status === 'inactive') {
            endpoint = '/admin/schools/pending';
        }

        const tbody = document.querySelector('#schools-tbody');
        tbody.innerHTML = '<tr><td colspan="7" class="px-6 py-8 text-center text-gray-500"><i class="fas fa-spinner fa-spin mr-2"></i>Loading schools...</td></tr>';

        fetch(endpoint)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    tbody.innerHTML = '';

                    if (data.schools.length === 0) {
                        tbody.innerHTML = '<tr><td colspan="7" class="px-6 py-8 text-center text-gray-500">No schools found.</td></tr>';
                        return;
                    }

                    data.schools.forEach(school => {
                        const row = document.createElement('tr');
                        row.className = 'hover:bg-gray-50';

                        const statusBadge = getStatusBadge(school.status);
                        const actionButtons = getActionButtons(school);

                        row.innerHTML = `
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">${escapeHtml(school.name)}</div>
                            </td>
                            <td class="px-6 py-4 text-gray-600">${escapeHtml(school.email)}</td>
                            <td class="px-6 py-4 text-gray-600">${escapeHtml(school.phone || 'N/A')}</td>
                            <td class="px-6 py-4">
                                <div class="max-w-xs truncate text-gray-600" title="${escapeHtml(school.address)}">
                                    ${escapeHtml(school.address || 'N/A')}
                                </div>
                            </td>
                            <td class="px-6 py-4">${statusBadge}</td>
                            <td class="px-6 py-4 text-gray-600">${new Date(school.created_at).toLocaleDateString()}</td>
                            <td class="px-6 py-4">${actionButtons}</td>
                        `;
                        tbody.appendChild(row);
                    });
                } else {
                    tbody.innerHTML = '<tr><td colspan="7" class="px-6 py-8 text-center text-red-500">Error loading schools: ' + (data.message || 'Unknown error') + '</td></tr>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                tbody.innerHTML = '<tr><td colspan="7" class="px-6 py-8 text-center text-red-500">Network error occurred. Please try again.</td></tr>';
            });
    }

    // Helper functions for school management
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    function getStatusBadge(status) {
        const badges = {
            'active': '<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Active</span>',
            'inactive': '<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Pending</span>',
            'rejected': '<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Rejected</span>'
        };
        return badges[status] || '<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">Unknown</span>';
    }

    function getActionButtons(school) {
        let buttons = '';

        if (school.status === 'inactive') {
            buttons += `
                <button onclick="approveSchool(${school.id})" class="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 mr-2">
                    <i class="fas fa-check mr-1"></i>Approve
                </button>
                <button onclick="rejectSchool(${school.id})" class="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700 mr-2">
                    <i class="fas fa-times mr-1"></i>Reject
                </button>
            `;
        }

        buttons += `
            <button onclick="viewSchoolDetails(${school.id})" class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">
                <i class="fas fa-eye mr-1"></i>View
            </button>
        `;

        return '<div class="flex space-x-1">' + buttons + '</div>';
    }

    function updateTabStyling(activeStatus) {
        // Reset all tab buttons
        document.querySelectorAll('.tab-button').forEach(btn => {
            btn.className = 'px-4 py-1 rounded-lg border text-gray-600 hover:bg-gray-50 tab-button';
        });

        // Set active tab styling
        const tabMap = {
            'all': 'border-indigo-600 text-indigo-600 bg-indigo-50',
            'inactive': 'border-yellow-600 text-yellow-600 bg-yellow-50',
            'active': 'border-green-600 text-green-600 bg-green-50'
        };

        const activeTab = document.querySelector(`[onclick="showSchoolTab('${activeStatus === 'inactive' ? 'pending' : activeStatus}')"]`);
        if (activeTab) {
            activeTab.className = `px-4 py-1 rounded-lg border ${tabMap[activeStatus]} tab-button`;
        }
    }

    function showSchoolTab(tabName) {
        if (tabName === 'pending') {
            filterSchools('inactive');
        } else if (tabName === 'active') {
            filterSchools('active');
        } else {
            filterSchools('all');
        }
    }

    function approveSchool(id) {
        if (confirm('Are you sure you want to approve this school?')) {
            fetch(`/admin/schools/approve/${id}`, {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert("School approved successfully!");
                    filterSchools('all');
                } else {
                    alert(data.message || 'Failed to approve school');
                }
            })
            .catch(error => {
                alert(error.message || 'Network error occurred');
            });
        }
    }

    function rejectSchool(id) {
        const reason = prompt('Please enter reason for rejection:');
        if (reason) {
            fetch(`/admin/schools/reject/${id}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ reason: reason })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert("School rejected successfully!");
                    filterSchools('all');
                } else {
                    alert(data.message || 'Failed to reject school');
                }
            })
            .catch(error => {
                alert(error.message || 'Network error occurred');
            });
        }
    }
    

    document.addEventListener('DOMContentLoaded', function() {
        showSection('dashboard');
    });

    // Enhanced search functionality
    document.getElementById('school-search')?.addEventListener('input', function(e) {
        const searchTerm = e.target.value.toLowerCase().trim();
        const rows = document.querySelectorAll('#schools-tbody tr');

        if (!searchTerm) {
            // Show all rows if search is empty
            rows.forEach(row => {
                row.style.display = '';
            });
            return;
        }

        rows.forEach(row => {
            // Skip loading/error rows
            if (row.cells.length < 7) {
                return;
            }

            // Search in school name, email, phone, and address
            const schoolName = row.cells[0].textContent.toLowerCase();
            const email = row.cells[1].textContent.toLowerCase();
            const phone = row.cells[2].textContent.toLowerCase();
            const address = row.cells[3].textContent.toLowerCase();

            const isMatch = schoolName.includes(searchTerm) ||
                           email.includes(searchTerm) ||
                           phone.includes(searchTerm) ||
                           address.includes(searchTerm);

            row.style.display = isMatch ? '' : 'none';
        });
    });

      function viewSchoolDetails(schoolId) {
        openViewSchoolModal(schoolId);
    }

    // Add School Modal Functions
    function openAddSchoolModal() {
        document.getElementById('addSchoolModal').classList.remove('hidden');
        document.body.style.overflow = 'hidden';
        // Clear form
        document.getElementById('addSchoolForm').reset();
        document.getElementById('addSchoolMessages').classList.add('hidden');
    }

    function closeAddSchoolModal() {
        document.getElementById('addSchoolModal').classList.add('hidden');
        document.body.style.overflow = 'auto';
        // Clear form and messages
        document.getElementById('addSchoolForm').reset();
        document.getElementById('addSchoolMessages').classList.add('hidden');
    }

    // Close modal when clicking outside
    document.getElementById('addSchoolModal')?.addEventListener('click', function(e) {
        if (e.target === this) {
            closeAddSchoolModal();
        }
    });

    // Close modal button
    document.getElementById('closeAddSchoolModal')?.addEventListener('click', closeAddSchoolModal);

    // Handle Add School Form Submission
    document.getElementById('addSchoolForm')?.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;

        // Validate passwords match
        if (formData.get('password') !== formData.get('confirm_password')) {
            showAddSchoolMessage('Passwords do not match!', 'error');
            return;
        }

        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Adding School...';

        // Clear previous messages
        document.getElementById('addSchoolMessages').classList.add('hidden');

        fetch('<?= site_url('school/register') ?>', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.text())
        .then(data => {
            // Check if the response indicates success
            if (data.includes('success') || data.includes('Registration successful')) {
                showAddSchoolMessage('School added successfully!', 'success');
                setTimeout(() => {
                    closeAddSchoolModal();
                    // Refresh the schools list
                    filterSchools('all');
                }, 2000);
            } else if (data.includes('already registered') || data.includes('Duplicate entry')) {
                showAddSchoolMessage('Email address is already registered!', 'error');
            } else if (data.includes('error') || data.includes('failed')) {
                showAddSchoolMessage('Failed to add school. Please check all fields and try again.', 'error');
            } else {
                showAddSchoolMessage('School added successfully!', 'success');
                setTimeout(() => {
                    closeAddSchoolModal();
                    filterSchools('all');
                }, 2000);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAddSchoolMessage('Network error occurred. Please try again.', 'error');
        })
        .finally(() => {
            // Reset button state
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        });
    });

    function showAddSchoolMessage(message, type) {
        const messagesDiv = document.getElementById('addSchoolMessages');
        const alertClass = type === 'error' ? 'bg-red-100 text-red-800 border-red-200' : 'bg-green-100 text-green-800 border-green-200';
        messagesDiv.innerHTML = `
            <div class="p-3 rounded border ${alertClass}">
                ${message}
            </div>
        `;
        messagesDiv.classList.remove('hidden');
    }



    // View School Details Modal Functions
    let currentSchoolId = null;

    function openViewSchoolModal(schoolId) {
        currentSchoolId = schoolId;
        document.getElementById('viewSchoolModal').classList.remove('hidden');
        document.body.style.overflow = 'hidden';

        // Show loading state
        document.getElementById('schoolDetailsLoading').classList.remove('hidden');
        document.getElementById('schoolDetailsContent').classList.add('hidden');
        document.getElementById('schoolDetailsError').classList.add('hidden');

        // Fetch school details
        fetchSchoolDetails(schoolId);
    }

    function closeViewSchoolModal() {
        document.getElementById('viewSchoolModal').classList.add('hidden');
        document.body.style.overflow = 'auto';
        currentSchoolId = null;
    }

    // Close modal when clicking outside
    document.getElementById('viewSchoolModal')?.addEventListener('click', function(e) {
        if (e.target === this) {
            closeViewSchoolModal();
        }
    });

    // Close modal button
    document.getElementById('closeViewSchoolModal')?.addEventListener('click', closeViewSchoolModal);

    function fetchSchoolDetails(schoolId) {
        // Since we don't have a specific endpoint, we'll get the data from the current table
        // or we can create a new endpoint. For now, let's simulate fetching from existing data

        // Try to get school data from the current table
        const schoolRows = document.querySelectorAll('#schools-tbody tr');
        let schoolData = null;

        schoolRows.forEach(row => {
            const viewButton = row.querySelector(`button[onclick*="${schoolId}"]`);
            if (viewButton) {
                const cells = row.cells;
                if (cells.length >= 6) {
                    schoolData = {
                        id: schoolId,
                        name: cells[0].textContent.trim(),
                        email: cells[1].textContent.trim(),
                        phone: cells[2].textContent.trim(),
                        address: cells[3].textContent.trim(),
                        status: cells[4].querySelector('span')?.textContent.trim() || 'Unknown',
                        created_at: cells[5].textContent.trim(),
                        plan_id: 'N/A', // We don't have this in the table
                        updated_at: 'N/A' // We don't have this in the table
                    };
                }
            }
        });

        if (schoolData) {
            displaySchoolDetails(schoolData);
        } else {
            // Fallback: try to fetch from server
            fetch(`/admin/schools/all`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const school = data.schools.find(s => s.id == schoolId);
                        if (school) {
                            displaySchoolDetails(school);
                        } else {
                            showSchoolDetailsError();
                        }
                    } else {
                        showSchoolDetailsError();
                    }
                })
                .catch(error => {
                    console.error('Error fetching school details:', error);
                    showSchoolDetailsError();
                });
        }
    }

    function displaySchoolDetails(school) {
        // Hide loading, show content
        document.getElementById('schoolDetailsLoading').classList.add('hidden');
        document.getElementById('schoolDetailsContent').classList.remove('hidden');
        document.getElementById('schoolDetailsError').classList.add('hidden');

        // Populate school details
        document.getElementById('school-name').textContent = school.name || 'N/A';
        document.getElementById('school-email').textContent = school.email || 'N/A';
        document.getElementById('school-phone').textContent = school.phone || 'N/A';
        document.getElementById('school-address').textContent = school.address || 'N/A';
        document.getElementById('school-plan').textContent = getPlanName(school.plan_id) || 'N/A';
        document.getElementById('school-created').textContent = formatDate(school.created_at) || 'N/A';
        document.getElementById('school-updated').textContent = formatDate(school.updated_at) || 'N/A';

        // Set status badge
        const statusElement = document.getElementById('school-status');
        const statusBadge = getStatusBadge(school.status);
        statusElement.outerHTML = '<span id="school-status">' + statusBadge + '</span>';

        // Show/hide rejection reason
        const rejectionContainer = document.getElementById('rejection-reason-container');
        const rejectionReason = document.getElementById('school-rejection-reason');
        if (school.rejection_reason) {
            rejectionReason.textContent = school.rejection_reason;
            rejectionContainer.classList.remove('hidden');
        } else {
            rejectionContainer.classList.add('hidden');
        }

        // Show/hide action buttons based on status
        const approveBtn = document.getElementById('approve-school-btn');
        const rejectBtn = document.getElementById('reject-school-btn');

        if (school.status === 'inactive') {
            approveBtn.classList.remove('hidden');
            rejectBtn.classList.remove('hidden');
        } else {
            approveBtn.classList.add('hidden');
            rejectBtn.classList.add('hidden');
        }
    }

    function showSchoolDetailsError() {
        document.getElementById('schoolDetailsLoading').classList.add('hidden');
        document.getElementById('schoolDetailsContent').classList.add('hidden');
        document.getElementById('schoolDetailsError').classList.remove('hidden');
    }

    function getPlanName(planId) {
        const plans = {
            '1': 'Free Trial (30 days)',
            '2': 'Professional ($49/month)',
            '3': 'Enterprise ($99/month)'
        };
        return plans[planId] || `Plan ${planId}`;
    }

    function formatDate(dateString) {
        if (!dateString || dateString === 'N/A') return 'N/A';
        try {
            return new Date(dateString).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch (e) {
            return dateString;
        }
    }

    function approveSchoolFromModal() {
        if (currentSchoolId && confirm('Are you sure you want to approve this school?')) {
            approveSchool(currentSchoolId);
            closeViewSchoolModal();
        }
    }

    function rejectSchoolFromModal() {
        if (currentSchoolId) {
            rejectSchool(currentSchoolId);
            closeViewSchoolModal();
        }
    }

    // User Management Functions
    let allUsers = [];
    let filteredUsers = [];

    function loadUsers() {
        fetch('<?= site_url('superadmin/getUsers') ?>', {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                allUsers = data.users;
                filteredUsers = [...allUsers];
                displayUsers(filteredUsers);
            } else {
                showError('Failed to load users: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error loading users:', error);
            showError('Failed to load users');
        });
    }

    function displayUsers(users) {
        const usersContainer = document.getElementById('users-list');
        const loadingElement = document.getElementById('users-loading');

        if (loadingElement) {
            loadingElement.style.display = 'none';
        }

        if (users.length === 0) {
            usersContainer.innerHTML = `
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-users text-4xl mb-4"></i>
                    <p>No users found</p>
                </div>
            `;
            return;
        }

        const usersHTML = users.map(user => {
            const initials = user.name.split(' ').map(n => n[0]).join('').toUpperCase();
            const statusClass = user.status === 'active' ? 'status-active' : 'status-expired';
            const statusText = user.status === 'active' ? 'Active' : 'Inactive';

            return `
                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-indigo-600 rounded-full flex items-center justify-center text-white font-semibold mr-4">
                            ${initials}
                        </div>
                        <div>
                            <p class="font-semibold">${user.name}</p>
                            <p class="text-sm text-gray-600">${user.email} • ${user.school_name || 'No School'}</p>
                            ${user.designation ? `<p class="text-xs text-gray-500">${user.designation}</p>` : ''}
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <span class="${statusClass} px-2 py-1 rounded-full text-xs font-medium">${statusText}</span>
                        <button onclick="toggleUserStatus(${user.id})" class="text-blue-600 hover:text-blue-800" title="Toggle Status">
                            <i class="fas fa-toggle-${user.status === 'active' ? 'on' : 'off'}"></i>
                        </button>
                        <button onclick="viewUserDetails(${user.id})" class="text-indigo-600 hover:text-indigo-800" title="View Details">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button onclick="deleteUser(${user.id})" class="text-red-600 hover:text-red-800" title="Delete User">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
        }).join('');

        usersContainer.innerHTML = usersHTML;
    }

    function toggleUserStatus(userId) {
        if (!confirm('Are you sure you want to toggle this user\'s status?')) {
            return;
        }

        fetch(`<?= site_url('superadmin/toggleUserStatus') ?>/${userId}`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess(data.message);
                loadUsers(); // Reload users to reflect changes
            } else {
                showError(data.message);
            }
        })
        .catch(error => {
            console.error('Error toggling user status:', error);
            showError('Failed to update user status');
        });
    }

    function deleteUser(userId) {
        if (!confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
            return;
        }

        fetch(`<?= site_url('superadmin/deleteUser') ?>/${userId}`, {
            method: 'DELETE',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess(data.message);
                loadUsers(); // Reload users to reflect changes
            } else {
                showError(data.message);
            }
        })
        .catch(error => {
            console.error('Error deleting user:', error);
            showError('Failed to delete user');
        });
    }

    function viewUserDetails(userId) {
        fetch(`<?= site_url('superadmin/getUserDetails') ?>/${userId}`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showUserDetailsModal(data.user);
            } else {
                showError(data.message);
            }
        })
        .catch(error => {
            console.error('Error fetching user details:', error);
            showError('Failed to load user details');
        });
    }

    function showUserDetailsModal(user) {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4';
        modal.innerHTML = `
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                <div class="flex justify-between items-center p-6 border-b">
                    <h2 class="text-xl font-semibold text-gray-800">User Details</h2>
                    <button onclick="this.closest('.fixed').remove()" class="text-gray-400 hover:text-gray-600 text-2xl font-bold">
                        &times;
                    </button>
                </div>
                <div class="p-6">
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Name</label>
                            <p class="text-gray-900 font-medium">${user.name}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Email</label>
                            <p class="text-gray-900">${user.email}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">School</label>
                            <p class="text-gray-900">${user.school_name || 'No School Assigned'}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Status</label>
                            <span class="${user.status === 'active' ? 'status-active' : 'status-expired'} px-2 py-1 rounded-full text-xs font-medium">
                                ${user.status === 'active' ? 'Active' : 'Inactive'}
                            </span>
                        </div>
                        ${user.designation ? `
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Designation</label>
                            <p class="text-gray-900">${user.designation}</p>
                        </div>
                        ` : ''}
                        ${user.phone ? `
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Phone</label>
                            <p class="text-gray-900">${user.phone}</p>
                        </div>
                        ` : ''}
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Created At</label>
                            <p class="text-gray-900">${new Date(user.created_at).toLocaleDateString()}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Last Updated</label>
                            <p class="text-gray-900">${new Date(user.updated_at).toLocaleDateString()}</p>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3 pt-6 border-t mt-6">
                        <button onclick="this.closest('.fixed').remove()" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    // Search and filter functionality
    document.addEventListener('DOMContentLoaded', function() {
        const userSearch = document.getElementById('user-search');
        const userRoleFilter = document.getElementById('user-role-filter');

        if (userSearch) {
            userSearch.addEventListener('input', function() {
                filterUsers();
            });
        }

        if (userRoleFilter) {
            userRoleFilter.addEventListener('change', function() {
                filterUsers();
            });
        }
    });

    function filterUsers() {
        const searchTerm = document.getElementById('user-search').value.toLowerCase();
        const statusFilter = document.getElementById('user-role-filter').value;

        filteredUsers = allUsers.filter(user => {
            const matchesSearch = user.name.toLowerCase().includes(searchTerm) ||
                                user.email.toLowerCase().includes(searchTerm) ||
                                (user.school_name && user.school_name.toLowerCase().includes(searchTerm));

            const matchesStatus = !statusFilter || user.status === statusFilter;

            return matchesSearch && matchesStatus;
        });

        displayUsers(filteredUsers);
    }
</script>

<!-- Flash Messages -->
<?php if (session()->getFlashdata('success')): ?>
    <div class="bg-green-100 text-green-800 px-4 py-2 rounded mb-4">
        <?= session()->getFlashdata('success') ?>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('error')): ?>
    <div class="bg-red-100 text-red-800 px-4 py-2 rounded mb-4">
        <?= session()->getFlashdata('error') ?>
    </div>
<?php endif; ?>



</body>
</html>
