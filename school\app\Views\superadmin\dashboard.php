<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?= csrf_token() ?>">
    <title>Super Admin Dashboard QuestionBank Pro</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .sidebar-transition {
            transition: transform 0.3s ease-in-out;
        }

        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .stats-card { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .stats-card-2 { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .stats-card-3 { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        .stats-card-4 { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
        .nav-item { transition: all 0.3s ease; }
        .nav-item:hover { background: linear-gradient(90deg, rgba(99, 102, 241, 0.1) 0%, rgba(99, 102, 241, 0.05) 100%); }
        .nav-item.active { background: linear-gradient(90deg, rgba(99, 102, 241, 0.15) 0%, rgba(99, 102, 241, 0.05) 100%); border-right: 4px solid #6366f1; }
        .animate-fade-in { animation: fadeIn 0.5s ease-in-out; }
        @keyframes fadeIn { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }
        .notification-badge { animation: pulse 2s infinite; }
        @keyframes pulse { 0%, 100% { transform: scale(1); } 50% { transform: scale(1.1); } }

        /* Error styling for form inputs */
        .border-red-500 {
            border-color: #ef4444 !important;
            box-shadow: 0 0 0 1px #ef4444;
        }

        .error-message {
            font-size: 0.875rem;
            line-height: 1.25rem;
        }

        /* Modal animations */
        .modal-enter {
            animation: modalEnter 0.3s ease-out;
        }

        @keyframes modalEnter {
            from {
                opacity: 0;
                transform: scale(0.95);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .status-active {
            background-color: rgb(220 252 231);
            color: rgb(22 101 52);
        }

        .status-trial {
            background-color: rgb(254 249 195);
            color: rgb(133 77 14);
        }

        .status-expired {
            background-color: rgb(254 226 226);
            color: rgb(153 27 27);
        }

        .status-suspended {
            background-color: rgb(243 244 246);
            color: rgb(55 65 81);
        }

        .section {
            display: none;
        }

        .section.active {
            display: block;
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>

<body class="bg-gray-50">
    <!-- Enhanced Sidebar -->
    <div id="sidebar" class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-xl sidebar-transition flex flex-col">
        <!-- Super Admin Header -->
        <div class="gradient-bg h-24 flex items-center justify-center relative flex-shrink-0 py-4">
            <div class="absolute inset-0 bg-black opacity-10"></div>
            <div class="relative text-center px-4 w-full">
                <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-2">
                    <i class="fas fa-crown text-white text-xl"></i>
                </div>
                <h1 class="text-white text-sm font-bold leading-tight">Super Admin</h1>
                <p class="text-white text-xs opacity-90 leading-tight mt-1">System Control</p>
            </div>
        </div>
        <!-- Navigation Menu -->
        <nav class="mt-6 px-3 flex-grow overflow-y-auto">
            <div class="space-y-1">
                <a href="#" onclick="showSection('dashboard')"
                    class="nav-item active flex items-center px-4 py-3 text-gray-700 rounded-lg font-medium">
                    <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-tachometer-alt text-indigo-600 text-sm"></i>
                    </div>
                    <span>Dashboard</span>
                </a>
                <a href="#" onclick="showSection('schools')"
                    class="nav-item flex items-center px-4 py-3 text-gray-700 rounded-lg font-medium">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-school text-blue-600 text-sm"></i>
                    </div>
                    <span>Schools Management</span>
                </a>
                <a href="#" onclick="showSection('users')"
                    class="nav-item flex items-center px-4 py-3 text-gray-700 rounded-lg font-medium">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-users text-green-600 text-sm"></i>
                    </div>
                    <span>User Management</span>
                </a>
                <a href="#" onclick="showSection('subscriptions')"
                    class="nav-item flex items-center px-4 py-3 text-gray-700 rounded-lg font-medium">
                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-credit-card text-orange-600 text-sm"></i>
                    </div>
                    <span>Subscriptions</span>
                </a>
                <a href="#" onclick="showSection('analytics')"
                    class="nav-item flex items-center px-4 py-3 text-gray-700 rounded-lg font-medium">
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-chart-bar text-purple-600 text-sm"></i>
                    </div>
                    <span>Analytics</span>
                </a>
                <a href="#" onclick="showSection('system')"
                    class="nav-item flex items-center px-4 py-3 text-gray-700 rounded-lg font-medium">
                    <div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-cog text-gray-600 text-sm"></i>
                    </div>
                    <span>System Settings</span>
                </a>
                <a href="#" onclick="showSection('audit')"
                    class="nav-item flex items-center px-4 py-3 text-gray-700 rounded-lg font-medium">
                    <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-history text-yellow-600 text-sm"></i>
                    </div>
                    <span>Audit Logs</span>
                </a>
            </div>
        </nav>
        <!-- Logout Button at Bottom -->
        <div class="px-3 pb-4">
            <a href="<?= site_url('logout') ?>" class="nav-item flex items-center px-4 py-3 text-red-600 rounded-lg font-medium hover:bg-red-50 transition-colors">
                <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-sign-out-alt text-red-600 text-sm"></i>
                </div>
                <span>Logout</span>
            </a>
        </div>

        <!-- User Profile Section -->
        <div class="p-4 border-t border-gray-200 flex-shrink-0">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 gradient-bg rounded-full flex items-center justify-center">
                    <span class="text-white font-bold text-sm">SA</span>
                </div>
                <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900 truncate">Super Admin</p>
                    <p class="text-xs text-gray-500 truncate">System Administrator</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="ml-64 min-h-screen">
        <!-- Enhanced Header -->
        <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-30">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="flex items-center">
                    <button class="lg:hidden text-gray-600 hover:text-gray-900 p-2 rounded-lg hover:bg-gray-100" onclick="toggleSidebar()">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                    <div class="ml-4">
                        <h2 id="pageTitle" class="text-2xl font-bold text-gray-800">Dashboard Overview</h2>
                        <p id="pageSubtitle" class="text-sm text-gray-600 mt-1">Welcome back, manage your entire platform from here</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <button id="notificationBtn" class="p-2 text-gray-400 hover:text-gray-600 relative rounded-lg hover:bg-gray-100 transition-colors">
                            <i class="fas fa-bell text-xl"></i>
                            <?php if ($notificationCount > 0): ?>
                                <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center notification-badge">
                                    <?= $notificationCount > 99 ? '99+' : $notificationCount ?>
                                </span>
                            <?php endif; ?>
                        </button>

                        <!-- Notification Dropdown -->
                        <div id="notificationDropdown" class="hidden absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                            <div class="p-4 border-b border-gray-200">
                                <div class="flex items-center justify-between">
                                    <h3 class="text-lg font-semibold text-gray-900">Notifications</h3>
                                    <button id="refreshNotifications" class="text-indigo-600 hover:text-indigo-800 text-sm">
                                        <i class="fas fa-sync-alt"></i> Refresh
                                    </button>
                                </div>
                            </div>
                            <div id="notificationList" class="max-h-96 overflow-y-auto">
                                <div class="p-4 text-center text-gray-500">
                                    <i class="fas fa-spinner fa-spin"></i> Loading notifications...
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="w-8 h-8 gradient-bg rounded-full flex items-center justify-center">
                        <span class="text-white font-bold text-sm">SA</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Dashboard Content -->
        <main class="p-6">
            <!-- Dashboard Section -->
            <div id="dashboard-section" class="section active">
                <!-- Enhanced Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8 animate-fade-in">
                    <div class="stats-card card-hover p-6 rounded-xl shadow-lg text-white relative overflow-hidden">
                        <div class="absolute top-0 right-0 w-20 h-20 bg-white bg-opacity-10 rounded-full -mr-10 -mt-10"></div>
                        <div class="relative">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-white text-opacity-80 text-sm">Total Schools</p>
                                    <p class="text-3xl font-bold text-white"><?= count($schools ?? []) ?></p>
                                    <p class="text-white text-opacity-90 text-sm mt-1">
                                        <i class="fa fa-arrow-up mr-1"></i>Registered schools
                                    </p>
                                </div>
                                <div class="bg-white bg-opacity-20 p-3 rounded-full">
                                    <i class="fas fa-school text-2xl text-white"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="stats-card-2 card-hover p-6 rounded-xl shadow-lg text-white relative overflow-hidden">
                        <div class="absolute top-0 right-0 w-20 h-20 bg-white bg-opacity-10 rounded-full -mr-10 -mt-10"></div>
                        <div class="relative">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-white text-opacity-80 text-sm">Active Users</p>
                                    <p class="text-3xl font-bold text-white"><?= $totalUsers ?? 0 ?></p>
                                    <p class="text-white text-opacity-90 text-sm mt-1">
                                        <i class="fa fa-arrow-up mr-1"></i>Total registered users
                                    </p>
                                </div>
                                <div class="bg-white bg-opacity-20 p-3 rounded-full">
                                    <i class="fas fa-users text-2xl text-white"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="stats-card-3 card-hover p-6 rounded-xl shadow-lg text-white relative overflow-hidden">
                        <div class="absolute top-0 right-0 w-20 h-20 bg-white bg-opacity-10 rounded-full -mr-10 -mt-10"></div>
                        <div class="relative">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-white text-opacity-80 text-sm">Total Questions</p>
                                    <p class="text-3xl font-bold text-white"><?= number_format($totalQuestions) ?></p>
                                    <p class="text-white text-opacity-90 text-sm mt-1">
                                        <i class="fa fa-question-circle mr-1"></i>Across all schools
                                    </p>
                                </div>
                                <div class="bg-white bg-opacity-20 p-3 rounded-full">
                                    <i class="fa fa-question-circle text-2xl text-white"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="stats-card-4 card-hover p-6 rounded-xl shadow-lg text-white relative overflow-hidden">
                        <div class="absolute top-0 right-0 w-20 h-20 bg-white bg-opacity-10 rounded-full -mr-10 -mt-10"></div>
                        <div class="relative">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-white text-opacity-80 text-sm">Monthly Revenue</p>
                                    <p class="text-3xl font-bold text-white">₹<?= number_format($monthlyRevenue, 2) ?></p>
                                    <p class="text-white text-opacity-90 text-sm mt-1">
                                        <?php if ($revenueGrowth > 0): ?>
                                            <i class="fa fa-arrow-up mr-1"></i><?= $revenueGrowth ?>% from last month
                                        <?php elseif ($revenueGrowth < 0): ?>
                                            <i class="fa fa-arrow-down mr-1"></i><?= abs($revenueGrowth) ?>% from last month
                                        <?php else: ?>
                                            <i class="fa fa-minus mr-1"></i>No change from last month
                                        <?php endif; ?>
                                    </p>
                                </div>
                                <div class="bg-white bg-opacity-20 p-3 rounded-full">
                                    <i class="fa fa-dollar-sign text-2xl text-white"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Charts Row -->
                <div class="grid lg:grid-cols-2 gap-6 mb-8 animate-fade-in">
                    <div class="bg-white p-6 rounded-xl shadow-lg card-hover border border-gray-100">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-gray-800">School Growth Trend</h3>
                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-chart-line text-blue-600 text-sm"></i>
                            </div>
                        </div>
                        <div class="relative h-[250px]">
                            <canvas id="schoolGrowthChart"></canvas>
                        </div>
                    </div>
                    <div class="bg-white p-6 rounded-xl shadow-lg card-hover border border-gray-100">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-gray-800">Subscription Distribution</h3>
                            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-chart-pie text-purple-600 text-sm"></i>
                            </div>
                        </div>
                        <div class="relative h-[250px]">
                            <canvas id="subscriptionChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Recent Activity -->
                <div class="bg-white rounded-xl shadow-lg card-hover border border-gray-100 animate-fade-in">
                    <div class="p-6 border-b border-gray-100">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-clock text-gray-600 text-sm"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-800">Recent Activity</h3>
                            </div>
                            <button onclick="loadRecentActivities()" class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-md hover:shadow-lg text-sm">
                                <i class="fas fa-refresh mr-1"></i>Refresh
                            </button>
                        </div>
                    </div>
                    <div class="p-6">
                        <div id="recent-activities-list" class="space-y-4">
                            <div class="text-center py-8 text-gray-500">
                                <i class="fas fa-spinner fa-spin text-2xl mb-3"></i>
                                <p>Loading recent activities...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Schools Section -->
            <div id="schools-section" class="section hidden animate-fade-in">
                <div class="bg-white rounded-xl shadow-lg border border-gray-100">
                    <div class="p-6 border-b border-gray-100 flex justify-between items-center">
                        <div>
                            <div class="flex items-center mb-3">
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-school text-blue-600 text-sm"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-800">Schools Management</h3>
                            </div>
                            <div class="flex space-x-2">
                                <button onclick="showSchoolTab('all')" class="px-4 py-2 rounded-lg border-2 border-indigo-600 text-indigo-600 hover:bg-indigo-50 transition-colors font-medium">All Schools</button>
                                <button onclick="showSchoolTab('pending')" class="px-4 py-2 rounded-lg border-2 border-yellow-600 text-yellow-600 hover:bg-yellow-50 transition-colors font-medium">Pending Approval</button>
                                <button onclick="showSchoolTab('active')" class="px-4 py-2 rounded-lg border-2 border-green-600 text-green-600 hover:bg-green-50 transition-colors font-medium">Active</button>
                            </div>
                        </div>
                        <div class="flex space-x-3">
                            <div class="relative">
                                <input type="text" id="school-search" placeholder="Search schools..."
                                    class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-colors">
                                <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                            </div>
                            <button onclick="openAddSchoolModal()"
                                class="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-4 py-2 rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 shadow-md hover:shadow-lg">
                                <i class="fa fa-plus mr-2"></i>Add New School
                            </button>
                        </div>
                    </div>

                    <!-- Enhanced Schools Table -->
                    <div id="schools-table" class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gradient-to-r from-gray-50 to-gray-100">
                                <tr>
                                    <th class="px-6 py-4 font-semibold text-xs uppercase text-gray-700 tracking-wider text-left">School Name</th>
                                    <th class="px-6 py-4 font-semibold text-xs uppercase text-gray-700 tracking-wider text-left">Email</th>
                                    <th class="px-6 py-4 font-semibold text-xs uppercase text-gray-700 tracking-wider text-left">Phone</th>
                                    <th class="px-6 py-4 font-semibold text-xs uppercase text-gray-700 tracking-wider text-left">Address</th>
                                    <th class="px-6 py-4 font-semibold text-xs uppercase text-gray-700 tracking-wider text-left">Status</th>
                                    <th class="px-6 py-4 font-semibold text-xs uppercase text-gray-700 tracking-wider text-left">Registered At</th>
                                    <th class="px-6 py-4 font-semibold text-xs uppercase text-gray-700 tracking-wider text-left">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="schools-tbody" class="divide-y divide-gray-200 bg-white">
                                <!-- Dynamic content will be loaded here -->
                                <tr class="hover:bg-gray-50 transition-colors">
                                    <td colspan="7" class="px-6 py-8 text-center text-gray-500">
                                        <div class="flex items-center justify-center">
                                            <i class="fas fa-spinner fa-spin mr-2 text-indigo-600"></i>
                                            <span>Loading schools...</span>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Add School Modal -->
            <div id="addSchoolModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
                <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                    <!-- Modal Header -->
                    <div class="flex justify-between items-center p-6 border-b">
                        <h2 class="text-xl font-semibold text-gray-800">Add New School</h2>
                        <button id="closeAddSchoolModal" class="text-gray-400 hover:text-gray-600 text-2xl font-bold">
                            &times;
                        </button>
                    </div>

                    <!-- Modal Content -->
                    <div class="p-6">
                        <form id="addSchoolForm" class="space-y-6">
                            <div class="grid md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">School Name *</label>
                                    <input type="text" name="name" required
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                                    <input type="email" name="email" required
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                </div>
                            </div>

                            <div class="grid md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number *</label>
                                    <input type="tel" name="phone" required
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Subscription Plan *</label>
                                    <select name="plan_id" required
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                        <option value="">Select Plan</option>
                                        <option value="1">Free Trial (30 days)</option>
                                        <option value="2">Professional ($49/month)</option>
                                        <option value="3">Enterprise ($99/month)</option>
                                    </select>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">School Address *</label>
                                <textarea name="address" required rows="3"
                                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                          placeholder="Enter complete school address"></textarea>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Password *</label>
                                <input type="password" name="password" required minlength="8"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                       placeholder="Minimum 8 characters">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Confirm Password *</label>
                                <input type="password" name="confirm_password" required minlength="8"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                       placeholder="Re-enter password">
                            </div>

                            <!-- Status Selection -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Initial Status</label>
                                <select name="status"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                    <option value="inactive">Pending Approval</option>
                                    <option value="active">Active (Approved)</option>
                                </select>
                            </div>

                            <!-- Error/Success Messages -->
                            <div id="addSchoolMessages" class="hidden"></div>

                            <!-- Submit Button -->
                            <div class="flex justify-end space-x-3 pt-4">
                                <button type="button" onclick="closeAddSchoolModal()"
                                        class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                                    Cancel
                                </button>
                                <button type="submit"
                                        class="px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition duration-300">
                                    <i class="fas fa-plus mr-2"></i>Add School
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

          <!-- Users Management Section -->
            <div id="users-section" class="section hidden">
                <div class="bg-white rounded-xl shadow-md">
                    <div class="p-6 border-b flex justify-between items-center">
                        <h3 class="text-lg font-semibold">User Management</h3>
                        <div class="text-sm text-gray-600">
                            Total Users: <span class="font-semibold"><?= $totalUsers ?? 0 ?></span>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="grid md:grid-cols-3 gap-6 mb-6">
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-blue-800">Super Admins</h4>
                                <p class="text-2xl font-bold text-blue-600">1</p>
                            </div>
                            <div class="bg-green-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-green-800">School Admins</h4>
                                <p class="text-2xl font-bold text-green-600"><?= $schoolAdmins ?? 0 ?></p>
                            </div>
                            <div class="bg-purple-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-purple-800">Staff Members</h4>
                                <p class="text-2xl font-bold text-purple-600"><?= $staffMembers ?? 0 ?></p>
                            </div>
                        </div>
                        <div class="flex justify-between items-center mb-4">
                            <input type="text" id="user-search" placeholder="Search users..."
                                class="px-4 py-2 border rounded-lg focus:outline-none focus:border-indigo-500">
                            <select id="user-role-filter" class="px-4 py-2 border rounded-lg focus:outline-none focus:border-indigo-500">
                                <option value="">All Roles</option>
                                <option value="active">Active Users</option>
                                <option value="inactive">Inactive Users</option>
                            </select>
                        </div>
                        <div id="users-list" class="space-y-3">
                            <!-- Loading state -->
                            <div id="users-loading" class="text-center py-8">
                                <i class="fas fa-spinner fa-spin text-2xl text-indigo-600 mb-4"></i>
                                <p class="text-gray-600">Loading users...</p>
                            </div>
                            <!-- Users will be loaded here dynamically -->
                        </div>
                    </div>
                </div>
            </div>



            <!-- View School Details Modal -->
            <div id="viewSchoolModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
                <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                    <!-- Modal Header -->
                    <div class="flex justify-between items-center p-6 border-b">
                        <h2 class="text-xl font-semibold text-gray-800">School Details</h2>
                        <button id="closeViewSchoolModal" class="text-gray-400 hover:text-gray-600 text-2xl font-bold">
                            &times;
                        </button>
                    </div>

                    <!-- Modal Content -->
                    <div class="p-6">
                        <!-- Loading State -->
                        <div id="schoolDetailsLoading" class="text-center py-8">
                            <i class="fas fa-spinner fa-spin text-2xl text-indigo-600 mb-4"></i>
                            <p class="text-gray-600">Loading school details...</p>
                        </div>

                        <!-- School Details Content -->
                        <div id="schoolDetailsContent" class="hidden">
                            <!-- Basic Information -->
                            <div class="grid md:grid-cols-2 gap-6 mb-6">
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Basic Information</h3>
                                    <div class="space-y-3">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-600">School Name</label>
                                            <p id="school-name" class="text-gray-900 font-medium">-</p>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-600">Email Address</label>
                                            <p id="school-email" class="text-gray-900">-</p>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-600">Phone Number</label>
                                            <p id="school-phone" class="text-gray-900">-</p>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-600">Status</label>
                                            <span id="school-status" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">-</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Additional Details</h3>
                                    <div class="space-y-3">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-600">Plan ID</label>
                                            <p id="school-plan" class="text-gray-900">-</p>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-600">Registration Date</label>
                                            <p id="school-created" class="text-gray-900">-</p>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-600">Last Updated</label>
                                            <p id="school-updated" class="text-gray-900">-</p>
                                        </div>
                                        <div id="rejection-reason-container" class="hidden">
                                            <label class="block text-sm font-medium text-gray-600">Rejection Reason</label>
                                            <p id="school-rejection-reason" class="text-red-600">-</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Address -->
                            <div class="bg-gray-50 p-4 rounded-lg mb-6">
                                <h3 class="text-lg font-semibold text-gray-800 mb-3">Address</h3>
                                <p id="school-address" class="text-gray-900">-</p>
                            </div>

                            <!-- Action Buttons -->
                            <div id="school-actions" class="flex justify-center space-x-3 pt-4 border-t">
                                <button id="approve-school-btn" onclick="approveSchoolFromModal()"
                                        class="hidden px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition duration-300">
                                    <i class="fas fa-check mr-2"></i>Approve School
                                </button>
                                <button id="reject-school-btn" onclick="rejectSchoolFromModal()"
                                        class="hidden px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition duration-300">
                                    <i class="fas fa-times mr-2"></i>Reject School
                                </button>
                                <button onclick="closeViewSchoolModal()"
                                        class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                                    Close
                                </button>
                            </div>
                        </div>

                        <!-- Error State -->
                        <div id="schoolDetailsError" class="hidden text-center py-8">
                            <i class="fas fa-exclamation-triangle text-2xl text-red-600 mb-4"></i>
                            <p class="text-red-600">Failed to load school details. Please try again.</p>
                            <button onclick="closeViewSchoolModal()"
                                    class="mt-4 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700">
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Subscriptions Section -->
            <div id="subscriptions-section" class="section hidden">
                <div class="grid lg:grid-cols-3 gap-6 mb-6">
                    <div class="bg-white p-6 rounded-xl shadow-md">
                        <h4 class="font-semibold text-gray-800 mb-2">Active Subscriptions</h4>
                        <p class="text-3xl font-bold text-green-600">189</p>
                        <p class="text-sm text-gray-600">$9,261/month revenue</p>
                    </div>
                    <div class="bg-white p-6 rounded-xl shadow-md">
                        <h4 class="font-semibold text-gray-800 mb-2">Trial Users</h4>
                        <p class="text-3xl font-bold text-yellow-600">43</p>
                        <p class="text-sm text-gray-600">15 expiring this week</p>
                    </div>
                    <div class="bg-white p-6 rounded-xl shadow-md">
                        <h4 class="font-semibold text-gray-800 mb-2">Expired/Suspended</h4>
                        <p class="text-3xl font-bold text-red-600">15</p>
                        <p class="text-sm text-gray-600">Requires attention</p>
                    </div>
                </div>
                <div class="bg-white rounded-xl shadow-md">
                    <div class="p-6 border-b">
                        <h3 class="text-lg font-semibold">Subscription Details</h3>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">School
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Plan
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Next
                                        Billing</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Revenue
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                <tr>
                                    <td class="px-6 py-4">Springfield High School</td>
                                    <td class="px-6 py-4">Professional</td>
                                    <td class="px-6 py-4">
                                        <span
                                            class="status-active px-2 py-1 rounded-full text-xs font-medium">Active</span>
                                    </td>
                                    <td class="px-6 py-4">Dec 15, 2024</td>
                                    <td class="px-6 py-4">$49/month</td>
                                    <td class="px-6 py-4">
                                        <button class="text-indigo-600 hover:text-indigo-800">Manage</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Analytics Section -->
            <div id="analytics-section" class="section hidden">
                <div class="grid lg:grid-cols-2 gap-6">
                    <div class="bg-white p-6 rounded-xl shadow-md">
                        <h3 class="text-lg font-semibold mb-4">Platform Usage Analytics</h3>
                        <canvas id="usageChart" height="200"></canvas>
                    </div>
                    <div class="bg-white p-6 rounded-xl shadow-md">
                        <h3 class="text-lg font-semibold mb-4">Revenue Analytics</h3>
                        <canvas id="revenueChart" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- System Settings Section -->
            <div id="system-section" class="section hidden">
                <div class="bg-white rounded-xl shadow-md">
                    <div class="p-6 border-b">
                        <h3 class="text-lg font-semibold">System Settings</h3>
                    </div>
                    <div class="p-6 space-y-6">
                        <div>
                            <h4 class="font-semibold mb-3">Platform Configuration</h4>
                            <div class="grid md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Trial Period
                                        (days)</label>
                                    <input type="number" value="30"
                                        class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:border-indigo-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Max Questions
                                        (Trial)</label>
                                    <input type="number" value="50"
                                        class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:border-indigo-500">
                                </div>
                            </div>
                        </div>
                        <div>
                            <h4 class="font-semibold mb-3">Email Notifications</h4>
                            <div class="space-y-3">
                                <label class="flex items-center">
                                    <input type="checkbox" checked class="mr-3">
                                    <span>Trial expiration warnings</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" checked class="mr-3">
                                    <span>New school registrations</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="mr-3">
                                    <span>Daily usage reports</span>
                                </label>
                            </div>
                        </div>
                        <button class="bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700">
                            Save Settings
                        </button>
                    </div>
                </div>
            </div>

            <!-- Audit Logs Section -->
            <div id="audit-section" class="section hidden">
                <div class="bg-white rounded-xl shadow-md">
                    <div class="p-6 border-b flex justify-between items-center">
                        <h3 class="text-lg font-semibold">Audit Logs</h3>
                        <div class="flex space-x-3">
                            <button onclick="refreshAuditLogs()" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700">
                                <i class="fas fa-refresh mr-2"></i>Refresh
                            </button>
                            <button onclick="exportAuditLogs()" class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700">
                                <i class="fas fa-download mr-2"></i>Export Logs
                            </button>
                        </div>
                    </div>

                    <!-- Audit Statistics -->
                    <div class="p-6 border-b bg-gray-50">
                        <div class="grid md:grid-cols-4 gap-4" id="audit-stats">
                            <div class="bg-white p-4 rounded-lg shadow-sm">
                                <h4 class="text-sm font-medium text-gray-600">Total Actions</h4>
                                <p class="text-2xl font-bold text-gray-900" id="total-actions">-</p>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow-sm">
                                <h4 class="text-sm font-medium text-gray-600">Today's Actions</h4>
                                <p class="text-2xl font-bold text-blue-600" id="today-actions">-</p>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow-sm">
                                <h4 class="text-sm font-medium text-gray-600">Critical Events</h4>
                                <p class="text-2xl font-bold text-red-600" id="critical-events">-</p>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow-sm">
                                <h4 class="text-sm font-medium text-gray-600">Failed Actions</h4>
                                <p class="text-2xl font-bold text-orange-600" id="failed-actions">-</p>
                            </div>
                        </div>
                    </div>

                    <div class="p-6">
                        <!-- Filters -->
                        <div class="grid md:grid-cols-6 gap-3 mb-6">
                            <input type="text" id="audit-search" placeholder="Search logs..."
                                class="px-3 py-2 border rounded-lg focus:outline-none focus:border-indigo-500">
                            <input type="date" id="audit-date-from"
                                class="px-3 py-2 border rounded-lg focus:outline-none focus:border-indigo-500">
                            <input type="date" id="audit-date-to"
                                class="px-3 py-2 border rounded-lg focus:outline-none focus:border-indigo-500">
                            <select id="audit-action-filter" class="px-3 py-2 border rounded-lg focus:outline-none focus:border-indigo-500">
                                <option value="">All Actions</option>
                            </select>
                            <select id="audit-severity-filter" class="px-3 py-2 border rounded-lg focus:outline-none focus:border-indigo-500">
                                <option value="">All Severities</option>
                                <option value="low">Low</option>
                                <option value="medium">Medium</option>
                                <option value="high">High</option>
                                <option value="critical">Critical</option>
                            </select>
                            <select id="audit-school-filter" class="px-3 py-2 border rounded-lg focus:outline-none focus:border-indigo-500">
                                <option value="">All Schools</option>
                            </select>
                        </div>

                        <!-- Audit Logs List -->
                        <div id="audit-logs-container">
                            <div id="audit-logs-loading" class="text-center py-8">
                                <i class="fas fa-spinner fa-spin text-2xl text-indigo-600 mb-4"></i>
                                <p class="text-gray-600">Loading audit logs...</p>
                            </div>
                            <div id="audit-logs-list" class="space-y-3 hidden"></div>
                        </div>

                        <!-- Pagination -->
                        <div id="audit-pagination" class="flex justify-between items-center mt-6 hidden">
                            <div class="text-sm text-gray-600" id="audit-pagination-info"></div>
                            <div class="flex space-x-2" id="audit-pagination-buttons"></div>
                        </div>
                    </div>
                </div>
            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
<script>
    function showSection(sectionName, clickedElement = null) {
        document.querySelectorAll('.section').forEach(section => {
            section.classList.remove('active');
            section.classList.add('hidden');
        });

        const targetSection = document.getElementById(sectionName + '-section');
        if (targetSection) {
            targetSection.classList.remove('hidden');
            targetSection.classList.add('active');
        }

        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active', 'bg-indigo-50', 'text-indigo-600');
        });

        // Handle both event-based and programmatic calls
        let navItem = clickedElement;
        if (!navItem && event && event.currentTarget) {
            navItem = event.currentTarget;
        }
        if (!navItem) {
            // Find the nav item by section name
            navItem = document.querySelector(`[onclick*="showSection('${sectionName}')"]`);
        }

        if (navItem) {
            navItem.classList.add('active', 'bg-indigo-50', 'text-indigo-600');
        }

        const titles = {
            'dashboard': 'Dashboard Overview',
            'schools': 'Schools Management',
            'users': 'User Management',
            'subscriptions': 'Subscription Management',
            'analytics': 'Platform Analytics',
            'system': 'System Settings',
            'audit': 'Audit Logs'
        };

        const subtitles = {
            'dashboard': 'Welcome back, manage your entire platform from here',
            'schools': 'Manage all educational institutions on the platform',
            'users': 'Oversee all user accounts and permissions',
            'subscriptions': 'Monitor and manage subscription plans',
            'analytics': 'View detailed platform usage and performance metrics',
            'system': 'Configure global platform settings',
            'audit': 'Review all system activities and changes'
        };

        document.getElementById('pageTitle').textContent = titles[sectionName] || 'Dashboard Overview';
        document.getElementById('pageSubtitle').textContent = subtitles[sectionName] || 'Welcome back';

        if (sectionName === 'schools') {
            filterSchools('all'); // Default tab
        } else if (sectionName === 'users') {
            loadUsers(); // Load users when users section is shown
        } else if (sectionName === 'audit') {
            loadAuditLogs(); // Load audit logs when audit section is shown
            loadAuditStats(); // Load audit statistics
            loadAuditFilterOptions(); // Load filter options
        }
    }

    function filterSchools(status = 'all') {
        // Update tab styling
        updateTabStyling(status);

        // Determine the correct endpoint based on status
        let endpoint = '/admin/schools/all';
        if (status === 'active') {
            endpoint = '/admin/schools/active';
        } else if (status === 'inactive') {
            endpoint = '/admin/schools/pending';
        }

        const tbody = document.querySelector('#schools-tbody');
        tbody.innerHTML = '<tr><td colspan="7" class="px-6 py-8 text-center text-gray-500"><i class="fas fa-spinner fa-spin mr-2"></i>Loading schools...</td></tr>';

        fetch(endpoint)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    tbody.innerHTML = '';

                    if (data.schools.length === 0) {
                        tbody.innerHTML = '<tr><td colspan="7" class="px-6 py-8 text-center text-gray-500">No schools found.</td></tr>';
                        return;
                    }

                    data.schools.forEach(school => {
                        const row = document.createElement('tr');
                        row.className = 'hover:bg-gray-50';

                        const statusBadge = getStatusBadge(school.status);
                        const actionButtons = getActionButtons(school);

                        row.innerHTML = `
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">${escapeHtml(school.name)}</div>
                            </td>
                            <td class="px-6 py-4 text-gray-600">${escapeHtml(school.email)}</td>
                            <td class="px-6 py-4 text-gray-600">${escapeHtml(school.phone || 'N/A')}</td>
                            <td class="px-6 py-4">
                                <div class="max-w-xs truncate text-gray-600" title="${escapeHtml(school.address)}">
                                    ${escapeHtml(school.address || 'N/A')}
                                </div>
                            </td>
                            <td class="px-6 py-4">${statusBadge}</td>
                            <td class="px-6 py-4 text-gray-600">${new Date(school.created_at).toLocaleDateString()}</td>
                            <td class="px-6 py-4">${actionButtons}</td>
                        `;
                        tbody.appendChild(row);
                    });
                } else {
                    tbody.innerHTML = '<tr><td colspan="7" class="px-6 py-8 text-center text-red-500">Error loading schools: ' + (data.message || 'Unknown error') + '</td></tr>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                tbody.innerHTML = '<tr><td colspan="7" class="px-6 py-8 text-center text-red-500">Network error occurred. Please try again.</td></tr>';
            });
    }

    // Helper functions for school management
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    function getStatusBadge(status) {
        const badges = {
            'active': '<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Active</span>',
            'inactive': '<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Pending</span>',
            'rejected': '<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Rejected</span>'
        };
        return badges[status] || '<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">Unknown</span>';
    }

    function getActionButtons(school) {
        let buttons = '';

        if (school.status === 'inactive') {
            buttons += `
                <button onclick="approveSchool(${school.id})" class="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 mr-2">
                    <i class="fas fa-check mr-1"></i>Approve
                </button>
                <button onclick="rejectSchool(${school.id})" class="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700 mr-2">
                    <i class="fas fa-times mr-1"></i>Reject
                </button>
            `;
        }

        buttons += `
            <button onclick="viewSchoolDetails(${school.id})" class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">
                <i class="fas fa-eye mr-1"></i>View
            </button>
        `;

        return '<div class="flex space-x-1">' + buttons + '</div>';
    }

    function updateTabStyling(activeStatus) {
        // Reset all tab buttons
        document.querySelectorAll('.tab-button').forEach(btn => {
            btn.className = 'px-4 py-1 rounded-lg border text-gray-600 hover:bg-gray-50 tab-button';
        });

        // Set active tab styling
        const tabMap = {
            'all': 'border-indigo-600 text-indigo-600 bg-indigo-50',
            'inactive': 'border-yellow-600 text-yellow-600 bg-yellow-50',
            'active': 'border-green-600 text-green-600 bg-green-50'
        };

        const activeTab = document.querySelector(`[onclick="showSchoolTab('${activeStatus === 'inactive' ? 'pending' : activeStatus}')"]`);
        if (activeTab) {
            activeTab.className = `px-4 py-1 rounded-lg border ${tabMap[activeStatus]} tab-button`;
        }
    }

    function showSchoolTab(tabName) {
        if (tabName === 'pending') {
            filterSchools('inactive');
        } else if (tabName === 'active') {
            filterSchools('active');
        } else {
            filterSchools('all');
        }
    }

    function approveSchool(id) {
        if (confirm('Are you sure you want to approve this school?')) {
            fetch(`/admin/schools/approve/${id}`, {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert("School approved successfully!");
                    filterSchools('all');
                } else {
                    alert(data.message || 'Failed to approve school');
                }
            })
            .catch(error => {
                alert(error.message || 'Network error occurred');
            });
        }
    }

    function rejectSchool(id) {
        const reason = prompt('Please enter reason for rejection:');
        if (reason) {
            fetch(`/admin/schools/reject/${id}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ reason: reason })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert("School rejected successfully!");
                    filterSchools('all');
                } else {
                    alert(data.message || 'Failed to reject school');
                }
            })
            .catch(error => {
                alert(error.message || 'Network error occurred');
            });
        }
    }
    

    document.addEventListener('DOMContentLoaded', function() {
        showSection('dashboard');
    });

    // Enhanced search functionality
    document.getElementById('school-search')?.addEventListener('input', function(e) {
        const searchTerm = e.target.value.toLowerCase().trim();
        const rows = document.querySelectorAll('#schools-tbody tr');

        if (!searchTerm) {
            // Show all rows if search is empty
            rows.forEach(row => {
                row.style.display = '';
            });
            return;
        }

        rows.forEach(row => {
            // Skip loading/error rows
            if (row.cells.length < 7) {
                return;
            }

            // Search in school name, email, phone, and address
            const schoolName = row.cells[0].textContent.toLowerCase();
            const email = row.cells[1].textContent.toLowerCase();
            const phone = row.cells[2].textContent.toLowerCase();
            const address = row.cells[3].textContent.toLowerCase();

            const isMatch = schoolName.includes(searchTerm) ||
                           email.includes(searchTerm) ||
                           phone.includes(searchTerm) ||
                           address.includes(searchTerm);

            row.style.display = isMatch ? '' : 'none';
        });
    });

      function viewSchoolDetails(schoolId) {
        openViewSchoolModal(schoolId);
    }

    // Add School Modal Functions
    function openAddSchoolModal() {
        document.getElementById('addSchoolModal').classList.remove('hidden');
        document.body.style.overflow = 'hidden';
        // Clear form
        document.getElementById('addSchoolForm').reset();
        document.getElementById('addSchoolMessages').classList.add('hidden');
    }

    function closeAddSchoolModal() {
        document.getElementById('addSchoolModal').classList.add('hidden');
        document.body.style.overflow = 'auto';
        // Clear form and messages
        document.getElementById('addSchoolForm').reset();
        document.getElementById('addSchoolMessages').classList.add('hidden');
    }

    // Close modal when clicking outside
    document.getElementById('addSchoolModal')?.addEventListener('click', function(e) {
        if (e.target === this) {
            closeAddSchoolModal();
        }
    });

    // Close modal button
    document.getElementById('closeAddSchoolModal')?.addEventListener('click', closeAddSchoolModal);

    // Handle Add School Form Submission
    document.getElementById('addSchoolForm')?.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;

        // Validate passwords match
        if (formData.get('password') !== formData.get('confirm_password')) {
            showAddSchoolMessage('Passwords do not match!', 'error');
            return;
        }

        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Adding School...';

        // Clear previous messages
        document.getElementById('addSchoolMessages').classList.add('hidden');

        fetch('<?= site_url('school/register') ?>', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.text())
        .then(data => {
            // Check if the response indicates success
            if (data.includes('success') || data.includes('Registration successful')) {
                showAddSchoolMessage('School added successfully!', 'success');
                setTimeout(() => {
                    closeAddSchoolModal();
                    // Refresh the schools list
                    filterSchools('all');
                }, 2000);
            } else if (data.includes('already registered') || data.includes('Duplicate entry')) {
                showAddSchoolMessage('Email address is already registered!', 'error');
            } else if (data.includes('error') || data.includes('failed')) {
                showAddSchoolMessage('Failed to add school. Please check all fields and try again.', 'error');
            } else {
                showAddSchoolMessage('School added successfully!', 'success');
                setTimeout(() => {
                    closeAddSchoolModal();
                    filterSchools('all');
                }, 2000);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAddSchoolMessage('Network error occurred. Please try again.', 'error');
        })
        .finally(() => {
            // Reset button state
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        });
    });

    function showAddSchoolMessage(message, type) {
        const messagesDiv = document.getElementById('addSchoolMessages');
        const alertClass = type === 'error' ? 'bg-red-100 text-red-800 border-red-200' : 'bg-green-100 text-green-800 border-green-200';
        messagesDiv.innerHTML = `
            <div class="p-3 rounded border ${alertClass}">
                ${message}
            </div>
        `;
        messagesDiv.classList.remove('hidden');
    }



    // View School Details Modal Functions
    let currentSchoolId = null;

    function openViewSchoolModal(schoolId) {
        currentSchoolId = schoolId;
        document.getElementById('viewSchoolModal').classList.remove('hidden');
        document.body.style.overflow = 'hidden';

        // Show loading state
        document.getElementById('schoolDetailsLoading').classList.remove('hidden');
        document.getElementById('schoolDetailsContent').classList.add('hidden');
        document.getElementById('schoolDetailsError').classList.add('hidden');

        // Fetch school details
        fetchSchoolDetails(schoolId);
    }

    function closeViewSchoolModal() {
        document.getElementById('viewSchoolModal').classList.add('hidden');
        document.body.style.overflow = 'auto';
        currentSchoolId = null;
    }

    // Close modal when clicking outside
    document.getElementById('viewSchoolModal')?.addEventListener('click', function(e) {
        if (e.target === this) {
            closeViewSchoolModal();
        }
    });

    // Close modal button
    document.getElementById('closeViewSchoolModal')?.addEventListener('click', closeViewSchoolModal);

    function fetchSchoolDetails(schoolId) {
        // Since we don't have a specific endpoint, we'll get the data from the current table
        // or we can create a new endpoint. For now, let's simulate fetching from existing data

        // Try to get school data from the current table
        const schoolRows = document.querySelectorAll('#schools-tbody tr');
        let schoolData = null;

        schoolRows.forEach(row => {
            const viewButton = row.querySelector(`button[onclick*="${schoolId}"]`);
            if (viewButton) {
                const cells = row.cells;
                if (cells.length >= 6) {
                    schoolData = {
                        id: schoolId,
                        name: cells[0].textContent.trim(),
                        email: cells[1].textContent.trim(),
                        phone: cells[2].textContent.trim(),
                        address: cells[3].textContent.trim(),
                        status: cells[4].querySelector('span')?.textContent.trim() || 'Unknown',
                        created_at: cells[5].textContent.trim(),
                        plan_id: 'N/A', // We don't have this in the table
                        updated_at: 'N/A' // We don't have this in the table
                    };
                }
            }
        });

        if (schoolData) {
            displaySchoolDetails(schoolData);
        } else {
            // Fallback: try to fetch from server
            fetch(`/admin/schools/all`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const school = data.schools.find(s => s.id == schoolId);
                        if (school) {
                            displaySchoolDetails(school);
                        } else {
                            showSchoolDetailsError();
                        }
                    } else {
                        showSchoolDetailsError();
                    }
                })
                .catch(error => {
                    console.error('Error fetching school details:', error);
                    showSchoolDetailsError();
                });
        }
    }

    function displaySchoolDetails(school) {
        // Hide loading, show content
        document.getElementById('schoolDetailsLoading').classList.add('hidden');
        document.getElementById('schoolDetailsContent').classList.remove('hidden');
        document.getElementById('schoolDetailsError').classList.add('hidden');

        // Populate school details
        document.getElementById('school-name').textContent = school.name || 'N/A';
        document.getElementById('school-email').textContent = school.email || 'N/A';
        document.getElementById('school-phone').textContent = school.phone || 'N/A';
        document.getElementById('school-address').textContent = school.address || 'N/A';
        document.getElementById('school-plan').textContent = getPlanName(school.plan_id) || 'N/A';
        document.getElementById('school-created').textContent = formatDate(school.created_at) || 'N/A';
        document.getElementById('school-updated').textContent = formatDate(school.updated_at) || 'N/A';

        // Set status badge
        const statusElement = document.getElementById('school-status');
        const statusBadge = getStatusBadge(school.status);
        statusElement.outerHTML = '<span id="school-status">' + statusBadge + '</span>';

        // Show/hide rejection reason
        const rejectionContainer = document.getElementById('rejection-reason-container');
        const rejectionReason = document.getElementById('school-rejection-reason');
        if (school.rejection_reason) {
            rejectionReason.textContent = school.rejection_reason;
            rejectionContainer.classList.remove('hidden');
        } else {
            rejectionContainer.classList.add('hidden');
        }

        // Show/hide action buttons based on status
        const approveBtn = document.getElementById('approve-school-btn');
        const rejectBtn = document.getElementById('reject-school-btn');

        if (school.status === 'inactive') {
            approveBtn.classList.remove('hidden');
            rejectBtn.classList.remove('hidden');
        } else {
            approveBtn.classList.add('hidden');
            rejectBtn.classList.add('hidden');
        }
    }

    function showSchoolDetailsError() {
        document.getElementById('schoolDetailsLoading').classList.add('hidden');
        document.getElementById('schoolDetailsContent').classList.add('hidden');
        document.getElementById('schoolDetailsError').classList.remove('hidden');
    }

    function getPlanName(planId) {
        const plans = {
            '1': 'Free Trial (30 days)',
            '2': 'Professional ($49/month)',
            '3': 'Enterprise ($99/month)'
        };
        return plans[planId] || `Plan ${planId}`;
    }

    function formatDate(dateString) {
        if (!dateString || dateString === 'N/A') return 'N/A';
        try {
            return new Date(dateString).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch (e) {
            return dateString;
        }
    }

    function approveSchoolFromModal() {
        if (currentSchoolId && confirm('Are you sure you want to approve this school?')) {
            approveSchool(currentSchoolId);
            closeViewSchoolModal();
        }
    }

    function rejectSchoolFromModal() {
        if (currentSchoolId) {
            rejectSchool(currentSchoolId);
            closeViewSchoolModal();
        }
    }

    // User Management Functions
    let allUsers = [];
    let filteredUsers = [];

    function loadUsers() {
        fetch('<?= site_url('superadmin/getUsers') ?>', {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                allUsers = data.users;
                filteredUsers = [...allUsers];
                displayUsers(filteredUsers);
            } else {
                showError('Failed to load users: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error loading users:', error);
            showError('Failed to load users');
        });
    }

    function displayUsers(users) {
        const usersContainer = document.getElementById('users-list');
        const loadingElement = document.getElementById('users-loading');

        if (loadingElement) {
            loadingElement.style.display = 'none';
        }

        if (users.length === 0) {
            usersContainer.innerHTML = `
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-users text-4xl mb-4"></i>
                    <p>No users found</p>
                </div>
            `;
            return;
        }

        const usersHTML = users.map(user => {
            const initials = user.name.split(' ').map(n => n[0]).join('').toUpperCase();
            const statusClass = user.status === 'active' ? 'status-active' : 'status-expired';
            const statusText = user.status === 'active' ? 'Active' : 'Inactive';

            return `
                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-indigo-600 rounded-full flex items-center justify-center text-white font-semibold mr-4">
                            ${initials}
                        </div>
                        <div>
                            <p class="text-lg font-bold text-gray-900">${user.name}</p>
                            <p class="text-sm font-medium text-indigo-600">
                                <i class="fas fa-school mr-1"></i>${user.school_name || 'No School Assigned'}
                            </p>
                            <p class="text-sm text-gray-600">${user.email}</p>
                            ${user.designation ? `<p class="text-xs text-gray-500"><i class="fas fa-user-tag mr-1"></i>${user.designation}</p>` : ''}
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <span class="${statusClass} px-2 py-1 rounded-full text-xs font-medium">${statusText}</span>
                        <button onclick="toggleUserStatus(${user.id})" class="text-blue-600 hover:text-blue-800" title="Toggle Status">
                            <i class="fas fa-toggle-${user.status === 'active' ? 'on' : 'off'}"></i>
                        </button>
                        <button onclick="viewUserDetails(${user.id})" class="text-indigo-600 hover:text-indigo-800" title="View Details">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button onclick="deleteUser(${user.id})" class="text-red-600 hover:text-red-800" title="Delete User">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
        }).join('');

        usersContainer.innerHTML = usersHTML;
    }

    function toggleUserStatus(userId) {
        if (!confirm('Are you sure you want to toggle this user\'s status?')) {
            return;
        }

        fetch(`<?= site_url('superadmin/toggleUserStatus') ?>/${userId}`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess(data.message);
                loadUsers(); // Reload users to reflect changes
            } else {
                showError(data.message);
            }
        })
        .catch(error => {
            console.error('Error toggling user status:', error);
            showError('Failed to update user status');
        });
    }

    function deleteUser(userId) {
        if (!confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
            return;
        }

        fetch(`<?= site_url('superadmin/deleteUser') ?>/${userId}`, {
            method: 'DELETE',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess(data.message);
                loadUsers(); // Reload users to reflect changes
            } else {
                showError(data.message);
            }
        })
        .catch(error => {
            console.error('Error deleting user:', error);
            showError('Failed to delete user');
        });
    }

    function viewUserDetails(userId) {
        fetch(`<?= site_url('superadmin/getUserDetails') ?>/${userId}`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showUserDetailsModal(data.user);
            } else {
                showError(data.message);
            }
        })
        .catch(error => {
            console.error('Error fetching user details:', error);
            showError('Failed to load user details');
        });
    }

    function showUserDetailsModal(user) {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4';
        modal.innerHTML = `
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                <div class="flex justify-between items-center p-6 border-b">
                    <h2 class="text-xl font-semibold text-gray-800">User Details</h2>
                    <button onclick="this.closest('.fixed').remove()" class="text-gray-400 hover:text-gray-600 text-2xl font-bold">
                        &times;
                    </button>
                </div>
                <div class="p-6">
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Name</label>
                            <p class="text-gray-900 font-medium">${user.name}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Email</label>
                            <p class="text-gray-900">${user.email}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">School</label>
                            <p class="text-gray-900">${user.school_name || 'No School Assigned'}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Status</label>
                            <span class="${user.status === 'active' ? 'status-active' : 'status-expired'} px-2 py-1 rounded-full text-xs font-medium">
                                ${user.status === 'active' ? 'Active' : 'Inactive'}
                            </span>
                        </div>
                        ${user.designation ? `
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Designation</label>
                            <p class="text-gray-900">${user.designation}</p>
                        </div>
                        ` : ''}
                        ${user.phone ? `
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Phone</label>
                            <p class="text-gray-900">${user.phone}</p>
                        </div>
                        ` : ''}
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Created At</label>
                            <p class="text-gray-900">${new Date(user.created_at).toLocaleDateString()}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Last Updated</label>
                            <p class="text-gray-900">${new Date(user.updated_at).toLocaleDateString()}</p>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3 pt-6 border-t mt-6">
                        <button onclick="this.closest('.fixed').remove()" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    // Search and filter functionality
    document.addEventListener('DOMContentLoaded', function() {
        const userSearch = document.getElementById('user-search');
        const userRoleFilter = document.getElementById('user-role-filter');

        if (userSearch) {
            userSearch.addEventListener('input', function() {
                filterUsers();
            });
        }

        if (userRoleFilter) {
            userRoleFilter.addEventListener('change', function() {
                filterUsers();
            });
        }
    });

    function filterUsers() {
        const searchTerm = document.getElementById('user-search').value.toLowerCase();
        const statusFilter = document.getElementById('user-role-filter').value;

        filteredUsers = allUsers.filter(user => {
            const matchesSearch = user.name.toLowerCase().includes(searchTerm) ||
                                user.email.toLowerCase().includes(searchTerm) ||
                                (user.school_name && user.school_name.toLowerCase().includes(searchTerm));

            const matchesStatus = !statusFilter || user.status === statusFilter;

            return matchesSearch && matchesStatus;
        });

        displayUsers(filteredUsers);
    }

    // Audit Logs Functions
    let currentAuditPage = 1;
    let auditFilters = {};

    function loadAuditLogs(page = 1) {
        currentAuditPage = page;
        const params = new URLSearchParams({
            page: page,
            per_page: 20,
            ...auditFilters
        });

        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        const headers = {
            'X-Requested-With': 'XMLHttpRequest'
        };

        if (csrfToken) {
            headers['X-CSRF-TOKEN'] = csrfToken.getAttribute('content');
        }

        fetch(`<?= site_url('superadmin/getAuditLogs') ?>?${params}`, {
            method: 'GET',
            headers: headers,
            credentials: 'same-origin'
        })
        .then(response => {
            console.log('Audit logs response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Audit logs data:', data);
            if (data.success) {
                displayAuditLogs(data.data.logs);
                updateAuditPagination(data.data);
            } else {
                console.error('Audit logs error:', data.message);
                showError('Failed to load audit logs: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error loading audit logs:', error);
            showError('Failed to load audit logs');
        });
    }

    function displayAuditLogs(logs) {
        const container = document.getElementById('audit-logs-list');
        const loading = document.getElementById('audit-logs-loading');

        loading.style.display = 'none';
        container.classList.remove('hidden');

        if (logs.length === 0) {
            container.innerHTML = `
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-history text-4xl mb-4"></i>
                    <p>No audit logs found</p>
                </div>
            `;
            return;
        }

        const logsHTML = logs.map(log => {
            const severityClass = getSeverityClass(log.severity);
            const statusClass = getStatusClass(log.status);
            const date = new Date(log.created_at).toLocaleString();

            return `
                <div class="bg-gray-50 p-4 rounded-lg border-l-4 ${severityClass}">
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <div class="flex items-center space-x-3 mb-2">
                                <span class="font-semibold text-gray-900">${log.action.toUpperCase()}</span>
                                <span class="text-sm text-gray-600">${log.entity_type}</span>
                                <span class="px-2 py-1 rounded-full text-xs font-medium ${statusClass}">
                                    ${log.status.toUpperCase()}
                                </span>
                                <span class="px-2 py-1 rounded-full text-xs font-medium ${getSeverityBadgeClass(log.severity)}">
                                    ${log.severity.toUpperCase()}
                                </span>
                            </div>
                            <p class="text-gray-700 mb-2">${log.description || 'No description'}</p>
                            <div class="flex items-center space-x-4 text-sm text-gray-500">
                                <span><i class="fas fa-user mr-1"></i>${log.user_name || 'System'}</span>
                                <span><i class="fas fa-school mr-1"></i>${log.school_name || 'N/A'}</span>
                                <span><i class="fas fa-clock mr-1"></i>${date}</span>
                                ${log.ip_address ? `<span><i class="fas fa-globe mr-1"></i>${log.ip_address}</span>` : ''}
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            ${log.old_values || log.new_values ? `
                                <button onclick="viewAuditDetails(${log.id})" class="text-indigo-600 hover:text-indigo-800" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </button>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        container.innerHTML = logsHTML;
    }

    function getSeverityClass(severity) {
        switch(severity) {
            case 'critical': return 'border-red-500';
            case 'high': return 'border-orange-500';
            case 'medium': return 'border-yellow-500';
            case 'low': return 'border-green-500';
            default: return 'border-gray-500';
        }
    }

    function getSeverityBadgeClass(severity) {
        switch(severity) {
            case 'critical': return 'bg-red-100 text-red-800';
            case 'high': return 'bg-orange-100 text-orange-800';
            case 'medium': return 'bg-yellow-100 text-yellow-800';
            case 'low': return 'bg-green-100 text-green-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    }

    function getStatusClass(status) {
        switch(status) {
            case 'success': return 'bg-green-100 text-green-800';
            case 'failed': return 'bg-red-100 text-red-800';
            case 'warning': return 'bg-yellow-100 text-yellow-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    }

    function updateAuditPagination(data) {
        const pagination = document.getElementById('audit-pagination');
        const info = document.getElementById('audit-pagination-info');
        const buttons = document.getElementById('audit-pagination-buttons');

        if (data.totalPages <= 1) {
            pagination.classList.add('hidden');
            return;
        }

        pagination.classList.remove('hidden');

        const start = ((data.page - 1) * data.perPage) + 1;
        const end = Math.min(data.page * data.perPage, data.total);
        info.textContent = `Showing ${start}-${end} of ${data.total} logs`;

        let buttonsHTML = '';

        // Previous button
        if (data.page > 1) {
            buttonsHTML += `<button onclick="loadAuditLogs(${data.page - 1})" class="px-3 py-2 border rounded-lg hover:bg-gray-50">Previous</button>`;
        }

        // Page numbers
        const startPage = Math.max(1, data.page - 2);
        const endPage = Math.min(data.totalPages, data.page + 2);

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === data.page ? 'bg-indigo-600 text-white' : 'hover:bg-gray-50';
            buttonsHTML += `<button onclick="loadAuditLogs(${i})" class="px-3 py-2 border rounded-lg ${activeClass}">${i}</button>`;
        }

        // Next button
        if (data.page < data.totalPages) {
            buttonsHTML += `<button onclick="loadAuditLogs(${data.page + 1})" class="px-3 py-2 border rounded-lg hover:bg-gray-50">Next</button>`;
        }

        buttons.innerHTML = buttonsHTML;
    }

    function loadAuditStats() {
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        const headers = {
            'X-Requested-With': 'XMLHttpRequest'
        };

        if (csrfToken) {
            headers['X-CSRF-TOKEN'] = csrfToken.getAttribute('content');
        }

        fetch('<?= site_url('superadmin/getAuditStats') ?>', {
            method: 'GET',
            headers: headers,
            credentials: 'same-origin'
        })
        .then(response => {
            console.log('Audit stats response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Audit stats data:', data);
            if (data.success) {
                updateAuditStats(data.stats);
            } else {
                console.error('Audit stats error:', data.message);
            }
        })
        .catch(error => {
            console.error('Error loading audit stats:', error);
        });
    }

    function updateAuditStats(stats) {
        document.getElementById('total-actions').textContent = stats.total_actions || 0;

        // Calculate today's actions
        const today = new Date().toISOString().split('T')[0];
        const todayActions = stats.actions_by_type?.reduce((sum, action) => sum + parseInt(action.count), 0) || 0;
        document.getElementById('today-actions').textContent = todayActions;

        // Calculate critical events
        const criticalEvents = stats.actions_by_severity?.find(s => s.severity === 'critical')?.count || 0;
        document.getElementById('critical-events').textContent = criticalEvents;

        // Calculate failed actions
        const failedActions = stats.actions_by_status?.find(s => s.status === 'failed')?.count || 0;
        document.getElementById('failed-actions').textContent = failedActions;
    }

    function loadAuditFilterOptions() {
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        const headers = {
            'X-Requested-With': 'XMLHttpRequest'
        };

        if (csrfToken) {
            headers['X-CSRF-TOKEN'] = csrfToken.getAttribute('content');
        }

        fetch('<?= site_url('superadmin/getAuditFilterOptions') ?>', {
            method: 'GET',
            headers: headers,
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                populateAuditFilters(data.options);
            }
        })
        .catch(error => {
            console.error('Error loading filter options:', error);
        });
    }

    function populateAuditFilters(options) {
        // Populate action filter
        const actionFilter = document.getElementById('audit-action-filter');
        actionFilter.innerHTML = '<option value="">All Actions</option>';
        options.actions.forEach(action => {
            actionFilter.innerHTML += `<option value="${action}">${action.toUpperCase()}</option>`;
        });

        // Populate school filter
        const schoolFilter = document.getElementById('audit-school-filter');
        schoolFilter.innerHTML = '<option value="">All Schools</option>';
        options.schools.forEach(school => {
            schoolFilter.innerHTML += `<option value="${school.id}">${school.name}</option>`;
        });
    }

    function refreshAuditLogs() {
        loadAuditLogs(1);
        loadAuditStats();
    }

    function exportAuditLogs() {
        const params = new URLSearchParams(auditFilters);
        window.open(`<?= site_url('superadmin/exportAuditLogs') ?>?${params}`, '_blank');
    }

    function applyAuditFilters() {
        auditFilters = {
            search: document.getElementById('audit-search').value,
            date_from: document.getElementById('audit-date-from').value,
            date_to: document.getElementById('audit-date-to').value,
            action: document.getElementById('audit-action-filter').value,
            severity: document.getElementById('audit-severity-filter').value,
            school_id: document.getElementById('audit-school-filter').value
        };

        // Remove empty filters
        Object.keys(auditFilters).forEach(key => {
            if (!auditFilters[key]) {
                delete auditFilters[key];
            }
        });

        loadAuditLogs(1);
    }

    function viewAuditDetails(logId) {
        // This would show a modal with detailed audit information
        // For now, we'll just show an alert
        showSuccess('Audit details view would be implemented here for log ID: ' + logId);
    }

    // Add event listeners for audit filters
    document.addEventListener('DOMContentLoaded', function() {
        const auditSearch = document.getElementById('audit-search');
        const auditDateFrom = document.getElementById('audit-date-from');
        const auditDateTo = document.getElementById('audit-date-to');
        const auditActionFilter = document.getElementById('audit-action-filter');
        const auditSeverityFilter = document.getElementById('audit-severity-filter');
        const auditSchoolFilter = document.getElementById('audit-school-filter');

        // Add debounced search
        let searchTimeout;
        if (auditSearch) {
            auditSearch.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(applyAuditFilters, 500);
            });
        }

        // Add change listeners for other filters
        [auditDateFrom, auditDateTo, auditActionFilter, auditSeverityFilter, auditSchoolFilter].forEach(element => {
            if (element) {
                element.addEventListener('change', applyAuditFilters);
            }
        });
    });

    // Mobile sidebar toggle function
    function toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.toggle('-translate-x-full');
    }

    // Close sidebar when clicking outside on mobile
    document.addEventListener('click', function(event) {
        const sidebar = document.getElementById('sidebar');
        const toggleButton = event.target.closest('button[onclick="toggleSidebar()"]');

        if (!sidebar.contains(event.target) && !toggleButton && window.innerWidth < 1024) {
            sidebar.classList.add('-translate-x-full');
        }
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        const sidebar = document.getElementById('sidebar');
        if (window.innerWidth >= 1024) {
            sidebar.classList.remove('-translate-x-full');
        }
    });

    // Recent Activities Functions
    function loadRecentActivities() {
        const activitiesList = document.getElementById('recent-activities-list');

        activitiesList.innerHTML = `
            <div class="text-center py-8 text-gray-500">
                <i class="fas fa-spinner fa-spin text-2xl mb-3"></i>
                <p>Loading recent activities...</p>
            </div>
        `;

        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        const headers = {
            'X-Requested-With': 'XMLHttpRequest'
        };

        if (csrfToken) {
            headers['X-CSRF-TOKEN'] = csrfToken.getAttribute('content');
        }

        fetch('<?= site_url('superadmin/getRecentActivities') ?>', {
            method: 'GET',
            headers: headers,
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayRecentActivities(data.activities);
            } else {
                activitiesList.innerHTML = `
                    <div class="text-center py-8 text-red-500">
                        <i class="fas fa-exclamation-triangle text-2xl mb-3"></i>
                        <p>Failed to load activities: ${data.message}</p>
                        <button onclick="loadRecentActivities()" class="mt-3 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                            Try Again
                        </button>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading activities:', error);
            activitiesList.innerHTML = `
                <div class="text-center py-8 text-red-500">
                    <i class="fas fa-exclamation-triangle text-2xl mb-3"></i>
                    <p>Network error occurred. Please try again.</p>
                    <button onclick="loadRecentActivities()" class="mt-3 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                        Try Again
                    </button>
                </div>
            `;
        });
    }

    function displayRecentActivities(activities) {
        const activitiesList = document.getElementById('recent-activities-list');

        if (!activities || activities.length === 0) {
            activitiesList.innerHTML = `
                <div class="text-center py-12 text-gray-500">
                    <i class="fas fa-clock text-4xl mb-4"></i>
                    <p class="text-lg font-medium">No recent activities</p>
                    <p class="text-sm mt-2">Activities will appear here as actions are performed</p>
                </div>
            `;
            return;
        }

        let html = '';
        activities.forEach(activity => {
            const timeAgo = formatTimeAgo(activity.time);
            const colorClass = getColorClass(activity.color);

            html += `
                <div class="flex items-center p-4 bg-gradient-to-r ${colorClass.bg} rounded-lg border-l-4 ${colorClass.border} hover:shadow-md transition-shadow">
                    <div class="w-10 h-10 ${colorClass.iconBg} rounded-full flex items-center justify-center mr-4">
                        <i class="${activity.icon} text-white text-sm"></i>
                    </div>
                    <div class="flex-1">
                        <p class="font-semibold text-gray-800">${escapeHtml(activity.title)}</p>
                        <p class="text-sm text-gray-600">${escapeHtml(activity.description)}</p>
                    </div>
                    <span class="text-sm text-gray-500 bg-white px-2 py-1 rounded-full">${timeAgo}</span>
                </div>
            `;
        });

        activitiesList.innerHTML = html;
    }

    function getColorClass(color) {
        const colorClasses = {
            'blue': {
                bg: 'from-blue-50 to-blue-100',
                border: 'border-blue-500',
                iconBg: 'bg-blue-600'
            },
            'green': {
                bg: 'from-green-50 to-green-100',
                border: 'border-green-500',
                iconBg: 'bg-green-600'
            },
            'yellow': {
                bg: 'from-yellow-50 to-yellow-100',
                border: 'border-yellow-500',
                iconBg: 'bg-yellow-600'
            },
            'red': {
                bg: 'from-red-50 to-red-100',
                border: 'border-red-500',
                iconBg: 'bg-red-600'
            },
            'purple': {
                bg: 'from-purple-50 to-purple-100',
                border: 'border-purple-500',
                iconBg: 'bg-purple-600'
            },
            'gray': {
                bg: 'from-gray-50 to-gray-100',
                border: 'border-gray-500',
                iconBg: 'bg-gray-600'
            }
        };

        return colorClasses[color] || colorClasses['gray'];
    }

    function formatTimeAgo(dateString) {
        const now = new Date();
        const date = new Date(dateString);
        const diffInSeconds = Math.floor((now - date) / 1000);

        if (diffInSeconds < 60) {
            return 'Just now';
        } else if (diffInSeconds < 3600) {
            const minutes = Math.floor(diffInSeconds / 60);
            return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
        } else if (diffInSeconds < 86400) {
            const hours = Math.floor(diffInSeconds / 3600);
            return `${hours} hour${hours > 1 ? 's' : ''} ago`;
        } else {
            const days = Math.floor(diffInSeconds / 86400);
            return `${days} day${days > 1 ? 's' : ''} ago`;
        }
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Load recent activities when dashboard loads
    document.addEventListener('DOMContentLoaded', function() {
        loadRecentActivities();

        // Auto-refresh activities every 5 minutes
        setInterval(loadRecentActivities, 300000);

        // Notification functionality
        setupNotifications();
    });

    // Notification System
    function setupNotifications() {
        const notificationBtn = document.getElementById('notificationBtn');
        const notificationDropdown = document.getElementById('notificationDropdown');
        const refreshBtn = document.getElementById('refreshNotifications');

        // Toggle notification dropdown
        notificationBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            notificationDropdown.classList.toggle('hidden');
            if (!notificationDropdown.classList.contains('hidden')) {
                loadNotifications();
            }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!notificationDropdown.contains(e.target) && !notificationBtn.contains(e.target)) {
                notificationDropdown.classList.add('hidden');
            }
        });

        // Refresh notifications
        refreshBtn.addEventListener('click', function() {
            loadNotifications();
        });
    }

    function loadNotifications() {
        const notificationList = document.getElementById('notificationList');

        notificationList.innerHTML = '<div class="p-4 text-center text-gray-500"><i class="fas fa-spinner fa-spin"></i> Loading notifications...</div>';

        fetch('/superadmin/getNotifications', {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayNotifications(data.notifications);
            } else {
                notificationList.innerHTML = '<div class="p-4 text-center text-red-500">Failed to load notifications</div>';
            }
        })
        .catch(error => {
            console.error('Error loading notifications:', error);
            notificationList.innerHTML = '<div class="p-4 text-center text-red-500">Error loading notifications</div>';
        });
    }

    function displayNotifications(notifications) {
        const notificationList = document.getElementById('notificationList');

        if (notifications.length === 0) {
            notificationList.innerHTML = '<div class="p-4 text-center text-gray-500">No new notifications</div>';
            return;
        }

        let html = '';
        notifications.forEach(notification => {
            const timeAgo = getTimeAgo(notification.time);
            const colorClass = getNotificationColorClass(notification.color);

            html += `
                <div class="p-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer" onclick="event.preventDefault(); handleNotificationClick('${notification.section || extractSectionFromUrl(notification.action_url)}'); return false;">
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 ${colorClass} rounded-full flex items-center justify-center">
                                <i class="${notification.icon} text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900">${notification.title}</p>
                            <p class="text-sm text-gray-600">${notification.message}</p>
                            <p class="text-xs text-gray-400 mt-1">${timeAgo}</p>
                        </div>
                    </div>
                </div>
            `;
        });

        notificationList.innerHTML = html;
    }

    function getNotificationColorClass(color) {
        const colorMap = {
            'red': 'bg-red-500',
            'orange': 'bg-orange-500',
            'yellow': 'bg-yellow-500',
            'green': 'bg-green-500',
            'blue': 'bg-blue-500',
            'indigo': 'bg-indigo-500',
            'purple': 'bg-purple-500'
        };
        return colorMap[color] || 'bg-gray-500';
    }

    function extractSectionFromUrl(actionUrl) {
        if (!actionUrl) return '';

        // Extract section from URLs like '/superadmin/schools' -> 'schools'
        const urlParts = actionUrl.split('/');
        const lastPart = urlParts[urlParts.length - 1];

        // Map common URL patterns to sections
        const sectionMap = {
            'schools': 'schools',
            'users': 'users',
            'subscriptions': 'subscriptions',
            'analytics': 'analytics',
            'system': 'system',
            'audit': 'audit'
        };

        return sectionMap[lastPart] || '';
    }

    function handleNotificationClick(section) {
        console.log('Notification clicked, navigating to section:', section); // Debug log

        // Prevent default link behavior
        if (event) {
            event.preventDefault();
            event.stopPropagation();
        }

        if (section && section.trim() !== '') {
            console.log('Navigating to section:', section); // Debug log

            // Close notification dropdown
            const dropdown = document.getElementById('notificationDropdown');
            if (dropdown) {
                dropdown.classList.add('hidden');
                console.log('Notification dropdown closed');
            }

            // Show the appropriate section
            console.log('Calling showSection with:', section);
            showSection(section);

            // Verify section was shown
            const targetSection = document.getElementById(section + '-section');
            if (targetSection) {
                console.log('Target section found:', targetSection);
                console.log('Section classes:', targetSection.className);
            } else {
                console.log('Target section NOT found:', section + '-section');
            }

            // If it's schools section, show pending tab by default
            if (section === 'schools') {
                console.log('Schools section - showing pending tab');
                setTimeout(() => {
                    if (typeof showSchoolTab === 'function') {
                        showSchoolTab('pending');
                    } else {
                        console.log('showSchoolTab function not found');
                    }
                }, 100);
            }
        } else {
            console.log('No valid section provided:', section);
        }
    }

    function getTimeAgo(dateString) {
        const now = new Date();
        const date = new Date(dateString);
        const diffInSeconds = Math.floor((now - date) / 1000);

        if (diffInSeconds < 60) return 'Just now';
        if (diffInSeconds < 3600) return Math.floor(diffInSeconds / 60) + ' minutes ago';
        if (diffInSeconds < 86400) return Math.floor(diffInSeconds / 3600) + ' hours ago';
        if (diffInSeconds < 2592000) return Math.floor(diffInSeconds / 86400) + ' days ago';
        return Math.floor(diffInSeconds / 2592000) + ' months ago';
    }
</script>

<!-- Flash Messages -->
<?php if (session()->getFlashdata('success')): ?>
    <div class="bg-green-100 text-green-800 px-4 py-2 rounded mb-4">
        <?= session()->getFlashdata('success') ?>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('error')): ?>
    <div class="bg-red-100 text-red-800 px-4 py-2 rounded mb-4">
        <?= session()->getFlashdata('error') ?>
    </div>
<?php endif; ?>



</body>
</html>
