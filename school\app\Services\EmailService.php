<?php

namespace App\Services;

use CodeIgniter\Config\Services;

class EmailService
{
    protected $email;
    protected $emailLogModel;

    public function __construct()
    {
        $this->email = Services::email();
        $this->emailLogModel = new \App\Models\EmailLogModel();
    }

    /**
     * Send welcome email to new user
     */
    public function sendWelcomeEmail($userEmail, $userName, $password, $schoolName, $schoolId = null, $userId = null)
    {
        try {
            $subject = "Welcome to School Question Bank - Your Account Details";
            
            $message = $this->getWelcomeEmailTemplate($userName, $userEmail, $password, $schoolName);
            
            $this->email->setTo($userEmail);
            $this->email->setFrom('<EMAIL>', 'School Question Bank');
            $this->email->setSubject($subject);
            $this->email->setMessage($message);
            $this->email->setMailType('html');

            $result = $this->email->send();
            
            // Log email attempt
            $this->logEmail($schoolId, $userId, $userEmail, $subject, $message, $result ? 'sent' : 'failed');
            
            if (!$result) {
                log_message('error', 'Failed to send welcome email to: ' . $userEmail . ' - ' . $this->email->printDebugger(['headers']));
            }
            
            return $result;
            
        } catch (\Exception $e) {
            log_message('error', 'Email service error: ' . $e->getMessage());
            $this->logEmail($schoolId, $userId, $userEmail, $subject ?? 'Welcome Email', 'Error: ' . $e->getMessage(), 'failed');
            return false;
        }
    }

    /**
     * Get welcome email HTML template
     */
    private function getWelcomeEmailTemplate($userName, $userEmail, $password, $schoolName)
    {
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>Welcome to School Question Bank</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
                .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
                .credentials { background: #fff; padding: 20px; border-radius: 8px; border-left: 4px solid #667eea; margin: 20px 0; }
                .button { display: inline-block; background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
                .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
                .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>🎓 Welcome to School Question Bank!</h1>
                    <p>Your account has been created successfully</p>
                </div>
                
                <div class='content'>
                    <h2>Hello {$userName}!</h2>
                    
                    <p>You have been added to the <strong>{$schoolName}</strong> Question Bank system. You can now access the platform to manage questions, create papers, and collaborate with your team.</p>
                    
                    <div class='credentials'>
                        <h3>🔐 Your Login Credentials:</h3>
                        <p><strong>Email:</strong> {$userEmail}</p>
                        <p><strong>Password:</strong> <code style='background: #f1f1f1; padding: 4px 8px; border-radius: 4px; font-family: monospace;'>{$password}</code></p>
                    </div>
                    
                    <div class='warning'>
                        <strong>⚠️ Important Security Notice:</strong>
                        <ul>
                            <li>Please change your password after your first login</li>
                            <li>Keep your credentials secure and don't share them</li>
                            <li>Contact your administrator if you face any issues</li>
                        </ul>
                    </div>
                    
                    <div style='text-align: center;'>
                        <a href='" . base_url('staff/login') . "' class='button'>🚀 Login to Your Account</a>
                    </div>
                    
                    <h3>📚 What you can do:</h3>
                    <ul>
                        <li>Create and manage questions</li>
                        <li>Generate question papers</li>
                        <li>Collaborate with other staff members</li>
                        <li>Track your contributions</li>
                    </ul>
                </div>
                
                <div class='footer'>
                    <p>This email was sent from School Question Bank System</p>
                    <p>If you didn't expect this email, please contact your school administrator.</p>
                    <p>&copy; " . date('Y') . " School Question Bank. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>";
    }

    /**
     * Log email to database
     */
    private function logEmail($schoolId, $userId, $emailTo, $subject, $content, $status)
    {
        try {
            $this->emailLogModel->insert([
                'school_id' => $schoolId,
                'user_id' => $userId,
                'email_to' => $emailTo,
                'subject' => $subject,
                'content' => $content,
                'status' => $status,
                'sent_at' => $status === 'sent' ? date('Y-m-d H:i:s') : null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Failed to log email: ' . $e->getMessage());
        }
    }

    /**
     * Send password reset email
     */
    public function sendPasswordResetEmail($userEmail, $userName, $resetToken, $schoolId = null, $userId = null)
    {
        try {
            $subject = "Password Reset Request - School Question Bank";
            $resetLink = base_url("staff/reset-password/{$resetToken}");

            $message = "
            <h2>Password Reset Request</h2>
            <p>Hello {$userName},</p>
            <p>You requested a password reset for your School Question Bank account.</p>
            <p><a href='{$resetLink}' style='background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Reset Password</a></p>
            <p>This link will expire in 1 hour.</p>
            <p>If you didn't request this, please ignore this email.</p>
            ";

            $this->email->setTo($userEmail);
            $this->email->setFrom('<EMAIL>', 'School Question Bank');
            $this->email->setSubject($subject);
            $this->email->setMessage($message);
            $this->email->setMailType('html');

            $result = $this->email->send();
            $this->logEmail($schoolId, $userId, $userEmail, $subject, $message, $result ? 'sent' : 'failed');

            return $result;

        } catch (\Exception $e) {
            log_message('error', 'Password reset email error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Send payment success notification email
     */
    public function sendPaymentSuccessEmail($schoolEmail, $schoolName, $transactionData, $schoolId = null)
    {
        try {
            $subject = "Payment Successful - Subscription Activated";
            $message = $this->getPaymentSuccessEmailTemplate($schoolName, $transactionData);

            $this->email->setTo($schoolEmail);
            $this->email->setFrom(env('EMAIL_FROM_ADDRESS', '<EMAIL>'), env('EMAIL_FROM_NAME', 'School Question Bank'));
            $this->email->setSubject($subject);
            $this->email->setMessage($message);
            $this->email->setMailType('html');

            $result = $this->email->send();
            $this->logEmail($schoolId, null, $schoolEmail, $subject, $message, $result ? 'sent' : 'failed');

            return $result;

        } catch (\Exception $e) {
            log_message('error', 'Payment success email error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Send payment failure notification email
     */
    public function sendPaymentFailureEmail($schoolEmail, $schoolName, $transactionData, $schoolId = null)
    {
        try {
            $subject = "Payment Failed - Action Required";
            $message = $this->getPaymentFailureEmailTemplate($schoolName, $transactionData);

            $this->email->setTo($schoolEmail);
            $this->email->setFrom(env('EMAIL_FROM_ADDRESS', '<EMAIL>'), env('EMAIL_FROM_NAME', 'School Question Bank'));
            $this->email->setSubject($subject);
            $this->email->setMessage($message);
            $this->email->setMailType('html');

            $result = $this->email->send();
            $this->logEmail($schoolId, null, $schoolEmail, $subject, $message, $result ? 'sent' : 'failed');

            return $result;

        } catch (\Exception $e) {
            log_message('error', 'Payment failure email error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Send subscription expiry reminder email
     */
    public function sendSubscriptionExpiryReminder($schoolEmail, $schoolName, $subscriptionData, $schoolId = null)
    {
        try {
            $daysLeft = ceil((strtotime($subscriptionData['expires_at']) - time()) / (60 * 60 * 24));
            $subject = "Subscription Expiring in {$daysLeft} Days - Renew Now";
            $message = $this->getSubscriptionExpiryEmailTemplate($schoolName, $subscriptionData, $daysLeft);

            $this->email->setTo($schoolEmail);
            $this->email->setFrom(env('EMAIL_FROM_ADDRESS', '<EMAIL>'), env('EMAIL_FROM_NAME', 'School Question Bank'));
            $this->email->setSubject($subject);
            $this->email->setMessage($message);
            $this->email->setMailType('html');

            $result = $this->email->send();
            $this->logEmail($schoolId, null, $schoolEmail, $subject, $message, $result ? 'sent' : 'failed');

            return $result;

        } catch (\Exception $e) {
            log_message('error', 'Subscription expiry email error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get payment success email template
     */
    private function getPaymentSuccessEmailTemplate($schoolName, $transactionData)
    {
        $invoiceUrl = base_url("payment/invoice/{$transactionData['id']}");
        $dashboardUrl = base_url('schooladmin/dashboard');

        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
                .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
                .success-icon { font-size: 48px; margin-bottom: 20px; }
                .transaction-details { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #10b981; }
                .button { display: inline-block; background: #10b981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 5px; }
                .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <div class='success-icon'>✅</div>
                    <h1>Payment Successful!</h1>
                    <p>Your subscription has been activated</p>
                </div>

                <div class='content'>
                    <h2>Hello {$schoolName}!</h2>

                    <p>Great news! Your payment has been processed successfully and your subscription is now active.</p>

                    <div class='transaction-details'>
                        <h3>📋 Transaction Details</h3>
                        <p><strong>Transaction ID:</strong> {$transactionData['transaction_id']}</p>
                        <p><strong>Plan:</strong> {$transactionData['plan_name']}</p>
                        <p><strong>Amount:</strong> ₹" . number_format($transactionData['amount']) . "</p>
                        <p><strong>Billing Cycle:</strong> " . ucfirst($transactionData['billing_cycle']) . "</p>
                        <p><strong>Payment Date:</strong> " . date('M d, Y H:i', strtotime($transactionData['processed_at'])) . "</p>
                    </div>

                    <div style='text-align: center; margin: 30px 0;'>
                        <a href='{$dashboardUrl}' class='button'>🚀 Go to Dashboard</a>
                        <a href='{$invoiceUrl}' class='button' style='background: #3b82f6;'>📄 Download Invoice</a>
                    </div>

                    <h3>🎉 What's Next?</h3>
                    <ul>
                        <li>Access your dashboard to start creating questions</li>
                        <li>Invite your staff members to collaborate</li>
                        <li>Generate question papers with ease</li>
                        <li>Track your usage and analytics</li>
                    </ul>

                    <p>If you have any questions or need assistance, our support team is here to help!</p>
                </div>

                <div class='footer'>
                    <p>Thank you for choosing School Question Bank!</p>
                    <p>📧 <a href='mailto:" . env('EMAIL_ADMIN_ADDRESS', '<EMAIL>') . "'>Contact Support</a> | 📞 " . env('COMPANY_PHONE', '+91-XXXXXXXXXX') . "</p>
                </div>
            </div>
        </body>
        </html>";
    }

    /**
     * Get payment failure email template
     */
    private function getPaymentFailureEmailTemplate($schoolName, $transactionData)
    {
        $retryUrl = base_url('payment/process');
        $supportUrl = base_url('help/payment-issues');

        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
                .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
                .error-icon { font-size: 48px; margin-bottom: 20px; }
                .transaction-details { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ef4444; }
                .button { display: inline-block; background: #ef4444; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 5px; }
                .help-section { background: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f59e0b; }
                .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <div class='error-icon'>❌</div>
                    <h1>Payment Failed</h1>
                    <p>We couldn't process your payment</p>
                </div>

                <div class='content'>
                    <h2>Hello {$schoolName},</h2>

                    <p>We're sorry, but we encountered an issue while processing your payment. Don't worry - no charges have been made to your account.</p>

                    <div class='transaction-details'>
                        <h3>📋 Transaction Details</h3>
                        <p><strong>Transaction ID:</strong> {$transactionData['transaction_id']}</p>
                        <p><strong>Plan:</strong> {$transactionData['plan_name']}</p>
                        <p><strong>Amount:</strong> ₹" . number_format($transactionData['amount']) . "</p>
                        <p><strong>Failure Reason:</strong> " . ($transactionData['notes'] ?? 'Payment processing error') . "</p>
                        <p><strong>Attempt Date:</strong> " . date('M d, Y H:i', strtotime($transactionData['created_at'])) . "</p>
                    </div>

                    <div class='help-section'>
                        <h3>💡 Common Solutions</h3>
                        <ul>
                            <li>Check if you have sufficient funds in your account</li>
                            <li>Verify your card details are correct</li>
                            <li>Try using a different payment method</li>
                            <li>Contact your bank if the issue persists</li>
                        </ul>
                    </div>

                    <div style='text-align: center; margin: 30px 0;'>
                        <a href='{$retryUrl}' class='button'>🔄 Try Again</a>
                        <a href='{$supportUrl}' class='button' style='background: #3b82f6;'>🆘 Get Help</a>
                    </div>

                    <p>If you continue to experience issues, please don't hesitate to contact our support team. We're here to help you get your subscription activated!</p>
                </div>

                <div class='footer'>
                    <p>Need immediate assistance?</p>
                    <p>📧 <a href='mailto:" . env('EMAIL_ADMIN_ADDRESS', '<EMAIL>') . "'>Contact Support</a> | 📞 " . env('COMPANY_PHONE', '+91-XXXXXXXXXX') . "</p>
                </div>
            </div>
        </body>
        </html>";
    }

    /**
     * Get subscription expiry email template
     */
    private function getSubscriptionExpiryEmailTemplate($schoolName, $subscriptionData, $daysLeft)
    {
        $renewUrl = base_url('subscription/upgrade');
        $currentUrl = base_url('subscription/current');

        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
                .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
                .warning-icon { font-size: 48px; margin-bottom: 20px; }
                .subscription-details { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f59e0b; }
                .button { display: inline-block; background: #f59e0b; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 5px; }
                .urgency { background: #fef2f2; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ef4444; }
                .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <div class='warning-icon'>⏰</div>
                    <h1>Subscription Expiring Soon</h1>
                    <p>Only {$daysLeft} days left!</p>
                </div>

                <div class='content'>
                    <h2>Hello {$schoolName},</h2>

                    <p>This is a friendly reminder that your School Question Bank subscription will expire in <strong>{$daysLeft} days</strong>.</p>

                    <div class='subscription-details'>
                        <h3>📋 Current Subscription</h3>
                        <p><strong>Plan:</strong> {$subscriptionData['plan_display_name']}</p>
                        <p><strong>Billing Cycle:</strong> " . ucfirst($subscriptionData['billing_cycle']) . "</p>
                        <p><strong>Expires On:</strong> " . date('M d, Y', strtotime($subscriptionData['expires_at'])) . "</p>
                        <p><strong>Status:</strong> " . ucfirst($subscriptionData['status']) . "</p>
                    </div>";

        if ($daysLeft <= 3) {
            $return .= "
                    <div class='urgency'>
                        <h3>🚨 Urgent Action Required</h3>
                        <p>Your subscription expires in just {$daysLeft} days! To avoid any interruption in service, please renew your subscription immediately.</p>
                    </div>";
        }

        $return .= "
                    <div style='text-align: center; margin: 30px 0;'>
                        <a href='{$renewUrl}' class='button'>🔄 Renew Subscription</a>
                        <a href='{$currentUrl}' class='button' style='background: #3b82f6;'>📊 View Details</a>
                    </div>

                    <h3>🎯 Why Renew?</h3>
                    <ul>
                        <li>Continue creating unlimited questions</li>
                        <li>Keep your staff access active</li>
                        <li>Maintain your question bank data</li>
                        <li>Access premium features and support</li>
                    </ul>

                    <p>Don't let your subscription lapse! Renew today to ensure uninterrupted access to all features.</p>
                </div>

                <div class='footer'>
                    <p>Questions about renewal?</p>
                    <p>📧 <a href='mailto:" . env('EMAIL_ADMIN_ADDRESS', '<EMAIL>') . "'>Contact Support</a> | 📞 " . env('COMPANY_PHONE', '+91-XXXXXXXXXX') . "</p>
                </div>
            </div>
        </body>
        </html>";
    }
}
