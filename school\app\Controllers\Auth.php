<?php

namespace App\Controllers;

use App\Models\SchoolModel;
use App\Services\AuditLogger;

class Auth extends BaseController
{
    // SuperAdmin login view
    public function superadminLogin()
    {
        return view('auth/superadmin_login');
    }

    // SchoolAdmin login view
    public function schooladminLogin()
    {
        return view('auth/schooladmin_login');
    }

    public function login()
    {
        $email    = $this->request->getPost('email');
        $password = $this->request->getPost('password');
        $isAjax   = $this->request->isAJAX();

        // Determine role based on email
        $role = ($email === '<EMAIL>') ? 'superadmin' : 'schooladmin';

        if ($role === 'superadmin') {
            if ($email === '<EMAIL>' && $password === '1234') {
                session()->set([
                    'role'         => 'superadmin',
                    'isSuperAdmin' => true,
                    'email'        => $email,
                    'logged_in'    => true,
                    'user_id'      => 1 // Hardcoded for superadmin
                ]);
                session()->setFlashdata('success', 'SuperAdmin login successful.');

                // Log successful superadmin login
                try {
                    $auditLogger = new AuditLogger();
                    $auditLogger->logLogin(1, null, 'superadmin');
                    $auditLogger->logSuperAdminAction(
                        'login',
                        1,
                        'Super admin logged in successfully',
                        null,
                        null,
                        null,
                        null,
                        'medium'
                    );
                } catch (\Exception $e) {
                    // Log the error but don't prevent login
                    log_message('error', 'Failed to log superadmin login: ' . $e->getMessage());
                }

                if ($isAjax) {
                    return $this->response->setJSON([
                        'success' => true,
                        'message' => 'Login successful',
                        'redirect' => site_url('superadmin/dashboard')
                    ]);
                }
                return redirect()->to('/superadmin/dashboard');
            } else {
                // Log failed superadmin login attempt
                try {
                    $auditLogger = new AuditLogger();
                    $auditLogger->logSecurityEvent(
                        'failed_login',
                        "Failed superadmin login attempt with email: {$email}",
                        null,
                        null,
                        'high'
                    );
                } catch (\Exception $e) {
                    // Log the error but don't prevent login flow
                    log_message('error', 'Failed to log failed login attempt: ' . $e->getMessage());
                }

                if ($isAjax) {
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => 'Invalid SuperAdmin credentials.'
                    ]);
                }
                return redirect()->back()->withInput()->with('error', 'Invalid SuperAdmin credentials.');
            }
        }

        // SchoolAdmin login logic
        if ($role === 'schooladmin') {
            $schoolModel = new SchoolModel();
            $school = $schoolModel->where('email', $email)->first();

            if (!$school) {
                if ($isAjax) {
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => 'No school account found for this email.'
                    ]);
                }
                return redirect()->back()->withInput()->with('error', 'No school account found for this email.');
            }

            if ($school['status'] !== 'active') {
                $reason = $school['rejection_reason'] ? ' Reason: ' . $school['rejection_reason'] : '';
                $message = 'Account inactive.' . $reason;
                if ($isAjax) {
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => $message
                    ]);
                }
                return redirect()->back()->withInput()->with('error', $message);
            }

            if (!password_verify($password, trim($school['password']))) {
                if ($isAjax) {
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => 'Incorrect password.'
                    ]);
                }
                return redirect()->back()->withInput()->with('error', 'Incorrect password.');
            }

            session()->set([
                'school_id'    => $school['id'],
                'school_name'  => $school['name'],
                'email'        => $school['email'],
                'role'         => 'schooladmin',
                'logged_in'    => true,
                'user_id'      => $school['id'] // Use school ID as user ID for school admin
            ]);

            // Log successful school admin login
            try {
                $auditLogger = new AuditLogger();
                $auditLogger->logLogin($school['id'], $school['id'], 'school admin');
            } catch (\Exception $e) {
                log_message('error', 'Failed to log school admin login: ' . $e->getMessage());
            }

            session()->setFlashdata('success', 'Login successful.');

            if ($isAjax) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Login successful',
                    'redirect' => site_url('schooladmin/dashboard')
                ]);
            }
            return redirect()->to('/schooladmin/dashboard');
        }

        // Fallback for unexpected cases
        if ($isAjax) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Something went wrong.'
            ]);
        }
        return redirect()->back()->withInput()->with('error', 'Something went wrong.');
    }

   public function logout()
{
    $role = session()->get('role');
    $email = session()->get('email');
    $userId = session()->get('user_id');
    $schoolId = session()->get('school_id');

    // Log logout activity
    if ($email) {
        log_message('info', "User logout: {$email} (Role: {$role})");

        // Log audit trail for logout
        try {
            $auditLogger = new AuditLogger();
            $auditLogger->logLogout($userId, $schoolId, null, $role);
        } catch (\Exception $e) {
            log_message('error', 'Failed to log logout audit: ' . $e->getMessage());
        }
    }

    // Destroy session
    session()->destroy();

    // Redirect to homepage with logout success message
    return redirect()->to('/')->with('logout_success', true);
}

}
