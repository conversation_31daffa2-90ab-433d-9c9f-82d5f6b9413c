<?php

namespace App\Controllers;

class Landing extends BaseController
{
    public function index()
    {
        return view('landing');
    }
    public function register()
    {
        return view('Auth/register');
    }
    public function login()
    {
        return view('Auth/login');
    }
    public function logout()
{
    // Log logout activity
    $email = session()->get('email');
    if ($email) {
        log_message('info', "User logout: {$email}");
    }

    // Destroy session completely
    session()->destroy();

    // Redirect to homepage with logout success message
    return redirect()->to('/')->with('logout_success', true);
}

}
