<?php

namespace App\Controllers;

class Landing extends BaseController
{
    public function index()
    {
        return view('landing');
    }
    public function register()
    {
        return view('Auth/register');
    }
    public function login()
    {
        return view('Auth/login');
    }
    public function logout()
{
    $email = session()->get('email');
    $userId = session()->get('user_id');
    $schoolId = session()->get('school_id');

    // Log logout activity
    if ($email) {
        log_message('info', "User logout: {$email}");

        // Log audit trail for logout
        try {
            $role = session()->get('role') ?? 'user';
            $auditLogger = new \App\Services\AuditLogger();
            $auditLogger->logLogout($userId, $schoolId, null, $role);
        } catch (\Exception $e) {
            log_message('error', 'Failed to log logout audit: ' . $e->getMessage());
        }
    }

    // Destroy session completely
    session()->destroy();

    // Redirect to homepage with logout success message
    return redirect()->to('/?logout=success');
}

}
