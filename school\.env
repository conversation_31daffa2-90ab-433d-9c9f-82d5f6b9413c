#--------------------------------------------------------------------
# Example Environment Configuration file
#
# This file can be used as a starting point for your own
# custom .env files, and contains most of the possible settings
# available in a default install.
#
# By default, all of the settings are commented out. If you want
# to override the setting, you must un-comment it by removing the '#'
# at the beginning of the line.
#--------------------------------------------------------------------

#--------------------------------------------------------------------
# ENVIRONMENT
#--------------------------------------------------------------------

CI_ENVIRONMENT = development

#--------------------------------------------------------------------
# APP
#--------------------------------------------------------------------

app.baseURL = 'http://localhost:8080/'
# If you have trouble with `.`, you could also use `_`.
# app_baseURL = ''
# app.forceGlobalSecureRequests = false
# app.CSPEnabled = false

#--------------------------------------------------------------------
# DATABASE
#--------------------------------------------------------------------

database.default.hostname = localhost
database.default.database = school
database.default.username = root
database.default.password =
database.default.DBDriver = MySQLi
database.default.DBPrefix =
# database.default.port = 3306

# If you use MySQLi as tests, first update the values of Config\Database::$tests.
# database.tests.hostname = localhost
# database.tests.database = ci4_test
# database.tests.username = root
# database.tests.password = root
# database.tests.DBDriver = MySQLi
# database.tests.DBPrefix =
# database.tests.charset = utf8mb4
# database.tests.DBCollat = utf8mb4_general_ci
# database.tests.port = 3306

#--------------------------------------------------------------------
# ENCRYPTION
#--------------------------------------------------------------------

# encryption.key =

#--------------------------------------------------------------------
# SESSION
#--------------------------------------------------------------------

# session.driver = 'CodeIgniter\Session\Handlers\FileHandler'
# session.savePath = null

#--------------------------------------------------------------------
# LOGGER
#--------------------------------------------------------------------

# logger.threshold = 4

#--------------------------------------------------------------------
# PAYMENT GATEWAY CONFIGURATION
#--------------------------------------------------------------------

# Razorpay Configuration
# For testing: Use test credentials (rzp_test_...)
# For production: Use live credentials (rzp_live_...)
RAZORPAY_KEY_ID = rzp_test_T96GgyysiEduFd
RAZORPAY_KEY_SECRET = dLDscJXPaqXPtfqLNjsxdeIX

# Production Keys (replace with actual live keys when going to production)
RAZORPAY_LIVE_KEY_ID = rzp_live_your_key_id
RAZORPAY_LIVE_KEY_SECRET = your_live_secret_key

# Payment Environment (development/production)
PAYMENT_MODE = development

# Webhook Configuration
RAZORPAY_WEBHOOK_SECRET = your_webhook_secret_here

# Currency Settings
DEFAULT_CURRENCY = INR
CURRENCY_SYMBOL = ₹

#--------------------------------------------------------------------
# SUBSCRIPTION SETTINGS
#--------------------------------------------------------------------

# Trial Period (in days)
FREE_TRIAL_DAYS = 14

# Subscription Limits
FREE_PLAN_QUESTION_LIMIT = 100
PROFESSIONAL_PLAN_QUESTION_LIMIT = 10000

# Auto-renewal Settings
AUTO_RENEWAL_ENABLED = true
RENEWAL_REMINDER_DAYS = 7

#--------------------------------------------------------------------
# EMAIL CONFIGURATION
#--------------------------------------------------------------------

# SMTP Settings for payment notifications
EMAIL_PROTOCOL = smtp
EMAIL_SMTP_HOST = smtp.gmail.com
EMAIL_SMTP_PORT = 587
EMAIL_SMTP_USER = <EMAIL>
EMAIL_SMTP_PASS = your-app-password
EMAIL_SMTP_CRYPTO = tls

# Email Settings
EMAIL_FROM_ADDRESS = <EMAIL>
EMAIL_FROM_NAME = "School Question Bank"
EMAIL_ADMIN_ADDRESS = <EMAIL>

#--------------------------------------------------------------------
# INVOICE SETTINGS
#--------------------------------------------------------------------

# Invoice Configuration
INVOICE_PREFIX = SQB
INVOICE_NUMBER_LENGTH = 6
COMPANY_NAME = "School Question Bank"
COMPANY_ADDRESS = "Your Company Address"
COMPANY_PHONE = +91-XXXXXXXXXX
COMPANY_EMAIL = "<EMAIL>"

# Tax Settings
GST_ENABLED = true
GST_RATE = 18
GST_NUMBER = YOUR_GST_NUMBER_HERE