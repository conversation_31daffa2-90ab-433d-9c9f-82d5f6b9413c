<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use App\Services\AuditLogger;
use App\Models\AuditLogModel;

class TestAuditLogs extends BaseCommand
{
    protected $group = 'Testing';
    protected $name = 'test:audit';
    protected $description = 'Test the audit logging system';

    public function run(array $params)
    {
        CLI::write('🔍 Testing Audit Logs System', 'green');
        CLI::write(str_repeat('=', 60), 'yellow');

        try {
            // Initialize services
            $auditLogger = new AuditLogger();
            $auditLogModel = new AuditLogModel();
            
            CLI::write('✅ Audit Logger and Model initialized successfully', 'green');
            CLI::newLine();
            
            // Test 1: Log a sample action
            CLI::write('📝 Test 1: Logging a sample action...', 'blue');
            $result = $auditLogger->logCreate(
                'test_entity',
                123,
                'Test Entity Name',
                ['name' => 'Test', 'status' => 'active'],
                1,
                1,
                'Test audit log entry from CLI'
            );
            
            if ($result) {
                CLI::write('✅ Sample audit log created successfully', 'green');
            } else {
                CLI::write('❌ Failed to create sample audit log', 'red');
            }
            
            // Test 2: Retrieve audit logs
            CLI::newLine();
            CLI::write('📊 Test 2: Retrieving audit logs...', 'blue');
            $logs = $auditLogModel->getAuditLogs([], 1, 5);
            
            CLI::write("✅ Retrieved {$logs['total']} total audit logs", 'green');
            CLI::write('📄 Showing latest 5 logs:', 'yellow');
            
            foreach ($logs['logs'] as $log) {
                CLI::write("  - [{$log['created_at']}] {$log['action']} on {$log['entity_type']} by " . 
                         ($log['user_name'] ?? 'System'), 'white');
            }
            
            // Test 3: Get audit statistics
            CLI::newLine();
            CLI::write('📈 Test 3: Getting audit statistics...', 'blue');
            $stats = $auditLogModel->getAuditStats();
            
            CLI::write("✅ Total actions: {$stats['total_actions']}", 'green');
            CLI::write('📊 Actions by type:', 'yellow');
            foreach ($stats['actions_by_type'] as $action) {
                CLI::write("  - {$action['action']}: {$action['count']}", 'white');
            }
            
            // Test 4: Test filter options
            CLI::newLine();
            CLI::write('🔍 Test 4: Getting filter options...', 'blue');
            $actions = $auditLogModel->getDistinctActions();
            $entityTypes = $auditLogModel->getDistinctEntityTypes();
            
            CLI::write('✅ Available actions: ' . implode(', ', $actions), 'green');
            CLI::write('✅ Available entity types: ' . implode(', ', $entityTypes), 'green');
            
            CLI::newLine();
            CLI::write('🎉 All audit log tests completed successfully!', 'green');
            CLI::write('🌐 You can now access the SuperAdmin dashboard to view audit logs.', 'cyan');
            CLI::write('🔗 URL: ' . site_url('superadmin/dashboard'), 'cyan');
            
        } catch (\Exception $e) {
            CLI::write('❌ Error: ' . $e->getMessage(), 'red');
            CLI::write('📍 File: ' . $e->getFile() . ' Line: ' . $e->getLine(), 'red');
        }

        CLI::newLine();
        CLI::write(str_repeat('=', 60), 'yellow');
        CLI::write('✨ Audit Logs System Test Complete ✨', 'green');
        CLI::write(str_repeat('=', 60), 'yellow');
    }
}
