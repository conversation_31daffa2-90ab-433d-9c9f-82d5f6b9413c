{"url": "http://localhost:8080/index.php/superadmin/dashboard", "method": "GET", "isAJAX": false, "startTime": **********.9328, "totalTime": 43.3, "totalMemory": "7.688", "segmentDuration": 10, "segmentCount": 5, "CI_VERSION": "4.6.1", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.936511, "duration": 0.012835025787353516}, {"name": "Required Before Filters", "component": "Timer", "start": **********.949348, "duration": 0.0019600391387939453}, {"name": "Routing", "component": "Timer", "start": **********.951313, "duration": 0.0009009838104248047}, {"name": "Before Filters", "component": "Timer", "start": **********.952341, "duration": 1.1920928955078125e-05}, {"name": "Controller", "component": "Timer", "start": **********.952354, "duration": 0.023411989212036133}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.952355, "duration": 0.013149023056030273}, {"name": "After Filters", "component": "Timer", "start": **********.975777, "duration": 4.0531158447265625e-06}, {"name": "Required After Filters", "component": "Timer", "start": **********.975808, "duration": 0.00035119056701660156}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(5 total Queries, 5 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "0.22 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `users`", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\superadmin.php:41", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\SuperAdmin->dashboard()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php"], "function": "        require_once()", "index": " 10    "}], "trace-file": "APPPATH\\Controllers\\superadmin.php:41", "qid": "0eccedbff640549a109edcadf532dea5"}, {"hover": "", "class": "", "duration": "0.5 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `users`\n<strong>JOIN</strong> `schools` <strong>ON</strong> `schools`.`id` = `users`.`school_id`\n<strong>WHERE</strong> `users`.`is_deleted` = 0", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\superadmin.php:44", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\SuperAdmin->dashboard()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php"], "function": "        require_once()", "index": " 10    "}], "trace-file": "APPPATH\\Controllers\\superadmin.php:44", "qid": "1fbfcb580c55dae60204e68076d59f18"}, {"hover": "", "class": "", "duration": "0.29 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `users`\n<strong>JO<PERSON></strong> `user_roles` <strong>ON</strong> `user_roles`.`user_id` = `users`.`id`\n<strong>JOIN</strong> `roles` <strong>ON</strong> `roles`.`id` = `user_roles`.`role_id`\n<strong>WHERE</strong> `roles`.`name` != &#039;Admin&#039;\n<strong>AND</strong> `users`.`is_deleted` = 0", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\superadmin.php:49", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\SuperAdmin->dashboard()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php"], "function": "        require_once()", "index": " 10    "}], "trace-file": "APPPATH\\Controllers\\superadmin.php:49", "qid": "9a604c7a4522ee7d15b20c28a8e2109b"}, {"hover": "", "class": "", "duration": "0.27 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `schools`\n<strong>WHERE</strong> `schools`.`deleted_at` <strong>IS</strong> <strong>NULL</strong>", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\superadmin.php:52", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\SuperAdmin->dashboard()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php"], "function": "        require_once()", "index": " 11    "}], "trace-file": "APPPATH\\Controllers\\superadmin.php:52", "qid": "6bcf85a40e3b41e46689a7f94fd78c88"}, {"hover": "", "class": "", "duration": "0.19 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `schools`\n<strong>WHERE</strong> `status` = &#039;inactive&#039;\n<strong>AND</strong> `schools`.`deleted_at` <strong>IS</strong> <strong>NULL</strong>", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\superadmin.php:53", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\SuperAdmin->dashboard()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\schoolquestionbank\\school\\public\\index.php"], "function": "        require_once()", "index": " 11    "}], "trace-file": "APPPATH\\Controllers\\superadmin.php:53", "qid": "a06ad4088255d3ce8ea1f777922d1e53"}]}, "badgeValue": 5, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.968456, "duration": "0.001352"}, {"name": "Query", "component": "Database", "start": **********.970321, "duration": "0.000223", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `users`"}, {"name": "Query", "component": "Database", "start": **********.971434, "duration": "0.000499", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `users`\n<strong>JOIN</strong> `schools` <strong>ON</strong> `schools`.`id` = `users`.`school_id`\n<strong>WHERE</strong> `users`.`is_deleted` = 0"}, {"name": "Query", "component": "Database", "start": **********.972061, "duration": "0.000286", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `users`\n<strong>JO<PERSON></strong> `user_roles` <strong>ON</strong> `user_roles`.`user_id` = `users`.`id`\n<strong>JOIN</strong> `roles` <strong>ON</strong> `roles`.`id` = `user_roles`.`role_id`\n<strong>WHERE</strong> `roles`.`name` != &#039;Admin&#039;\n<strong>AND</strong> `users`.`is_deleted` = 0"}, {"name": "Query", "component": "Database", "start": **********.972405, "duration": "0.000267", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `schools`\n<strong>WHERE</strong> `schools`.`deleted_at` <strong>IS</strong> <strong>NULL</strong>"}, {"name": "Query", "component": "Database", "start": **********.972735, "duration": "0.000190", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `schools`\n<strong>WHERE</strong> `status` = &#039;inactive&#039;\n<strong>AND</strong> `schools`.`deleted_at` <strong>IS</strong> <strong>NULL</strong>"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": [{"level": "debug", "msg": "Session: Class initialized using 'CodeIgniter\\Session\\Handlers\\FileHandler' driver."}, {"level": "warning", "msg": "[DEPRECATED] Creation of dynamic property App\\Controllers\\SuperAdmin::$session is deprecated in APPPATH\\Controllers\\BaseController.php on line 37.\n 1 SYSTEMPATH\\CodeIgniter.php(904): App\\Controllers\\BaseController->initController(Object(CodeIgniter\\HTTP\\IncomingRequest), Object(CodeIgniter\\HTTP\\Response), Object(CodeIgniter\\Log\\Logger))\n 2 SYSTEMPATH\\CodeIgniter.php(498): CodeIgniter\\CodeIgniter->createController()\n 3 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 4 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n 5 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n 6 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))\n 7 SYSTEMPATH\\rewrite.php(44): require_once('C:\\\\xampp\\\\htdocs\\\\schoolquestionbank\\\\school\\\\public\\\\index.php')"}, {"level": "warning", "msg": "[DEPRECATED] Creation of dynamic property CodeIgniter\\HTTP\\IncomingRequest::$school_id is deprecated in APPPATH\\Controllers\\BaseController.php on line 43.\n 1 SYSTEMPATH\\CodeIgniter.php(904): App\\Controllers\\BaseController->initController(Object(CodeIgniter\\HTTP\\IncomingRequest), Object(CodeIgniter\\HTTP\\Response), Object(CodeIgniter\\Log\\Logger))\n 2 SYSTEMPATH\\CodeIgniter.php(498): CodeIgniter\\CodeIgniter->createController()\n 3 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 4 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n 5 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n 6 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))\n 7 SYSTEMPATH\\rewrite.php(44): require_once('C:\\\\xampp\\\\htdocs\\\\schoolquestionbank\\\\school\\\\public\\\\index.php')"}]}, "badgeValue": null, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 1, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "View: superadmin/dashboard.php", "component": "Views", "start": **********.97415, "duration": 0.0014500617980957031}]}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 174 )", "display": {"coreFiles": [{"path": "SYSTEMPATH\\API\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\Autoloader\\Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH\\BaseModel.php", "name": "BaseModel.php"}, {"path": "SYSTEMPATH\\Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH\\Cache\\CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH\\Cache\\CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Cache\\ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH\\CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH\\Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH\\Config\\AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH\\Config\\DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH\\Config\\Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH\\Config\\Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH\\Config\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH\\Cookie\\CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH\\Database\\BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "SYSTEMPATH\\Database\\BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH\\Database\\BaseResult.php", "name": "BaseResult.php"}, {"path": "SYSTEMPATH\\Database\\Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH\\Database\\ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH\\Database\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Builder.php", "name": "Builder.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Result.php", "name": "Result.php"}, {"path": "SYSTEMPATH\\Database\\Query.php", "name": "Query.php"}, {"path": "SYSTEMPATH\\Database\\QueryInterface.php", "name": "QueryInterface.php"}, {"path": "SYSTEMPATH\\Database\\ResultInterface.php", "name": "ResultInterface.php"}, {"path": "SYSTEMPATH\\Debug\\Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH\\Debug\\Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH\\Events\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Filters\\DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH\\Filters\\FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH\\Filters\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Filters\\ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH\\Filters\\PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH\\Filters\\PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH\\HTTP\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH\\HTTP\\Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH\\HTTP\\IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH\\HTTP\\URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH\\HTTP\\UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH\\Helpers\\array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\filesystem_helper.php", "name": "filesystem_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\form_helper.php", "name": "form_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH\\I18n\\Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH\\I18n\\TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\HandlerInterface.php", "name": "HandlerInterface.php"}, {"path": "SYSTEMPATH\\Log\\Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH\\Model.php", "name": "Model.php"}, {"path": "SYSTEMPATH\\Modules\\Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH\\Router\\Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH\\Router\\RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH\\Security\\Security.php", "name": "Security.php"}, {"path": "SYSTEMPATH\\Security\\SecurityInterface.php", "name": "SecurityInterface.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Session\\Session.php", "name": "Session.php"}, {"path": "SYSTEMPATH\\Session\\SessionInterface.php", "name": "SessionInterface.php"}, {"path": "SYSTEMPATH\\Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init.php", "name": "init.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH\\Traits\\ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "SYSTEMPATH\\Validation\\FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH\\View\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\View\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\View\\ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}, {"path": "SYSTEMPATH\\rewrite.php", "name": "rewrite.php"}], "userFiles": [{"path": "APPPATH\\Common.php", "name": "Common.php"}, {"path": "APPPATH\\Config\\App.php", "name": "App.php"}, {"path": "APPPATH\\Config\\Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH\\Config\\Boot\\development.php", "name": "development.php"}, {"path": "APPPATH\\Config\\Cache.php", "name": "Cache.php"}, {"path": "APPPATH\\Config\\Constants.php", "name": "Constants.php"}, {"path": "APPPATH\\Config\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH\\Config\\Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH\\Config\\Database.php", "name": "Database.php"}, {"path": "APPPATH\\Config\\Events.php", "name": "Events.php"}, {"path": "APPPATH\\Config\\Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH\\Config\\Feature.php", "name": "Feature.php"}, {"path": "APPPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "APPPATH\\Config\\Kint.php", "name": "Kint.php"}, {"path": "APPPATH\\Config\\Logger.php", "name": "Logger.php"}, {"path": "APPPATH\\Config\\Modules.php", "name": "Modules.php"}, {"path": "APPPATH\\Config\\Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH\\Config\\Paths.php", "name": "Paths.php"}, {"path": "APPPATH\\Config\\Routes.php", "name": "Routes.php"}, {"path": "APPPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "APPPATH\\Config\\Security.php", "name": "Security.php"}, {"path": "APPPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "APPPATH\\Config\\Session.php", "name": "Session.php"}, {"path": "APPPATH\\Config\\Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH\\Config\\UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH\\Config\\View.php", "name": "View.php"}, {"path": "APPPATH\\Controllers\\BaseController.php", "name": "BaseController.php"}, {"path": "APPPATH\\Controllers\\superadmin.php", "name": "superadmin.php"}, {"path": "APPPATH\\Models\\ActivityLogModel.php", "name": "ActivityLogModel.php"}, {"path": "APPPATH\\Models\\AuditLogModel.php", "name": "AuditLogModel.php"}, {"path": "APPPATH\\Models\\SchoolModel.php", "name": "SchoolModel.php"}, {"path": "APPPATH\\Models\\SuperAdminLogModel.php", "name": "SuperAdminLogModel.php"}, {"path": "APPPATH\\Models\\UserModel.php", "name": "UserModel.php"}, {"path": "APPPATH\\Models\\UserProfileModel.php", "name": "UserProfileModel.php"}, {"path": "APPPATH\\Services\\AuditLogger.php", "name": "AuditLogger.php"}, {"path": "APPPATH\\Views\\superadmin\\dashboard.php", "name": "dashboard.php"}, {"path": "FCPATH\\index.php", "name": "index.php"}, {"path": "VENDORPATH\\autoload.php", "name": "autoload.php"}, {"path": "VENDORPATH\\composer\\ClassLoader.php", "name": "ClassLoader.php"}, {"path": "VENDORPATH\\composer\\InstalledVersions.php", "name": "InstalledVersions.php"}, {"path": "VENDORPATH\\composer\\autoload_real.php", "name": "autoload_real.php"}, {"path": "VENDORPATH\\composer\\autoload_static.php", "name": "autoload_static.php"}, {"path": "VENDORPATH\\composer\\installed.php", "name": "installed.php"}, {"path": "VENDORPATH\\composer\\platform_check.php", "name": "platform_check.php"}, {"path": "VENDORPATH\\laminas\\laminas-escaper\\src\\Escaper.php", "name": "Escaper.php"}, {"path": "VENDORPATH\\laminas\\laminas-escaper\\src\\EscaperInterface.php", "name": "EscaperInterface.php"}, {"path": "VENDORPATH\\myclabs\\deep-copy\\src\\DeepCopy\\deep_copy.php", "name": "deep_copy.php"}, {"path": "VENDORPATH\\phpunit\\phpunit\\src\\Framework\\Assert\\Functions.php", "name": "Functions.php"}, {"path": "VENDORPATH\\psr\\log\\src\\LogLevel.php", "name": "LogLevel.php"}, {"path": "VENDORPATH\\psr\\log\\src\\LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "VENDORPATH\\psr\\log\\src\\LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "VENDORPATH\\razorpay\\razorpay\\Deprecated.php", "name": "Deprecated.php"}, {"path": "VENDORPATH\\rmccue\\requests\\library\\Deprecated.php", "name": "Deprecated.php"}, {"path": "VENDORPATH\\rmccue\\requests\\src\\Autoload.php", "name": "Autoload.php"}, {"path": "VENDORPATH\\symfony\\deprecation-contracts\\function.php", "name": "function.php"}]}, "badgeValue": 174, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\SuperAdmin", "method": "dashboard", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Landing::index"}, {"method": "GET", "route": "register", "handler": "\\App\\Controllers\\Landing::register"}, {"method": "GET", "route": "login", "handler": "\\App\\Controllers\\Auth::schooladminLogin"}, {"method": "GET", "route": "login/superadmin", "handler": "\\App\\Controllers\\Auth::superadminLogin"}, {"method": "GET", "route": "login/schooladmin", "handler": "\\App\\Controllers\\Auth::schooladminLogin"}, {"method": "GET", "route": "logout", "handler": "\\App\\Controllers\\Auth::logout"}, {"method": "GET", "route": "auth/logout", "handler": "\\App\\Controllers\\Auth::logout"}, {"method": "GET", "route": "superadmin/dashboard", "handler": "\\App\\Controllers\\SuperAdmin::dashboard"}, {"method": "GET", "route": "schooladmin/dashboard", "handler": "\\App\\Controllers\\SchoolAdmin::dashboard"}, {"method": "GET", "route": "schooladmin/question-papers", "handler": "\\App\\Controllers\\SchoolAdmin::questionPapers"}, {"method": "GET", "route": "subscription/plans", "handler": "\\App\\Controllers\\Subscription::plans"}, {"method": "GET", "route": "subscription/current", "handler": "\\App\\Controllers\\Subscription::current"}, {"method": "GET", "route": "subscription/upgrade", "handler": "\\App\\Controllers\\Subscription::upgrade"}, {"method": "GET", "route": "subscription/usage", "handler": "\\App\\Controllers\\Subscription::getUsage"}, {"method": "GET", "route": "payment/process", "handler": "\\App\\Controllers\\Payment::process"}, {"method": "GET", "route": "payment/success", "handler": "\\App\\Controllers\\Payment::success"}, {"method": "GET", "route": "payment/failure", "handler": "\\App\\Controllers\\Payment::failure"}, {"method": "GET", "route": "payment/history", "handler": "\\App\\Controllers\\Payment::history"}, {"method": "GET", "route": "payment/transaction-details/([0-9]+)", "handler": "\\App\\Controllers\\Payment::transactionDetails/$1"}, {"method": "GET", "route": "payment/invoice/([0-9]+)", "handler": "\\App\\Controllers\\Payment::invoice/$1"}, {"method": "GET", "route": "payment/test-webhook/([^/]+)", "handler": "\\App\\Controllers\\Payment::testWebhook/$1"}, {"method": "GET", "route": "payment/debugCompletePending", "handler": "\\App\\Controllers\\Payment::debugCompletePending"}, {"method": "GET", "route": "payment/debugSession", "handler": "\\App\\Controllers\\Payment::debugSession"}, {"method": "GET", "route": "superadmin/school/([0-9]+)/details", "handler": "\\App\\Controllers\\SuperAdmin::viewSchoolDetails/$1"}, {"method": "GET", "route": "superadmin/getUsers", "handler": "\\App\\Controllers\\SuperAdmin::getUsers"}, {"method": "GET", "route": "superadmin/getUserDetails/([0-9]+)", "handler": "\\App\\Controllers\\SuperAdmin::getUserDetails/$1"}, {"method": "GET", "route": "superadmin/getAuditLogs", "handler": "\\App\\Controllers\\SuperAdmin::getAuditLogs"}, {"method": "GET", "route": "superadmin/getAuditStats", "handler": "\\App\\Controllers\\SuperAdmin::getAuditStats"}, {"method": "GET", "route": "superadmin/getSuperAdminLogs", "handler": "\\App\\Controllers\\SuperAdmin::getSuperAdminLogs"}, {"method": "GET", "route": "superadmin/exportAuditLogs", "handler": "\\App\\Controllers\\SuperAdmin::exportAuditLogs"}, {"method": "GET", "route": "superadmin/getAuditFilterOptions", "handler": "\\App\\Controllers\\SuperAdmin::getAuditFilterOptions"}, {"method": "GET", "route": "admin/schools/pending", "handler": "\\App\\Controllers\\SchoolController::pending"}, {"method": "GET", "route": "admin/schools/active", "handler": "\\App\\Controllers\\SchoolController::active"}, {"method": "GET", "route": "admin/schools/all", "handler": "\\App\\Controllers\\SchoolController::all"}, {"method": "GET", "route": "schooladmin/getUsers", "handler": "\\App\\Controllers\\SchoolAdmin::getUsers"}, {"method": "GET", "route": "schooladmin/getStaff", "handler": "\\App\\Controllers\\SchoolAdmin::getStaff"}, {"method": "GET", "route": "schooladmin/getDeletedUsers", "handler": "\\App\\Controllers\\SchoolAdmin::getDeletedUsers"}, {"method": "GET", "route": "schooladmin/getUserDetails/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::getUserDetails/$1"}, {"method": "GET", "route": "schooladmin/getStaffMember/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::getStaffMember/$1"}, {"method": "GET", "route": "schooladmin/testSoftDelete", "handler": "\\App\\Controllers\\SchoolAdmin::testSoftDelete"}, {"method": "GET", "route": "schooladmin/settings", "handler": "\\App\\Controllers\\SchoolAdmin::settings"}, {"method": "GET", "route": "schooladmin/getSchoolSettings", "handler": "\\App\\Controllers\\SchoolAdmin::getSchoolSettings"}, {"method": "GET", "route": "schooladmin/getQuestionsForReview", "handler": "\\App\\Controllers\\SchoolAdmin::getQuestionsForReview"}, {"method": "GET", "route": "schooladmin/getQuestionDetails/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::getQuestionDetails/$1"}, {"method": "GET", "route": "schooladmin/getApprovedQuestions", "handler": "\\App\\Controllers\\SchoolAdmin::getApprovedQuestions"}, {"method": "GET", "route": "schooladmin/getQuestionPapers", "handler": "\\App\\Controllers\\SchoolAdmin::getQuestionPapers"}, {"method": "GET", "route": "schooladmin/getQuestionPaper/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::getQuestionPaper/$1"}, {"method": "GET", "route": "schooladmin/testSession", "handler": "\\App\\Controllers\\SchoolAdmin::testSession"}, {"method": "GET", "route": "schooladmin/downloadQuestionPaperPDF/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::downloadQuestionPaperPDF/$1"}, {"method": "GET", "route": "schooladmin/getQuestionPaperForEdit/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::getQuestionPaperForEdit/$1"}, {"method": "GET", "route": "staff/login", "handler": "\\App\\Controllers\\Staff::login"}, {"method": "GET", "route": "staff/dashboard", "handler": "\\App\\Controllers\\Staff::dashboard"}, {"method": "GET", "route": "staff/profile", "handler": "\\App\\Controllers\\Staff::profile"}, {"method": "GET", "route": "staff/logout", "handler": "\\App\\Controllers\\Auth::logout"}, {"method": "GET", "route": "staff/getSubjects", "handler": "\\App\\Controllers\\Staff::getSubjects"}, {"method": "GET", "route": "staff/getQuestions", "handler": "\\App\\Controllers\\Staff::getQuestions"}, {"method": "GET", "route": "staff/getQuestionDetails/([0-9]+)", "handler": "\\App\\Controllers\\Staff::getQuestionDetails/$1"}, {"method": "GET", "route": "staff/getSubjectsWithCounts", "handler": "\\App\\Controllers\\Staff::getSubjectsWithCounts"}, {"method": "GET", "route": "staff/getQuestionsBySubject/([^/]+)", "handler": "\\App\\Controllers\\Staff::getQuestionsBySubject/$1"}, {"method": "GET", "route": "staff/getDetailedReports", "handler": "\\App\\Controllers\\Staff::getDetailedReports"}, {"method": "GET", "route": "staff/getRecentActivities", "handler": "\\App\\Controllers\\Staff::getRecentActivities"}, {"method": "GET", "route": "staff/testUpdate/([0-9]+)", "handler": "\\App\\Controllers\\Staff::testUpdate/$1"}, {"method": "POST", "route": "school/register", "handler": "\\App\\Controllers\\SchoolController::register"}, {"method": "POST", "route": "register/send-otp", "handler": "\\App\\Controllers\\Register::sendOtp"}, {"method": "POST", "route": "register/verify-otp", "handler": "\\App\\Controllers\\Register::verifyOtp"}, {"method": "POST", "route": "auth/login", "handler": "\\App\\Controllers\\Auth::login"}, {"method": "POST", "route": "subscription/select-plan", "handler": "\\App\\Controllers\\Subscription::selectPlan"}, {"method": "POST", "route": "subscription/upgrade", "handler": "\\App\\Controllers\\Subscription::processUpgrade"}, {"method": "POST", "route": "subscription/cancel", "handler": "\\App\\Controllers\\Subscription::cancel"}, {"method": "POST", "route": "payment/initiate", "handler": "\\App\\Controllers\\Payment::initiate"}, {"method": "POST", "route": "payment/verify", "handler": "\\App\\Controllers\\Payment::verify"}, {"method": "POST", "route": "payment/webhook/([^/]+)", "handler": "\\App\\Controllers\\Payment::webhook/$1"}, {"method": "POST", "route": "superadmin/approve/([0-9]+)", "handler": "\\App\\Controllers\\SuperAdmin::approveSchool/$1"}, {"method": "POST", "route": "superadmin/reject/([0-9]+)", "handler": "\\App\\Controllers\\SuperAdmin::rejectSchool/$1"}, {"method": "POST", "route": "superadmin/toggleUserStatus/([0-9]+)", "handler": "\\App\\Controllers\\SuperAdmin::toggleUserStatus/$1"}, {"method": "POST", "route": "admin/schools/approve/([0-9]+)", "handler": "\\App\\Controllers\\SchoolController::approve/$1"}, {"method": "POST", "route": "admin/schools/superadmin/reject/([0-9]+)", "handler": "\\App\\Controllers\\SuperAdmin::rejectSchool/$1"}, {"method": "POST", "route": "schooladmin/addUser", "handler": "\\App\\Controllers\\SchoolAdmin::addUser"}, {"method": "POST", "route": "schooladmin/deleteUser/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::deleteUser/$1"}, {"method": "POST", "route": "schooladmin/restoreUser/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::restoreUser/$1"}, {"method": "POST", "route": "schooladmin/updateStaffMember/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::updateStaffMember/$1"}, {"method": "POST", "route": "schooladmin/toggleStaffStatus/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::toggleStaffStatus/$1"}, {"method": "POST", "route": "schooladmin/updateSchoolProfile", "handler": "\\App\\Controllers\\SchoolAdmin::updateSchoolProfile"}, {"method": "POST", "route": "schooladmin/saveAllSettings", "handler": "\\App\\Controllers\\SchoolAdmin::saveAllSettings"}, {"method": "POST", "route": "schooladmin/approveQuestion/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::approveQuestion/$1"}, {"method": "POST", "route": "schooladmin/rejectQuestion/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::rejectQuestion/$1"}, {"method": "POST", "route": "schooladmin/createQuestionPaper", "handler": "\\App\\Controllers\\SchoolAdmin::createQuestionPaper"}, {"method": "POST", "route": "schooladmin/downloadQuestionPaperPDF", "handler": "\\App\\Controllers\\SchoolAdmin::downloadQuestionPaperPDF"}, {"method": "POST", "route": "staff/authenticate", "handler": "\\App\\Controllers\\Staff::authenticate"}, {"method": "POST", "route": "staff/updateProfile", "handler": "\\App\\Controllers\\Staff::updateProfile"}, {"method": "POST", "route": "staff/createQuestion", "handler": "\\App\\Controllers\\Staff::createQuestion"}, {"method": "POST", "route": "staff/saveDraft", "handler": "\\App\\Controllers\\Staff::saveDraft"}, {"method": "POST", "route": "staff/updateQuestion/([0-9]+)", "handler": "\\App\\Controllers\\Staff::updateQuestion/$1"}, {"method": "POST", "route": "school/check-email", "handler": "\\App\\Controllers\\SchoolController::checkEmail"}, {"method": "PUT", "route": "schooladmin/updateQuestionPaper/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::updateQuestionPaper/$1"}, {"method": "PUT", "route": "staff/updateQuestion/([0-9]+)", "handler": "\\App\\Controllers\\Staff::updateQuestion/$1"}, {"method": "DELETE", "route": "superadmin/deleteUser/([0-9]+)", "handler": "\\App\\Controllers\\SuperAdmin::deleteUser/$1"}, {"method": "DELETE", "route": "schooladmin/deleteStaffMember/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::deleteStaffMember/$1"}, {"method": "DELETE", "route": "schooladmin/deleteQuestionPaper/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::deleteQuestionPaper/$1"}, {"method": "DELETE", "route": "staff/deleteQuestion/([0-9]+)", "handler": "\\App\\Controllers\\Staff::deleteQuestion/$1"}]}, "badgeValue": 63, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "5.64", "count": 1}, "DBQuery": {"event": "DB<PERSON>uery", "duration": "0.05", "count": 5}}}, "badgeValue": 6, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.9437, "duration": 0.005641937255859375}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.970549, "duration": 2.288818359375e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.971937, "duration": 1.5020370483398438e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.972349, "duration": 5.9604644775390625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.972673, "duration": 5.9604644775390625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.972926, "duration": 4.0531158447265625e-06}]}], "vars": {"varData": {"View Data": {"schools": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (12)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (12)</li><li>Contents (12)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>name</th><th>email</th><th>password</th><th>plan_id</th><th>address</th><th>phone</th><th>status</th><th>rejection_reason</th><th>created_at</th><th>updated_at</th><th>deleted_at</th></tr></thead><tbody><tr><th>0</th><td title=\"string (1)\">1</td><td title=\"string (31)\">St.Josephs matriculation school</td><td title=\"string (26)\"><EMAIL></td><td title=\"string (61)\">$2y$10$CLTW6ueBbtCYsSeTMRpbReCGhHu8xjAaewmweRa1SY88QrckC8vSy\n</td><td title=\"string (1)\">1</td><td title=\"string (23)\">sathyamanagalam,\nErode.</td><td title=\"string (10)\">8778498541</td><td title=\"string (6)\">active</td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 04:38:11</td><td title=\"string (19)\">2025-07-15 09:13:34</td><td title=\"null\"><var>null</var></td></tr><tr><th>1</th><td title=\"string (1)\">2</td><td title=\"string (30)\">Governmnet Model Hr.sec school</td><td title=\"string (19)\"><EMAIL></td><td title=\"string (60)\">$2y$10$CLTW6ueBbtCYsSeTMRpbReCGhHu8xjAaewmweRa1SY88QrckC8vSy</td><td title=\"string (1)\">2</td><td title=\"string (12)\">sathy,erode.</td><td title=\"string (10)\">8778498541</td><td title=\"string (6)\">active</td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-08 05:54:25</td><td title=\"string (19)\">2025-07-08 06:11:50</td><td title=\"null\"><var>null</var></td></tr><tr><th>2</th><td title=\"string (1)\">6</td><td title=\"string (10)\">SVN school</td><td title=\"string (25)\"><EMAIL></td><td title=\"string (60)\">$2y$10$D.eyg6hiXSeP0SqzCwgV9OdGJeR/QhIKbm17q8jsz5GOS3EgdX9vm</td><td title=\"string (1)\">1</td><td title=\"string (13)\">sithode,erode</td><td title=\"string (10)\">9988774455</td><td title=\"string (8)\">rejected</td><td title=\"string (30)\">emaill is not valid for school</td><td title=\"string (19)\">2025-07-08 07:15:05</td><td title=\"string (19)\">2025-07-09 06:18:47</td><td title=\"null\"><var>null</var></td></tr><tr><th>3</th><td title=\"string (1)\">9</td><td title=\"string (10)\">XYZ SCHOOL</td><td title=\"string (25)\"><EMAIL></td><td title=\"string (60)\">$2y$10$7rA.O/zgJLmn1SzrymQYxe44e5aGS1jL/e8NjFrFj4B7j3CnVSHx6</td><td title=\"string (1)\">1</td><td title=\"string (25)\">Gobichettipalayam, Erode.</td><td title=\"string (10)\">6385418511</td><td title=\"string (6)\">active</td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-11 08:51:51</td><td title=\"string (19)\">2025-07-11 08:52:14</td><td title=\"null\"><var>null</var></td></tr><tr><th>4</th><td title=\"string (2)\">10</td><td title=\"string (10)\">ABC SCHOOL</td><td title=\"string (20)\"><EMAIL></td><td title=\"string (60)\">$2y$10$IoSytpbGhJN9GPTDRnY0rOF6Md4H8ZeosUSH1I7x/KqfSOkq0akVS</td><td title=\"string (1)\">1</td><td title=\"string (16)\">sfasdf d asdf as</td><td title=\"string (10)\">6385418511</td><td title=\"string (6)\">active</td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-11 10:06:36</td><td title=\"string (19)\">2025-07-11 10:34:27</td><td title=\"null\"><var>null</var></td></tr><tr><th>5</th><td title=\"string (2)\">11</td><td title=\"string (29)\">Government Boys hr sec school</td><td title=\"string (24)\"><EMAIL></td><td title=\"string (60)\">$2y$10$dCq4JFCXVxomnG.yABW4I.XzePnu/U96kOutFrU3WmlT8eenKL8mO</td><td title=\"string (1)\">1</td><td title=\"string (24)\">Northpet,Sathyamangalam.</td><td title=\"string (10)\">6374695596</td><td title=\"string (6)\">active</td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-15 16:26:58</td><td title=\"string (19)\">2025-07-15 16:30:07</td><td title=\"null\"><var>null</var></td></tr><tr><th>6</th><td title=\"string (2)\">12</td><td title=\"string (11)\">Test School</td><td title=\"string (15)\"><EMAIL></td><td title=\"string (60)\">$2y$10$ieNvfrfQFFGFAMx7HjJnquQ8z3c9tD4MP8mht46FB1QbydXnbR3hO</td><td title=\"string (1)\">1</td><td title=\"string (15)\">123 Test Street</td><td title=\"string (10)\">1234567890</td><td title=\"string (6)\">active</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td></tr><tr><th>7</th><td title=\"string (2)\">13</td><td title=\"string (11)\">Test School</td><td title=\"string (22)\"><EMAIL></td><td title=\"string (60)\">$2y$10$/lVgEuT87FDaMnxBvAS0X.w0OdtANZV5g0wZBX6mmIENXiDFbp9iK</td><td title=\"string (1)\">1</td><td title=\"string (26)\">123 Test Street, Test City</td><td title=\"string (11)\">+1234567890</td><td title=\"string (6)\">active</td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-16 06:21:00</td><td title=\"string (19)\">2025-07-16 06:21:00</td><td title=\"null\"><var>null</var></td></tr><tr><th>8</th><td title=\"string (2)\">15</td><td title=\"string (14)\">captain school</td><td title=\"string (23)\"><EMAIL></td><td title=\"string (60)\">$2y$10$HILl/u2e0q0YywORd.snCOQDXQSzEzazOvbG1/br9S0t7NS2LwE0u</td><td title=\"string (1)\">2</td><td title=\"string (12)\">newyork,usa.</td><td title=\"string (10)\">9999912345</td><td title=\"string (6)\">active</td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-16 17:38:13</td><td title=\"string (19)\">2025-07-16 17:40:51</td><td title=\"null\"><var>null</var></td></tr><tr><th>9</th><td title=\"string (2)\">16</td><td title=\"string (15)\">paperfox school</td><td title=\"string (22)\"><EMAIL></td><td title=\"string (60)\">$2y$10$u981bdUfrHRcrPg.cfZadumhJ214crgMU1JD9VpSoI5xlxI/CsewW</td><td title=\"string (1)\">1</td><td title=\"string (23)\">Paperfox school , Erode</td><td title=\"string (10)\">1234567890</td><td title=\"string (6)\">active</td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-17 15:23:05</td><td title=\"string (19)\">2025-07-17 16:07:04</td><td title=\"null\"><var>null</var></td></tr><tr><th>10</th><td title=\"string (2)\">17</td><td title=\"string (11)\">OOOO SCHOOL</td><td title=\"string (23)\"><EMAIL></td><td title=\"string (60)\">$2y$10$3CJxeaRSmyH7kHC09plK8u4ojxRGLUVDc7.LqCCblc2gQFL1kdLxm</td><td title=\"string (1)\">1</td><td title=\"string (19)\">OOOO SCHOOL, ERODE.</td><td title=\"string (10)\">1234567890</td><td title=\"string (6)\">active</td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-17 16:23:09</td><td title=\"string (19)\">2025-07-17 16:32:37</td><td title=\"null\"><var>null</var></td></tr><tr><th>11</th><td title=\"string (2)\">18</td><td title=\"string (20)\">TEST APPROVAL SCHOOL</td><td title=\"string (24)\"><EMAIL></td><td title=\"string (60)\">$2y$10$b5vUAP2bQPqLr2mhLxBbQO0hcuY/fnwIrcPgdNG3MT20XEYCYaQCG</td><td title=\"string (1)\">1</td><td title=\"string (25)\">Test Address for Approval</td><td title=\"string (10)\">1234567890</td><td title=\"string (8)\">inactive</td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-07-17 18:42:30</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (31) \"St.Josephs matriculation school\"<div class=\"access-path\">$value[0]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (26) \"<EMAIL>\"<div class=\"access-path\">$value[0]['email']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>password</dfn> =&gt; <var>string</var> (61) \"$2y$10$CLTW6ueBbtCYsSeTMRpbReCGhHu8xjAaewmweRa1SY88QrckC8vSy \"<div class=\"access-path\">$value[0]['password']</div></dt><dd><pre>$2y$10$CLTW6ueBbtCYsSeTMRpbReCGhHu8xjAaewmweRa1SY88QrckC8vSy\n\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['plan_id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>address</dfn> =&gt; <var>string</var> (23) \"sathyamanagalam, Erode.\"<div class=\"access-path\">$value[0]['address']</div></dt><dd><pre>sathyamanagalam,\nErode.\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"8778498541\"<div class=\"access-path\">$value[0]['phone']</div></dt><dd><pre>2248-03-06T23:49:01+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>rejection_reason</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['rejection_reason']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 04:38:11\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-15 09:13:34\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>deleted_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['deleted_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (30) \"Governmnet Model Hr.sec school\"<div class=\"access-path\">$value[1]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (19) \"<EMAIL>\"<div class=\"access-path\">$value[1]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>password</dfn> =&gt; <var>string</var> (60) \"$2y$10$CLTW6ueBbtCYsSeTMRpbReCGhHu8xjAaewmweRa1SY88QrckC8vSy\"<div class=\"access-path\">$value[1]['password']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>string</var> (12) \"sathy,erode.\"<div class=\"access-path\">$value[1]['address']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"8778498541\"<div class=\"access-path\">$value[1]['phone']</div></dt><dd><pre>2248-03-06T23:49:01+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[1]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>rejection_reason</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['rejection_reason']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 05:54:25\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 06:11:50\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>deleted_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['deleted_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"6\"<div class=\"access-path\">$value[2]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (10) \"SVN school\"<div class=\"access-path\">$value[2]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (25) \"<EMAIL>\"<div class=\"access-path\">$value[2]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>password</dfn> =&gt; <var>string</var> (60) \"$2y$10$D.eyg6hiXSeP0SqzCwgV9OdGJeR/QhIKbm17q8jsz5GOS3EgdX9vm\"<div class=\"access-path\">$value[2]['password']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>string</var> (13) \"sithode,erode\"<div class=\"access-path\">$value[2]['address']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"9988774455\"<div class=\"access-path\">$value[2]['phone']</div></dt><dd><pre>2286-07-13T19:34:15+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (8) \"rejected\"<div class=\"access-path\">$value[2]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>rejection_reason</dfn> =&gt; <var>string</var> (30) \"emaill is not valid for school\"<div class=\"access-path\">$value[2]['rejection_reason']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 07:15:05\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-09 06:18:47\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>deleted_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['deleted_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"9\"<div class=\"access-path\">$value[3]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (10) \"XYZ SCHOOL\"<div class=\"access-path\">$value[3]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (25) \"<EMAIL>\"<div class=\"access-path\">$value[3]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>password</dfn> =&gt; <var>string</var> (60) \"$2y$10$7rA.O/zgJLmn1SzrymQYxe44e5aGS1jL/e8NjFrFj4B7j3CnVSHx6\"<div class=\"access-path\">$value[3]['password']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[3]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>string</var> (25) \"Gobichettipalayam, Erode.\"<div class=\"access-path\">$value[3]['address']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"6385418511\"<div class=\"access-path\">$value[3]['phone']</div></dt><dd><pre>2172-05-06T07:21:51+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[3]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>rejection_reason</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['rejection_reason']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-11 08:51:51\"<div class=\"access-path\">$value[3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-11 08:52:14\"<div class=\"access-path\">$value[3]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>deleted_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['deleted_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"10\"<div class=\"access-path\">$value[4]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (10) \"ABC SCHOOL\"<div class=\"access-path\">$value[4]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (20) \"<EMAIL>\"<div class=\"access-path\">$value[4]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>password</dfn> =&gt; <var>string</var> (60) \"$2y$10$IoSytpbGhJN9GPTDRnY0rOF6Md4H8ZeosUSH1I7x/KqfSOkq0akVS\"<div class=\"access-path\">$value[4]['password']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[4]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>string</var> (16) \"sfasdf d asdf as\"<div class=\"access-path\">$value[4]['address']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"6385418511\"<div class=\"access-path\">$value[4]['phone']</div></dt><dd><pre>2172-05-06T07:21:51+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[4]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>rejection_reason</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['rejection_reason']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-11 10:06:36\"<div class=\"access-path\">$value[4]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-11 10:34:27\"<div class=\"access-path\">$value[4]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>deleted_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['deleted_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>5</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[5]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"11\"<div class=\"access-path\">$value[5]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (29) \"Government Boys hr sec school\"<div class=\"access-path\">$value[5]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (24) \"<EMAIL>\"<div class=\"access-path\">$value[5]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>password</dfn> =&gt; <var>string</var> (60) \"$2y$10$dCq4JFCXVxomnG.yABW4I.XzePnu/U96kOutFrU3WmlT8eenKL8mO\"<div class=\"access-path\">$value[5]['password']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[5]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>string</var> (24) \"Northpet,Sathyamangalam.\"<div class=\"access-path\">$value[5]['address']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"6374695596\"<div class=\"access-path\">$value[5]['phone']</div></dt><dd><pre>2172-01-03T04:46:36+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[5]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>rejection_reason</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[5]['rejection_reason']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-15 16:26:58\"<div class=\"access-path\">$value[5]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-15 16:30:07\"<div class=\"access-path\">$value[5]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>deleted_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[5]['deleted_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>6</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[6]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"12\"<div class=\"access-path\">$value[6]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (11) \"Test School\"<div class=\"access-path\">$value[6]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (15) \"<EMAIL>\"<div class=\"access-path\">$value[6]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>password</dfn> =&gt; <var>string</var> (60) \"$2y$10$ieNvfrfQFFGFAMx7HjJnquQ8z3c9tD4MP8mht46FB1QbydXnbR3hO\"<div class=\"access-path\">$value[6]['password']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[6]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>string</var> (15) \"123 Test Street\"<div class=\"access-path\">$value[6]['address']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"1234567890\"<div class=\"access-path\">$value[6]['phone']</div></dt><dd><pre>2009-02-13T23:31:30+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[6]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>rejection_reason</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[6]['rejection_reason']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[6]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[6]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>deleted_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[6]['deleted_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>7</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[7]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"13\"<div class=\"access-path\">$value[7]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (11) \"Test School\"<div class=\"access-path\">$value[7]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (22) \"<EMAIL>\"<div class=\"access-path\">$value[7]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>password</dfn> =&gt; <var>string</var> (60) \"$2y$10$/lVgEuT87FDaMnxBvAS0X.w0OdtANZV5g0wZBX6mmIENXiDFbp9iK\"<div class=\"access-path\">$value[7]['password']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[7]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>string</var> (26) \"123 Test Street, Test City\"<div class=\"access-path\">$value[7]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>phone</dfn> =&gt; <var>string</var> (11) \"+1234567890\"<div class=\"access-path\">$value[7]['phone']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[7]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>rejection_reason</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[7]['rejection_reason']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-16 06:21:00\"<div class=\"access-path\">$value[7]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-16 06:21:00\"<div class=\"access-path\">$value[7]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>deleted_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[7]['deleted_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>8</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[8]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[8]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (14) \"captain school\"<div class=\"access-path\">$value[8]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (23) \"<EMAIL>\"<div class=\"access-path\">$value[8]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>password</dfn> =&gt; <var>string</var> (60) \"$2y$10$HILl/u2e0q0YywORd.snCOQDXQSzEzazOvbG1/br9S0t7NS2LwE0u\"<div class=\"access-path\">$value[8]['password']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[8]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>string</var> (12) \"newyork,usa.\"<div class=\"access-path\">$value[8]['address']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"9999912345\"<div class=\"access-path\">$value[8]['phone']</div></dt><dd><pre>2286-11-19T17:25:45+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[8]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>rejection_reason</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[8]['rejection_reason']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-16 17:38:13\"<div class=\"access-path\">$value[8]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-16 17:40:51\"<div class=\"access-path\">$value[8]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>deleted_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[8]['deleted_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>9</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[9]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"16\"<div class=\"access-path\">$value[9]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (15) \"paperfox school\"<div class=\"access-path\">$value[9]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (22) \"<EMAIL>\"<div class=\"access-path\">$value[9]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>password</dfn> =&gt; <var>string</var> (60) \"$2y$10$u981bdUfrHRcrPg.cfZadumhJ214crgMU1JD9VpSoI5xlxI/CsewW\"<div class=\"access-path\">$value[9]['password']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[9]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>string</var> (23) \"Paperfox school , Erode\"<div class=\"access-path\">$value[9]['address']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"1234567890\"<div class=\"access-path\">$value[9]['phone']</div></dt><dd><pre>2009-02-13T23:31:30+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[9]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>rejection_reason</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[9]['rejection_reason']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-17 15:23:05\"<div class=\"access-path\">$value[9]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-17 16:07:04\"<div class=\"access-path\">$value[9]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>deleted_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[9]['deleted_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>10</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[10]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"17\"<div class=\"access-path\">$value[10]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (11) \"OOOO SCHOOL\"<div class=\"access-path\">$value[10]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (23) \"<EMAIL>\"<div class=\"access-path\">$value[10]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>password</dfn> =&gt; <var>string</var> (60) \"$2y$10$3CJxeaRSmyH7kHC09plK8u4ojxRGLUVDc7.LqCCblc2gQFL1kdLxm\"<div class=\"access-path\">$value[10]['password']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[10]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>string</var> (19) \"OOOO SCHOOL, ERODE.\"<div class=\"access-path\">$value[10]['address']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"1234567890\"<div class=\"access-path\">$value[10]['phone']</div></dt><dd><pre>2009-02-13T23:31:30+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[10]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>rejection_reason</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[10]['rejection_reason']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-17 16:23:09\"<div class=\"access-path\">$value[10]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-17 16:32:37\"<div class=\"access-path\">$value[10]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>deleted_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[10]['deleted_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>11</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[11]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"18\"<div class=\"access-path\">$value[11]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (20) \"TEST APPROVAL SCHOOL\"<div class=\"access-path\">$value[11]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (24) \"<EMAIL>\"<div class=\"access-path\">$value[11]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>password</dfn> =&gt; <var>string</var> (60) \"$2y$10$b5vUAP2bQPqLr2mhLxBbQO0hcuY/fnwIrcPgdNG3MT20XEYCYaQCG\"<div class=\"access-path\">$value[11]['password']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[11]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>string</var> (25) \"Test Address for Approval\"<div class=\"access-path\">$value[11]['address']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"1234567890\"<div class=\"access-path\">$value[11]['phone']</div></dt><dd><pre>2009-02-13T23:31:30+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (8) \"inactive\"<div class=\"access-path\">$value[11]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>rejection_reason</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[11]['rejection_reason']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-17 18:42:30\"<div class=\"access-path\">$value[11]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[11]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>deleted_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[11]['deleted_at']</div></dt></dl></dd></dl></li></ul></dd></dl></div>", "pendingSchools": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (1)</dt><dd><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"18\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (20) \"TEST APPROVAL SCHOOL\"<div class=\"access-path\">$value[0]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (24) \"<EMAIL>\"<div class=\"access-path\">$value[0]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>password</dfn> =&gt; <var>string</var> (60) \"$2y$10$b5vUAP2bQPqLr2mhLxBbQO0hcuY/fnwIrcPgdNG3MT20XEYCYaQCG\"<div class=\"access-path\">$value[0]['password']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>string</var> (25) \"Test Address for Approval\"<div class=\"access-path\">$value[0]['address']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"1234567890\"<div class=\"access-path\">$value[0]['phone']</div></dt><dd><pre>2009-02-13T23:31:30+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (8) \"inactive\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>rejection_reason</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['rejection_reason']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-17 18:42:30\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>deleted_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['deleted_at']</div></dt></dl></dd></dl></dd></dl></div>", "totalUsers": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>integer</var> 12</dt></dl></div>", "schoolAdmins": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>integer</var> 11</dt></dl></div>", "staffMembers": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>integer</var> 3</dt></dl></div>"}}, "session": {"_ci_previous_url": "http://localhost:8080/index.php/superadmin/dashboard", "role": "superadmin", "isSuperAdmin": "<pre>1</pre>", "email": "<EMAIL>", "logged_in": "<pre>1</pre>", "user_id": "<pre>1</pre>", "__ci_vars": "<pre>Array\n(\n)\n</pre>"}, "headers": {"Host": "localhost:8080", "Connection": "keep-alive", "Cache-Control": "max-age=0", "Sec-Ch-Ua": "&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua-Platform": "&quot;Windows&quot;", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-User": "?1", "Sec-Fetch-Dest": "document", "Referer": "http://localhost:8080/index.php?logout=success", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "en-US,en;q=0.9", "Cookie": "csrf_cookie_name=d7c532fc44f5b82847f4efbd43afe67a; ci_session=9fdc510d7b211994b4dc67e0796ae70e"}, "cookies": {"csrf_cookie_name": "d7c532fc44f5b82847f4efbd43afe67a", "ci_session": "9fdc510d7b211994b4dc67e0796ae70e"}, "request": "HTTP/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Content-Type": "text/html; charset=UTF-8"}}}, "config": {"ciVersion": "4.6.1", "phpVersion": "8.2.12", "phpSAPI": "cli-server", "environment": "development", "baseURL": "http://localhost:8080/", "timezone": "UTC", "locale": "en", "cspEnabled": false}}