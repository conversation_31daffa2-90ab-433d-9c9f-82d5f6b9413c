<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\SchoolModel;
use App\Models\UserModel;
use App\Models\UserProfileModel;
use App\Models\QuestionModel;
use App\Models\UserRoleModel;
use App\Models\SchoolSettingsModel;
use App\Models\SubscriptionModel;
use App\Models\PaymentLogModel;
use App\Services\EmailService;
use App\Services\AuditLogger;

class SchoolAdmin extends BaseController
{
    protected $schoolModel;
    protected $userModel;
    protected $userProfileModel;
    protected $userRoleModel;
    protected $emailService;
    protected $questionModel;
    protected $schoolSettingsModel;
    protected $subscriptionModel;
    protected $paymentLogModel;
    protected $schoolId;

    public function __construct()
    {
        // Initialize Models
        $this->schoolModel = new SchoolModel();
        $this->userModel = new UserModel();
        $this->userProfileModel = new UserProfileModel();
        $this->userRoleModel = new UserRoleModel();
        $this->emailService = new EmailService();
        $this->questionModel = new QuestionModel();
        $this->schoolSettingsModel = new SchoolSettingsModel();
        $this->subscriptionModel = new SubscriptionModel();
        $this->paymentLogModel = new PaymentLogModel();
    }

    public function dashboard()
    {
        // ✅ AUTH CHECK
        if (!session()->get('logged_in') || session()->get('role') !== 'schooladmin') {
            return redirect()->to('/login/superadmin')->with('error', 'Please login first.');
        }

        // Get school ID from session (assuming it's stored during login)
        $schoolId = session()->get('school_id');

        // If no school_id in session, try to get it from user data or set a default
        if (!$schoolId) {
            // For now, let's get the first active school (you can modify this logic)
            $school = $this->schoolModel->where('status', 'active')->first();
            if ($school) {
                $schoolId = $school['id'];
                // Store in session for future use
                session()->set('school_id', $schoolId);
            }
        }

        // Get school data
        $school = null;
        if ($schoolId) {
            $school = $this->schoolModel->find($schoolId);
        }

        // Get subscription data
        $currentSubscription = null;
        $recentTransactions = [];
        if ($schoolId) {
            $currentSubscription = $this->subscriptionModel->getCurrentSubscription($schoolId);
            $recentTransactions = $this->paymentLogModel->getRecentTransactions($schoolId, 5);
        }

        // Prepare dashboard data
        $dashboardData = [
            'school' => $school,
            'stats' => $this->getDashboardStats($school),
            'recent_activity' => $this->getRecentActivity($school, 20), // Get more activities for the modal
            'subject_performance' => $this->getSubjectPerformance($school, 6),
            'school_name' => $school ? $school['name'] : 'School Dashboard',
            'school_email' => $school ? $school['email'] : '',
            'school_phone' => $school ? $school['phone'] : '',
            'school_address' => $school ? $school['address'] : '',
            'school_status' => $school ? $school['status'] : 'active',
            'current_subscription' => $currentSubscription,
            'recent_transactions' => $recentTransactions
        ];

        return view('schooladmin/dashboard', $dashboardData);
    }

    /**
     * Question Papers Management
     */
    public function questionPapers()
    {
        // ✅ AUTH CHECK
        if (!session()->get('logged_in') || session()->get('role') !== 'schooladmin') {
            return redirect()->to('/login/superadmin')->with('error', 'Please login first.');
        }

        // Get school ID from session
        $schoolId = session()->get('school_id');

        // If no school_id in session, try to get it from user data
        if (!$schoolId) {
            $school = $this->schoolModel->where('status', 'active')->first();
            if ($school) {
                $schoolId = $school['id'];
                session()->set('school_id', $schoolId);
            }
        }

        // Get school data
        $school = null;
        if ($schoolId) {
            $school = $this->schoolModel->find($schoolId);
        }

        // Prepare question papers data
        $questionPapersData = [
            'school' => $school,
            'school_name' => $school ? $school['name'] : 'School Dashboard',
            'school_email' => $school ? $school['email'] : '',
            'school_phone' => $school ? $school['phone'] : '',
            'school_address' => $school ? $school['address'] : '',
            'school_status' => $school ? $school['status'] : 'active'
        ];

        return view('schooladmin/question_papers', $questionPapersData);
    }

    /**
     * Test session status
     */
    public function testSession()
    {
        return $this->response->setJSON([
            'logged_in' => session()->get('logged_in'),
            'role' => session()->get('role'),
            'school_id' => session()->get('school_id'),
            'user_id' => session()->get('user_id'),
            'session_data' => session()->get()
        ]);
    }

    /**
     * Create a new question paper (AJAX)
     */
    public function createQuestionPaper()
    {
        // Debug session first
        log_message('debug', 'Session data in createQuestionPaper: ' . json_encode([
            'logged_in' => session()->get('logged_in'),
            'role' => session()->get('role'),
            'school_id' => session()->get('school_id')
        ]));

        // Check authentication
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $schoolId = session()->get('school_id');
        $userId = session()->get('user_id');

        // Handle school admin case - they don't have user_id in session
        if (!$userId && session()->get('role') === 'schooladmin') {
            $email = session()->get('email');
            log_message('debug', 'School admin creating question paper - email: ' . $email);

            // Try to find existing user record for this school admin
            $db = \Config\Database::connect();
            $existingUser = $db->table('users')
                              ->where('email', $email)
                              ->where('school_id', $schoolId)
                              ->get()
                              ->getRowArray();

            if ($existingUser) {
                $userId = $existingUser['id'];
                log_message('debug', 'Found existing user record with ID: ' . $userId);
            } else {
                log_message('debug', 'Creating new user record for school admin');
                // Create a user record for the school admin
                $userData = [
                    'school_id' => $schoolId,
                    'name' => session()->get('school_name') . ' Admin',
                    'email' => $email,
                    'password' => '', // Empty password since they login via school admin
                    'status' => 'active',
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                $userId = $db->table('users')->insert($userData);
                log_message('debug', 'Created user record with ID: ' . $userId);

                if (!$userId) {
                    log_message('error', 'Failed to create user record for school admin');
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => 'Failed to create user record for school admin'
                    ]);
                }
            }
        }

        log_message('debug', 'Final userId for question paper creation: ' . $userId);

        // Get POST data
        $title = $this->request->getPost('title');
        $standard = $this->request->getPost('standard');
        $subject = $this->request->getPost('subject');
        $duration = $this->request->getPost('duration');
        $totalMarks = $this->request->getPost('totalMarks') ?: $this->request->getPost('total_marks');
        $selectedQuestions = $this->request->getPost('selectedQuestions') ?: $this->request->getPost('selected_questions'); // JSON string
        $status = $this->request->getPost('status') ?: 'draft';
        $academicYear = $this->request->getPost('academicYear');
        $examType = $this->request->getPost('examType');
        $examDate = $this->request->getPost('examDate');
        $instructions = $this->request->getPost('instructions');

        // Debug request parameters
        log_message('debug', 'Create question paper parameters: ' . json_encode([
            'title' => $title,
            'standard' => $standard,
            'subject' => $subject,
            'duration' => $duration,
            'total_marks' => $totalMarks,
            'status' => $status,
            'selected_questions_count' => is_string($selectedQuestions) ? count(json_decode($selectedQuestions, true)) : 0
        ]));

        // Validate required fields
        if (!$title || !$standard || !$subject || !$selectedQuestions) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Title, standard, subject, and selected questions are required'
            ]);
        }

        // Parse selected questions
        $questionsData = json_decode($selectedQuestions, true);
        if (!$questionsData || !is_array($questionsData)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid questions data format'
            ]);
        }

        // Final validation before database insert
        if (!$userId) {
            log_message('error', 'userId is still null after user handling logic - using default');
            // For now, use a default user ID to test functionality
            $userId = 1; // Temporary fix
        }

        try {
            $db = \Config\Database::connect();
            $db->transStart();

            // Insert question paper
            $paperData = [
                'school_id' => $schoolId,
                'title' => $title,
                'standard' => $standard,
                'subject' => $subject,
                'duration' => $duration ?: 3.0,
                'total_marks' => $totalMarks ?: 100,
                'status' => $status,
                'academic_year' => $academicYear,
                'exam_type' => $examType,
                'exam_date' => $examDate,
                'instructions' => $instructions,
                'created_by' => $userId,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            log_message('debug', 'About to insert question paper with userId: ' . $userId);

            $insertResult = $db->table('question_papers')->insert($paperData);
            if (!$insertResult) {
                throw new \Exception('Failed to create question paper');
            }

            $paperId = $db->insertID();
            log_message('debug', 'Question paper created with ID: ' . $paperId);

            // Insert paper questions
            log_message('debug', 'Inserting questions for paper ID: ' . $paperId);
            log_message('debug', 'Questions data: ' . json_encode($questionsData));

            foreach ($questionsData as $index => $questionData) {
                $paperQuestionData = [
                    'paper_id' => $paperId,
                    'question_id' => $questionData['question_id'],
                    'question_order' => $index + 1,
                    'marks' => $questionData['marks'] ?: 1,
                    'created_at' => date('Y-m-d H:i:s')
                ];

                log_message('debug', 'Inserting paper question: ' . json_encode($paperQuestionData));
                $insertResult = $db->table('paper_questions')->insert($paperQuestionData);
                log_message('debug', 'Insert result: ' . ($insertResult ? 'success' : 'failed'));
            }

            $db->transComplete();

            if ($db->transStatus() === false) {
                throw new \Exception('Transaction failed');
            }

            log_message('debug', 'Question paper created successfully with ID: ' . $paperId);

            // Log question paper creation
            $auditLogger = new AuditLogger();
            $auditLogger->logCreate(
                'question_paper',
                $paperId,
                $title,
                [
                    'title' => $title,
                    'standard' => $standard,
                    'subject' => $subject,
                    'duration' => $duration,
                    'total_marks' => $totalMarks,
                    'questions_count' => count($questionsData),
                    'status' => $status,
                    'academic_year' => $academicYear,
                    'exam_type' => $examType
                ],
                $userId,
                $schoolId,
                "Question paper created: {$title} for {$standard} {$subject}"
            );

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Question paper created successfully!',
                'paper_id' => $paperId,
                'data' => [
                    'id' => $paperId,
                    'title' => $title,
                    'subject' => $subject,
                    'standard' => $standard,
                    'total_marks' => $totalMarks,
                    'questions_count' => count($questionsData),
                    'status' => $status
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error creating question paper: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error creating question paper: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get created question papers (AJAX)
     */
    public function getQuestionPapers()
    {
        // Check authentication
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $schoolId = session()->get('school_id');
        $status = $this->request->getGet('status'); // Optional filter
        $limit = $this->request->getGet('limit') ?: 10;
        $offset = $this->request->getGet('offset') ?: 0;

        try {
            $db = \Config\Database::connect();

            // Build query for question papers
            $builder = $db->table('question_papers qp')
                         ->select('qp.*, u.name as created_by_name, COUNT(pq.id) as questions_count')
                         ->join('users u', 'u.id = qp.created_by', 'left')
                         ->join('paper_questions pq', 'pq.paper_id = qp.id', 'left')
                         ->where('qp.school_id', $schoolId)
                         ->groupBy('qp.id');

            // Apply status filter if provided
            if ($status) {
                $builder->where('qp.status', $status);
            }

            // Get total count for pagination
            $totalBuilder = clone $builder;
            $total = $totalBuilder->countAllResults(false);

            // Get papers with pagination
            $papers = $builder->orderBy('qp.created_at', 'DESC')
                            ->limit($limit, $offset)
                            ->get()
                            ->getResultArray();

            return $this->response->setJSON([
                'success' => true,
                'data' => $papers,
                'pagination' => [
                    'total' => $total,
                    'limit' => $limit,
                    'offset' => $offset,
                    'has_more' => ($offset + $limit) < $total
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error fetching question papers: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error fetching question papers: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get a specific question paper with its questions (AJAX)
     */
    public function getQuestionPaper($paperId)
    {
        // Check authentication
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $schoolId = session()->get('school_id');

        try {
            $db = \Config\Database::connect();

            // Get question paper details
            $paper = $db->table('question_papers qp')
                       ->select('qp.*, u.name as created_by_name')
                       ->join('users u', 'u.id = qp.created_by', 'left')
                       ->where('qp.id', $paperId)
                       ->where('qp.school_id', $schoolId)
                       ->get()
                       ->getRowArray();

            if (!$paper) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Question paper not found'
                ]);
            }

            // Get questions for this paper
            log_message('debug', 'Fetching questions for paper ID: ' . $paperId);

            // First check if paper_questions exist
            $paperQuestionsCount = $db->table('paper_questions')
                                     ->where('paper_id', $paperId)
                                     ->countAllResults();
            log_message('debug', 'Paper questions count: ' . $paperQuestionsCount);

            $questions = $db->table('paper_questions pq')
                           ->select('pq.*, q.question_text, q.option_a, q.option_b, q.option_c, q.option_d, q.correct_answer, q.answer, q.question_type, q.difficulty, q.chapter, q.chapter_name, q.topic_name')
                           ->join('questions q', 'q.id = pq.question_id', 'left')
                           ->where('pq.paper_id', $paperId)
                           ->orderBy('pq.question_order', 'ASC')
                           ->get()
                           ->getResultArray();

            log_message('debug', 'Questions retrieved: ' . json_encode($questions));

            $paper['questions'] = $questions;
            $paper['questions_count'] = count($questions);

            return $this->response->setJSON([
                'success' => true,
                'data' => $paper
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error fetching question paper: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error fetching question paper: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Delete question paper
     */
    public function deleteQuestionPaper($paperId)
    {
        // Check authentication
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $schoolId = session()->get('school_id');

        if (!$paperId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Question paper ID is required'
            ]);
        }

        try {
            $db = \Config\Database::connect();

            // Verify question paper belongs to this school
            $paper = $db->table('question_papers')
                       ->where('id', $paperId)
                       ->where('school_id', $schoolId)
                       ->get()
                       ->getRowArray();

            if (!$paper) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Question paper not found or access denied'
                ]);
            }

            // Only allow deletion of draft papers
            if ($paper['status'] !== 'draft') {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Only draft question papers can be deleted'
                ]);
            }

            $db->transStart();

            // Delete paper questions first (foreign key constraint)
            $db->table('paper_questions')->where('paper_id', $paperId)->delete();

            // Delete question paper
            $db->table('question_papers')->where('id', $paperId)->delete();

            $db->transComplete();

            if ($db->transStatus() === false) {
                throw new \Exception('Transaction failed');
            }

            log_message('debug', 'Question paper deleted successfully: ' . $paperId);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Question paper deleted successfully'
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error deleting question paper: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to delete question paper'
            ]);
        }
    }

    /**
     * Get approved questions for question paper creation (AJAX)
     */
    public function getApprovedQuestions()
    {
        // Debug session and request parameters
        log_message('debug', 'Session data in getApprovedQuestions: ' . json_encode([
            'logged_in' => session()->get('logged_in'),
            'role' => session()->get('role'),
            'school_id' => session()->get('school_id')
        ]));



        // Check authentication
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $schoolId = session()->get('school_id');
        $subject = $this->request->getGet('subject');
        $standard = $this->request->getGet('standard');
        $questionType = $this->request->getGet('question_type');
        $difficulty = $this->request->getGet('difficulty');
        $marks = $this->request->getGet('marks');

        // Debug request parameters
        log_message('debug', 'Request parameters: ' . json_encode([
            'subject' => $subject,
            'standard' => $standard,
            'question_type' => $questionType,
            'difficulty' => $difficulty,
            'marks' => $marks
        ]));

        if (!$subject) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Subject is required'
            ]);
        }

        try {
            log_message('debug', 'NEW CODE EXECUTING - Using database query builder');
            $db = \Config\Database::connect();

            // Build query for approved questions using query builder
            $builder = $db->table('questions')
                         ->select('questions.*, users.name as staff_name')
                         ->join('users', 'users.id = questions.staff_id', 'left')
                         ->where('questions.school_id', $schoolId)
                         ->where('questions.status', 'approved');

            // Apply subject filter if provided
            if ($subject) {
                $builder->where('questions.subject', $subject);
            }

            // Apply filters
            if ($standard) {
                $builder->where('questions.standard', $standard);
            }

            if ($questionType) {
                $builder->where('questions.question_type', $questionType);
            }

            if ($difficulty) {
                $builder->where('questions.difficulty', $difficulty);
            }

            if ($marks) {
                $builder->where('questions.marks', $marks);
            }

            // Debug the SQL query before execution
            $sql = $builder->getCompiledSelect(false);
            log_message('debug', 'Generated SQL: ' . $sql);

            $questions = $builder->orderBy('questions.chapter', 'ASC')
                               ->orderBy('questions.created_at', 'DESC')
                               ->get()
                               ->getResultArray();

            // Debug the results
            log_message('debug', 'Query returned ' . count($questions) . ' questions');
            if (count($questions) > 0) {
                log_message('debug', 'First question: ' . json_encode($questions[0]));
            }

            // Group questions by question type and marks for easier selection
            $groupedQuestions = [];
            foreach ($questions as $question) {
                $key = $question['question_type'] . '_' . $question['marks'];
                if (!isset($groupedQuestions[$key])) {
                    $groupedQuestions[$key] = [
                        'type' => $question['question_type'],
                        'marks' => $question['marks'],
                        'questions' => []
                    ];
                }
                $groupedQuestions[$key]['questions'][] = $question;
            }

            return $this->response->setJSON([
                'success' => true,
                'questions' => $questions,
                'grouped_questions' => $groupedQuestions,
                'total_count' => count($questions),
                'filters' => [
                    'subject' => $subject,
                    'standard' => $standard,
                    'question_type' => $questionType,
                    'difficulty' => $difficulty,
                    'marks' => $marks
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error fetching approved questions: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error fetching questions: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Helper method to get and set school ID
     */
    private function getSchoolId()
    {
        if (!$this->schoolId) {
            $this->schoolId = session()->get('school_id');

            if (!$this->schoolId) {
                // Try to get first active school as fallback
                $school = $this->schoolModel->where('status', 'active')->first();
                if ($school) {
                    $this->schoolId = $school['id'];
                    session()->set('school_id', $this->schoolId);
                }
            }
        }

        return $this->schoolId;
    }

    /**
     * Check authentication and set school ID
     */
    private function checkAuth()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'schooladmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ]);
        }

        if (!$this->getSchoolId()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'School information not found'
            ]);
        }

        return null; // No error
    }

    /**
     * Get dashboard statistics using questions table
     */
    private function getDashboardStats($school)
    {
        $stats = [
            'total_questions' => 0,
            'pending_review' => 0,
            'approved_questions' => 0,
            'rejected_questions' => 0,
            'active_users' => 1,
            'success_rate' => 0,
            'total_staff' => 0,
            'total_subjects' => 0
        ];

        if ($school) {
            // Get real question statistics
            $questionStats = $this->questionModel->getAdminStats($school['id']);
            $stats = array_merge($stats, $questionStats);

            // Get active users count (staff members)
            $stats['active_users'] = $this->userModel->where('school_id', $school['id'])
                                                   ->where('status', 'active')
                                                   ->where('is_deleted', 0)
                                                   ->countAllResults();

            // Get total staff count
            $stats['total_staff'] = $stats['active_users'];

            // Get unique subjects count
            $stats['total_subjects'] = $this->questionModel->select('subject')
                                                         ->where('school_id', $school['id'])
                                                         ->groupBy('subject')
                                                         ->countAllResults();

            // Calculate success rate (approved / total submitted)
            $totalSubmitted = $stats['total_questions'];
            if ($totalSubmitted > 0) {
                $stats['success_rate'] = round(($stats['approved_questions'] / $totalSubmitted) * 100, 1);
            }

            // Add pending review alias for backward compatibility
            $stats['pending_review'] = $stats['pending_reviews'];
        }

        return $stats;
    }

    /**
     * Get recent activity data for dashboard
     */
    private function getRecentActivity($school, $limit = 10)
    {
        $activities = [];

        if (!$school) {
            return $activities;
        }

        $schoolId = $school['id'];

        // Get recent question activities (approved, rejected, submitted)
        $recentQuestions = $this->questionModel->select('questions.*, users.name as staff_name')
                                             ->join('users', 'users.id = questions.staff_id', 'left')
                                             ->where('questions.school_id', $schoolId)
                                             ->whereIn('questions.status', ['approved', 'rejected', 'pending'])
                                             ->orderBy('questions.updated_at', 'DESC')
                                             ->limit($limit)
                                             ->findAll();

        foreach ($recentQuestions as $question) {
            $activity = [
                'type' => 'question',
                'status' => $question['status'],
                'title' => '',
                'description' => '',
                'time' => $question['updated_at'],
                'icon' => '',
                'color' => ''
            ];

            switch ($question['status']) {
                case 'approved':
                    $activity['title'] = 'Question approved';
                    $activity['description'] = $question['subject'] . ' - Class ' . $question['standard'];
                    $activity['icon'] = 'fas fa-check';
                    $activity['color'] = 'green';
                    break;
                case 'rejected':
                    $activity['title'] = 'Question rejected';
                    $activity['description'] = $question['subject'] . ' - Class ' . $question['standard'];
                    $activity['icon'] = 'fas fa-times';
                    $activity['color'] = 'red';
                    break;
                case 'pending':
                    $activity['title'] = 'New question submitted';
                    $activity['description'] = $question['subject'] . ' - Class ' . $question['standard'] . ' by ' . ($question['staff_name'] ?? 'Unknown');
                    $activity['icon'] = 'fas fa-clock';
                    $activity['color'] = 'yellow';
                    break;
            }

            $activities[] = $activity;
        }

        // Get recent staff additions
        $recentStaff = $this->userModel->where('school_id', $schoolId)
                                     ->where('is_deleted', 0)
                                     ->orderBy('created_at', 'DESC')
                                     ->limit(3)
                                     ->findAll();

        foreach ($recentStaff as $staff) {
            $activities[] = [
                'type' => 'staff',
                'status' => 'added',
                'title' => 'Staff member added',
                'description' => $staff['name'] . ' joined the team',
                'time' => $staff['created_at'],
                'icon' => 'fas fa-user-plus',
                'color' => 'blue'
            ];
        }

        // Get recent payment transactions
        $paymentLogModel = new \App\Models\PaymentLogModel();
        $recentTransactions = $paymentLogModel->getRecentTransactions($schoolId, 5);

        // Debug: Log transaction data
        if (ENVIRONMENT === 'development') {
            log_message('debug', "Dashboard: School ID = $schoolId, Found " . count($recentTransactions) . " transactions");
            foreach ($recentTransactions as $i => $transaction) {
                log_message('debug', "Transaction $i: ID={$transaction['id']}, Status={$transaction['status']}, Amount={$transaction['amount']}");
            }
        }

        foreach ($recentTransactions as $transaction) {
            $statusColor = $transaction['status'] === 'completed' ? 'green' :
                          ($transaction['status'] === 'failed' ? 'red' : 'yellow');

            $statusIcon = $transaction['status'] === 'completed' ? 'fas fa-check-circle' :
                         ($transaction['status'] === 'failed' ? 'fas fa-times-circle' : 'fas fa-clock');

            $activities[] = [
                'type' => 'payment',
                'status' => $transaction['status'],
                'title' => 'Payment ' . ucfirst($transaction['status']),
                'description' => '₹' . number_format($transaction['amount']) . ' for ' . ($transaction['plan_name'] ?? 'subscription'),
                'time' => $transaction['created_at'],
                'icon' => $statusIcon,
                'color' => $statusColor
            ];
        }

        // Sort all activities by time (most recent first)
        usort($activities, function($a, $b) {
            return strtotime($b['time']) - strtotime($a['time']);
        });

        // Return only the requested number of activities
        return array_slice($activities, 0, $limit);
    }

    /**
     * Get subject performance data for dashboard
     */
    private function getSubjectPerformance($school, $limit = 6)
    {
        $subjects = [];

        if (!$school) {
            return $subjects;
        }

        $schoolId = $school['id'];

        // Get subjects with question counts
        $subjectData = $this->questionModel->select('subject, COUNT(*) as total_questions,
                                                   SUM(CASE WHEN status = "approved" THEN 1 ELSE 0 END) as approved_questions,
                                                   SUM(CASE WHEN status = "pending" THEN 1 ELSE 0 END) as pending_questions,
                                                   SUM(CASE WHEN status = "rejected" THEN 1 ELSE 0 END) as rejected_questions')
                                          ->where('school_id', $schoolId)
                                          ->groupBy('subject')
                                          ->orderBy('total_questions', 'DESC')
                                          ->limit($limit)
                                          ->findAll();

        // Define subject icons
        $subjectIcons = [
            'Mathematics' => 'fas fa-calculator',
            'Math' => 'fas fa-calculator',
            'Science' => 'fas fa-flask',
            'Physics' => 'fas fa-atom',
            'Chemistry' => 'fas fa-vial',
            'Biology' => 'fas fa-dna',
            'English' => 'fas fa-book',
            'Tamil' => 'fas fa-language',
            'History' => 'fas fa-landmark',
            'Geography' => 'fas fa-globe',
            'Computer Science' => 'fas fa-laptop-code',
            'Economics' => 'fas fa-chart-line',
            'Commerce' => 'fas fa-briefcase',
            'Accountancy' => 'fas fa-calculator',
            'Business Studies' => 'fas fa-handshake'
        ];

        // Define subject colors
        $subjectColors = [
            'Mathematics' => 'blue',
            'Math' => 'blue',
            'Science' => 'green',
            'Physics' => 'purple',
            'Chemistry' => 'red',
            'Biology' => 'green',
            'English' => 'indigo',
            'Tamil' => 'orange',
            'History' => 'yellow',
            'Geography' => 'teal',
            'Computer Science' => 'gray',
            'Economics' => 'pink',
            'Commerce' => 'cyan',
            'Accountancy' => 'blue',
            'Business Studies' => 'emerald'
        ];

        foreach ($subjectData as $subject) {
            $subjectName = $subject['subject'];
            $subjects[] = [
                'name' => $subjectName,
                'total_questions' => (int)$subject['total_questions'],
                'approved_questions' => (int)$subject['approved_questions'],
                'pending_questions' => (int)$subject['pending_questions'],
                'rejected_questions' => (int)$subject['rejected_questions'],
                'icon' => $subjectIcons[$subjectName] ?? 'fas fa-book',
                'color' => $subjectColors[$subjectName] ?? 'gray'
            ];
        }

        return $subjects;
    }

    // ==================== USER MANAGEMENT METHODS ====================

    /**
     * Get users data for dashboard
     */
    public function getUsers()
    {
        // Check authentication for AJAX
        if (!session()->get('logged_in') || session()->get('role') !== 'schooladmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ]);
        }

        // Get school ID from session
        $schoolId = session()->get('school_id');
        if (!$schoolId) {
            // Try to get first active school as fallback
            $school = $this->schoolModel->where('status', 'active')->first();
            if ($school) {
                $schoolId = $school['id'];
                session()->set('school_id', $schoolId);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'School information not found'
                ]);
            }
        }

        // Get users with profiles for this school
        $users = $this->userModel->getUsersWithProfiles($schoolId);

        return $this->response->setJSON([
            'success' => true,
            'data' => $users
        ]);
    }

    /**
     * Add new user
     */
    public function addUser()
    {
        // Check authentication
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        if ($this->request->getMethod() === 'POST') {
            $validation = \Config\Services::validation();

            $validation->setRules([
                'name' => 'required|min_length[3]|max_length[255]',
                'email' => 'required|valid_email|is_unique[users.email]',
                'password' => 'required|min_length[6]',
                'designation' => 'permit_empty|max_length[255]',
                'phone' => 'permit_empty|min_length[10]|max_length[20]'
            ]);

            if (!$validation->withRequest($this->request)->run()) {
                return $this->response->setJSON([
                    'success' => false,
                    'errors' => $validation->getErrors()
                ]);
            }

            // Store plain password for email before hashing
            $plainPassword = $this->request->getPost('password');
            $userName = $this->request->getPost('name');
            $userEmail = $this->request->getPost('email');

            $userData = [
                'school_id' => $this->schoolId,
                'name' => $userName,
                'email' => $userEmail,
                'password' => $plainPassword, // Will be hashed by createUser method
                'status' => 'active'
            ];

            // Create user
            $userId = $this->userModel->createUser($userData);

            if ($userId) {
                // Create profile if designation or phone provided
                $designation = $this->request->getPost('designation');
                $phone = $this->request->getPost('phone');

                if ($designation || $phone) {
                    $profileData = [
                        'designation' => $designation,
                        'phone' => $phone
                    ];
                    $this->userProfileModel->saveProfile($userId, $this->schoolId, $profileData);
                }

                // Get school information for email
                $school = $this->schoolModel->find($this->schoolId);
                $schoolName = $school ? $school['name'] : 'School Question Bank';

                // Assign default role to user
                $this->userRoleModel->assignDefaultRole($userId, $this->schoolId, 'Staff');

                // Log user creation
                $auditLogger = new AuditLogger();
                $auditLogger->logCreate(
                    'user',
                    $userId,
                    $userName,
                    [
                        'name' => $userName,
                        'email' => $userEmail,
                        'designation' => $designation,
                        'phone' => $phone,
                        'role' => 'Staff'
                    ],
                    session()->get('user_id'),
                    $this->schoolId,
                    "New staff member added: {$userName} ({$userEmail})"
                );

                // Send welcome email
                $emailSent = $this->emailService->sendWelcomeEmail(
                    $userEmail,
                    $userName,
                    $plainPassword,
                    $schoolName,
                    $this->schoolId,
                    $userId
                );

                // Log email status
                if ($emailSent) {
                    log_message('info', "Welcome email sent successfully to: {$userEmail}");
                } else {
                    log_message('error', "Failed to send welcome email to: {$userEmail}");
                }

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'User added successfully' . ($emailSent ? ' and welcome email sent' : ' but email failed to send'),
                    'email_sent' => $emailSent
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to add user'
                ]);
            }
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => 'Invalid request method'
        ]);
    }

    /**
     * Delete user (soft delete)
     */
    public function deleteUser($userId)
    {
        // Check authentication
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        // Validate user ID
        if (!is_numeric($userId) || $userId <= 0) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid user ID'
            ]);
        }

        // Verify user belongs to this school and is not already deleted
        $user = $this->userModel->where('id', $userId)
                                ->where('school_id', $this->schoolId)
                                ->where('is_deleted', 0)
                                ->first();

        if (!$user) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'User not found or already deleted'
            ]);
        }

        // Log the deletion attempt
        log_message('info', "Attempting to delete user ID: {$userId} by school admin ID: {$this->schoolId}");

        // Soft delete user
        $deleteResult = $this->userModel->softDelete($userId);

        if ($deleteResult) {
            // Verify the deletion was successful by checking the database
            $deletedUser = $this->userModel->where('id', $userId)->first();

            if ($deletedUser && $deletedUser['is_deleted'] == 1 && !empty($deletedUser['deleted_at'])) {
                log_message('info', "Successfully deleted user ID: {$userId}. Deleted at: {$deletedUser['deleted_at']}");
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'User deleted successfully',
                    'deleted_at' => $deletedUser['deleted_at']
                ]);
            } else {
                log_message('error', "Delete operation returned true but user was not properly marked as deleted. User ID: {$userId}");
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Delete operation failed - user not properly marked as deleted'
                ]);
            }
        } else {
            log_message('error', "Failed to delete user ID: {$userId}");
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to delete user'
            ]);
        }
    }

    /**
     * Get user details
     */
    public function getUserDetails($userId)
    {
        // Check authentication for AJAX
        if (!session()->get('logged_in') || session()->get('role') !== 'schooladmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required'
            ]);
        }

        // Get school ID from session
        $schoolId = session()->get('school_id');
        if (!$schoolId) {
            // Try to get first active school as fallback
            $school = $this->schoolModel->where('status', 'active')->first();
            if ($school) {
                $schoolId = $school['id'];
                session()->set('school_id', $schoolId);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'School information not found'
                ]);
            }
        }

        // Get user with profile
        $user = $this->userModel->select('users.*, user_profiles.designation, user_profiles.phone')
                               ->join('user_profiles', 'user_profiles.user_id = users.id', 'left')
                               ->where('users.id', $userId)
                               ->where('users.school_id', $schoolId)
                               ->where('users.is_deleted', false)
                               ->first();

        if ($user) {
            return $this->response->setJSON([
                'success' => true,
                'data' => $user
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'User not found'
            ]);
        }
    }

    /**
     * Restore deleted user
     */
    public function restoreUser($userId)
    {
        // Check authentication
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        // Validate user ID
        if (!is_numeric($userId) || $userId <= 0) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid user ID'
            ]);
        }

        // Verify user belongs to this school and is deleted
        $user = $this->userModel->where('id', $userId)
                                ->where('school_id', $this->schoolId)
                                ->where('is_deleted', 1)
                                ->first();

        if (!$user) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Deleted user not found'
            ]);
        }

        // Restore user
        if ($this->userModel->restoreUser($userId)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'User restored successfully'
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to restore user'
            ]);
        }
    }

    /**
     * Test soft delete functionality
     */
    public function testSoftDelete()
    {
        // This is a test method to verify soft delete is working
        $testResults = [];

        // Test 1: Create a test user
        $testUser = [
            'school_id' => 1,
            'name' => 'Test User ' . time(),
            'email' => 'test' . time() . '@example.com',
            'password' => password_hash('testpass', PASSWORD_DEFAULT),
            'status' => 'active',
            'is_deleted' => 0
        ];

        $userId = $this->userModel->insert($testUser);
        $testResults['user_created'] = $userId ? 'Success' : 'Failed';

        if ($userId) {
            // Test 2: Soft delete the user
            $deleteResult = $this->userModel->softDelete($userId);
            $testResults['soft_delete'] = $deleteResult ? 'Success' : 'Failed';

            // Test 3: Check if deleted_at is set
            $deletedUser = $this->userModel->find($userId);
            $testResults['deleted_at_set'] = !empty($deletedUser['deleted_at']) ? 'Success' : 'Failed';
            $testResults['is_deleted_set'] = $deletedUser['is_deleted'] == 1 ? 'Success' : 'Failed';
            $testResults['deleted_at_value'] = $deletedUser['deleted_at'];

            // Test 4: Restore the user
            $restoreResult = $this->userModel->restoreUser($userId);
            $testResults['restore'] = $restoreResult ? 'Success' : 'Failed';

            // Clean up: Hard delete the test user
            $this->userModel->delete($userId, true);
            $testResults['cleanup'] = 'Completed';
        }

        return $this->response->setJSON([
            'test_results' => $testResults
        ]);
    }

    // ==================== QUESTION REVIEW METHODS ====================

    /**
     * Get questions for review (AJAX)
     */
    public function getQuestionsForReview()
    {
        // Check authentication
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        // Debug: Check if we have questions in database
        try {
            $totalQuestions = $this->questionModel->where('school_id', $this->schoolId)->countAllResults();
            log_message('debug', "Total questions for school {$this->schoolId}: {$totalQuestions}");
        } catch (\Exception $e) {
            log_message('error', "Database error in getQuestionsForReview: " . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Database error: ' . $e->getMessage()
            ]);
        }

        $status = $this->request->getGet('status') ?? 'all';
        $subject = $this->request->getGet('subject');
        $standard = $this->request->getGet('standard');
        $type = $this->request->getGet('type');
        $search = $this->request->getGet('search');
        $page = (int)($this->request->getGet('page') ?? 1);
        $perPage = (int)($this->request->getGet('per_page') ?? 10);

        // Ensure valid pagination values
        $page = max(1, $page);
        $perPage = max(1, min(100, $perPage)); // Limit to max 100 per page

        $filters = [
            'status' => $status,
            'subject' => $subject,
            'standard' => $standard,
            'type' => $type,
            'search' => $search
        ];

        $result = $this->questionModel->getQuestionsForReviewPaginated($this->schoolId, $filters, $page, $perPage);

        // Get statistics for the review center
        $stats = $this->questionModel->getReviewStats($this->schoolId);

        return $this->response->setJSON([
            'success' => true,
            'questions' => $result['questions'],
            'total' => $result['total'],
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($result['total'] / $perPage),
            'stats' => $stats
        ]);
    }

    /**
     * Approve question
     */
    public function approveQuestion($questionId)
    {
        // Check authentication
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        // Validate question ID
        if (!is_numeric($questionId) || $questionId <= 0) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid question ID'
            ]);
        }

        // Get feedback from request
        $feedback = $this->request->getPost('feedback');

        // Verify question belongs to this school
        $question = $this->questionModel->where('id', $questionId)
                                       ->where('school_id', $this->schoolId)
                                       ->first();

        if (!$question) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Question not found'
            ]);
        }

        // Approve question
        $adminId = session()->get('user_id');
        if ($this->questionModel->approveQuestion($questionId, $adminId, $feedback)) {
            // Log question approval
            $auditLogger = new AuditLogger();
            $auditLogger->logApproval(
                'question',
                $questionId,
                $question['question_text'] ? substr($question['question_text'], 0, 50) . '...' : 'Question #' . $questionId,
                $adminId,
                $this->schoolId,
                "Question approved by school admin" . ($feedback ? " with feedback: {$feedback}" : "")
            );

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Question approved successfully'
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to approve question'
            ]);
        }
    }

    /**
     * Reject question
     */
    public function rejectQuestion($questionId)
    {
        // Check authentication
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        // Validate question ID
        if (!is_numeric($questionId) || $questionId <= 0) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid question ID'
            ]);
        }

        // Get feedback from request (required for rejection)
        $feedback = $this->request->getPost('feedback');
        if (empty($feedback)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Feedback is required for rejection'
            ]);
        }

        // Verify question belongs to this school
        $question = $this->questionModel->where('id', $questionId)
                                       ->where('school_id', $this->schoolId)
                                       ->first();

        if (!$question) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Question not found'
            ]);
        }

        // Reject question
        $adminId = session()->get('user_id');
        if ($this->questionModel->rejectQuestion($questionId, $adminId, $feedback)) {
            // Log question rejection
            $auditLogger = new AuditLogger();
            $auditLogger->logRejection(
                'question',
                $questionId,
                $question['question_text'] ? substr($question['question_text'], 0, 50) . '...' : 'Question #' . $questionId,
                $feedback,
                $adminId,
                $this->schoolId
            );

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Question rejected successfully'
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to reject question'
            ]);
        }
    }

    /**
     * Revoke approval (set back to pending)
     */
    public function revokeApproval($questionId)
    {
        // Check authentication
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        // Validate question ID
        if (!is_numeric($questionId) || $questionId <= 0) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid question ID'
            ]);
        }

        // Verify question belongs to this school
        $question = $this->questionModel->where('id', $questionId)
                                       ->where('school_id', $this->schoolId)
                                       ->first();

        if (!$question) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Question not found'
            ]);
        }

        // Check if question is approved
        if ($question['status'] !== 'approved') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Only approved questions can have their approval revoked'
            ]);
        }

        $feedback = $this->request->getPost('feedback') ?? 'Approval revoked by admin';

        $data = [
            'status' => 'pending',
            'admin_feedback' => $feedback,
            'reviewed_by' => null,
            'reviewed_at' => null
        ];

        if ($this->questionModel->update($questionId, $data)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Question approval revoked successfully'
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to revoke approval'
            ]);
        }
    }

    /**
     * Bulk approve questions
     */
    public function bulkApproveQuestions()
    {
        // Check authentication
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        // Get question IDs and feedback
        $questionIds = json_decode($this->request->getPost('question_ids'), true);
        $feedback = $this->request->getPost('feedback');

        if (!is_array($questionIds) || empty($questionIds)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'No questions selected'
            ]);
        }

        $approvedCount = 0;
        $adminId = session()->get('user_id');

        foreach ($questionIds as $questionId) {
            // Verify question belongs to this school
            $question = $this->questionModel->where('id', $questionId)
                                          ->where('school_id', $this->schoolId)
                                          ->first();

            if ($question && $question['status'] === 'pending') {
                if ($this->questionModel->approveQuestion($questionId, $adminId, $feedback)) {
                    $approvedCount++;
                }
            }
        }

        return $this->response->setJSON([
            'success' => true,
            'approved_count' => $approvedCount,
            'message' => "$approvedCount questions approved successfully"
        ]);
    }

    /**
     * Bulk reject questions
     */
    public function bulkRejectQuestions()
    {
        // Check authentication
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        // Get question IDs and feedback
        $questionIds = json_decode($this->request->getPost('question_ids'), true);
        $feedback = $this->request->getPost('feedback');

        if (!is_array($questionIds) || empty($questionIds)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'No questions selected'
            ]);
        }

        if (empty($feedback)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Feedback is required for rejection'
            ]);
        }

        $rejectedCount = 0;
        $adminId = session()->get('user_id');

        foreach ($questionIds as $questionId) {
            // Verify question belongs to this school
            $question = $this->questionModel->where('id', $questionId)
                                          ->where('school_id', $this->schoolId)
                                          ->first();

            if ($question && $question['status'] === 'pending') {
                if ($this->questionModel->rejectQuestion($questionId, $adminId, $feedback)) {
                    $rejectedCount++;
                }
            }
        }

        return $this->response->setJSON([
            'success' => true,
            'rejected_count' => $rejectedCount,
            'message' => "$rejectedCount questions rejected successfully"
        ]);
    }

    /**
     * Get question details for review modal
     */
    public function getQuestionDetails($questionId)
    {
        // Check authentication
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        // Validate question ID
        if (!is_numeric($questionId) || $questionId <= 0) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid question ID'
            ]);
        }

        // Get question with staff details
        $question = $this->questionModel->select('questions.*, users.name as staff_name')
                                       ->join('users', 'users.id = questions.staff_id')
                                       ->where('questions.id', $questionId)
                                       ->where('questions.school_id', $this->schoolId)
                                       ->first();

        if (!$question) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Question not found'
            ]);
        }

        return $this->response->setJSON([
            'success' => true,
            'question' => $question
        ]);
    }

    /**
     * Test JSON endpoint
     */
    public function testJson()
    {
        return $this->response->setJSON([
            'success' => true,
            'message' => 'JSON test successful',
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Get staff data for admin dashboard
     */
    public function getStaff()
    {
        try {
            // Set proper headers
            $this->response->setHeader('Content-Type', 'application/json');

            // Check authentication
            $session = session();
            if (!$session->get('logged_in') || $session->get('role') !== 'schooladmin') {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Authentication required'
                ]);
            }

            // Get school ID from session
            $schoolId = $session->get('school_id');
            if (!$schoolId) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'School ID not found in session'
                ]);
            }

            // Get users with their profiles
            $staff = $this->userModel->select('users.*, user_profiles.designation, user_profiles.phone')
                                   ->join('user_profiles', 'user_profiles.user_id = users.id', 'left')
                                   ->where('users.school_id', $schoolId)
                                   ->where('users.is_deleted', false)
                                   ->orderBy('users.created_at', 'DESC')
                                   ->findAll();

            // Add question statistics for each staff member
            foreach ($staff as &$member) {
                // Get total question count for this staff member
                $questionCount = $this->questionModel->where('staff_id', $member['id'])
                                                   ->where('school_id', $schoolId)
                                                   ->countAllResults();

                // Get approved questions count
                $approvedCount = $this->questionModel->where('staff_id', $member['id'])
                                                   ->where('school_id', $schoolId)
                                                   ->where('status', 'approved')
                                                   ->countAllResults();

                // Calculate approval rate
                $approvalRate = $questionCount > 0 ? round(($approvedCount / $questionCount) * 100, 1) : 0;

                // Add statistics to staff member data
                $member['question_count'] = $questionCount;
                $member['approved_count'] = $approvedCount;
                $member['approval_rate'] = $approvalRate;

                // Ensure designation and phone are set
                $member['designation'] = $member['designation'] ?? 'Staff';
                $member['phone'] = $member['phone'] ?? 'N/A';
            }

            $data = [
                'success' => true,
                'staff' => $staff,
                'stats' => [
                    'total_staff' => count($staff),
                    'active_staff' => count(array_filter($staff, fn($s) => $s['status'] === 'active')),
                    'inactive_staff' => count(array_filter($staff, fn($s) => $s['status'] !== 'active'))
                ]
            ];

            return $this->response->setJSON($data);

        } catch (\Exception $e) {
            log_message('error', 'Error in getStaff: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Database error: ' . $e->getMessage()
            ]);
        }


    }

    /**
     * Get subjects with question counts
     */
    public function getSubjects()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $schoolId = session()->get('school_id');
        if (!$schoolId) {
            return $this->response->setJSON(['success' => false, 'message' => 'School not found']);
        }

        try {
            // Get subjects with question counts
            $subjects = $this->questionModel->select('subject,
                                                     COUNT(*) as question_count,
                                                     SUM(CASE WHEN status = "approved" THEN 1 ELSE 0 END) as approved_count,
                                                     SUM(CASE WHEN status = "pending" THEN 1 ELSE 0 END) as pending_count,
                                                     SUM(CASE WHEN status = "rejected" THEN 1 ELSE 0 END) as rejected_count')
                                           ->where('school_id', $schoolId)
                                           ->where('subject IS NOT NULL')
                                           ->where('subject !=', '')
                                           ->groupBy('subject')
                                           ->orderBy('question_count', 'DESC')
                                           ->findAll();

            return $this->response->setJSON([
                'success' => true,
                'subjects' => $subjects
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error fetching subjects: ' . $e->getMessage());
            return $this->response->setJSON(['success' => false, 'message' => 'Error fetching subjects data']);
        }
    }

    // ==================== SETTINGS METHODS ====================

    /**
     * Display settings page
     */
    public function settings()
    {
        // ✅ AUTH CHECK
        if (!session()->get('logged_in') || session()->get('role') !== 'schooladmin') {
            return redirect()->to('/login/superadmin')->with('error', 'Please login first.');
        }

        // Get school ID from session
        $schoolId = $this->getSchoolId();
        if (!$schoolId) {
            return redirect()->to('/login/superadmin')->with('error', 'School information not found.');
        }

        // Get school data
        $school = $this->schoolModel->find($schoolId);
        if (!$school) {
            return redirect()->to('/login/superadmin')->with('error', 'School not found.');
        }

        // Prepare data for view
        $data = [
            'school_name' => $school['name'],
            'school_email' => $school['email'],
            'school_phone' => $school['phone'] ?? '',
            'school_address' => $school['address'] ?? '',
            'school_status' => $school['status']
        ];

        return view('schooladmin/settings', $data);
    }

    /**
     * Update school profile settings
     */
    public function updateSchoolProfile()
    {
        // Check authentication
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $schoolId = $this->getSchoolId();

        // Get input data
        $data = [
            'name' => $this->request->getPost('name'),
            'email' => $this->request->getPost('email'),
            'phone' => $this->request->getPost('phone'),
            'address' => $this->request->getPost('address')
        ];

        // Validate required fields
        if (empty($data['name']) || empty($data['email'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'School name and email are required'
            ]);
        }

        // Validate email format
        if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Please enter a valid email address'
            ]);
        }

        try {
            // Update school profile
            $updated = $this->schoolModel->update($schoolId, $data);

            if ($updated) {
                // Update session data
                session()->set('school_name', $data['name']);

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'School profile updated successfully'
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to update school profile'
                ]);
            }
        } catch (\Exception $e) {
            log_message('error', 'School profile update error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'An error occurred while updating the profile: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Download question paper as PDF
     */
    public function downloadQuestionPaperPDF($paperId = null)
    {
        // Check authentication
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $schoolId = $this->getSchoolId();

        try {
            // If paperId is provided, download existing paper
            if ($paperId) {
                // Get paper data using existing method logic
                $db = \Config\Database::connect();
                $paper = $db->table('question_papers qp')
                           ->select('qp.*, u.name as created_by_name')
                           ->join('users u', 'u.id = qp.created_by', 'left')
                           ->where('qp.id', $paperId)
                           ->where('qp.school_id', $schoolId)
                           ->get()
                           ->getRowArray();

                if (!$paper) {
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => 'Question paper not found'
                    ]);
                }

                // Get questions for this paper
                $questions = $db->table('paper_questions pq')
                               ->select('pq.*, q.question_text, q.difficulty, q.chapter_name, q.marks as original_marks')
                               ->join('questions q', 'q.id = pq.question_id', 'left')
                               ->where('pq.paper_id', $paperId)
                               ->get()
                               ->getResultArray();

                // Format paper data for PDF generation
                $paperData = [
                    'title' => $paper['title'],
                    'academicYear' => $paper['academic_year'],
                    'examType' => $paper['exam_type'],
                    'examDate' => $paper['exam_date'],
                    'duration' => $paper['duration'],
                    'totalMarks' => $paper['total_marks'],
                    'instructions' => $paper['instructions'],
                    'selectedQuestions' => array_map(function($q) {
                        return [
                            'text' => $q['question_text'],
                            'marks' => $q['marks'] ?: $q['original_marks'],
                            'difficulty' => $q['difficulty'],
                            'chapter' => $q['chapter_name'],
                            'section_name' => $q['section_name'] ?? 'Part I'
                        ];
                    }, $questions)
                ];
            } else {
                // Generate PDF from preview data (POST request)
                $request = $this->request->getJSON(true);
                if (!$request) {
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => 'No data provided for PDF generation'
                    ]);
                }
                $paperData = $request;
            }

            // Generate PDF HTML
            $pdfHTML = $this->generateQuestionPaperPDF($paperData, $schoolId);

            // Load DomPDF
            require_once ROOTPATH . 'vendor/autoload.php';

            $options = new \Dompdf\Options();
            $options->set('defaultFont', 'Arial');
            $options->set('isRemoteEnabled', true);
            $options->set('isHtml5ParserEnabled', true);

            $dompdf = new \Dompdf\Dompdf($options);
            $dompdf->loadHtml($pdfHTML);
            $dompdf->setPaper('A4', 'portrait');
            $dompdf->render();

            // Set headers for PDF download
            $filename = 'question_paper_' . ($paperData['title'] ?? 'untitled') . '_' . date('Y-m-d') . '.pdf';
            $filename = preg_replace('/[^a-zA-Z0-9_.-]/', '_', $filename);

            return $this->response
                ->setHeader('Content-Type', 'application/pdf')
                ->setHeader('Content-Disposition', 'attachment; filename="' . $filename . '"')
                ->setHeader('Cache-Control', 'no-cache, must-revalidate')
                ->setBody($dompdf->output());

        } catch (\Exception $e) {
            log_message('error', 'PDF download error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to generate PDF: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Generate PDF HTML for question paper
     */
    private function generateQuestionPaperPDF($paperData, $schoolId)
    {
        // Get school information
        $school = $this->schoolModel->find($schoolId);

        $html = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>' . esc($paperData['title'] ?? 'Question Paper') . '</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; line-height: 1.6; }
        .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
        .school-info { margin-bottom: 15px; }
        .school-name { font-size: 18px; font-weight: bold; color: #333; }
        .school-address { font-size: 12px; color: #666; }
        .paper-title { font-size: 24px; font-weight: bold; margin: 15px 0; }
        .paper-details { font-size: 14px; color: #555; }
        .instructions { margin: 20px 0; padding: 15px; background: #f9f9f9; border-left: 4px solid #007cba; }
        .instructions h3 { margin-top: 0; color: #333; }
        .section { margin: 25px 0; page-break-inside: avoid; }
        .section-header { background: #f0f0f0; padding: 10px; border-left: 4px solid #007cba; margin-bottom: 15px; }
        .section-title { font-size: 16px; font-weight: bold; margin: 0; }
        .section-info { font-size: 12px; color: #666; margin: 5px 0 0 0; }
        .question { margin: 15px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .question-number { font-weight: bold; color: #007cba; }
        .question-text { margin: 5px 0; }
        .question-marks { float: right; background: #007cba; color: white; padding: 2px 8px; border-radius: 3px; font-size: 12px; }
        .question-meta { font-size: 11px; color: #888; margin-top: 5px; }
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
    </style>
</head>
<body>';

        // Header
        $html .= '<div class="header">
            <div class="school-info">
                <div class="school-name">' . esc($school['name'] ?? 'School Name') . '</div>
                <div class="school-address">' . esc($school['address'] ?? '') . '</div>
            </div>
            <div class="paper-title">' . esc($paperData['title'] ?? 'Question Paper') . '</div>
            <div class="paper-details">
                <strong>Academic Year:</strong> ' . esc($paperData['academicYear'] ?? 'Not specified') . ' |
                <strong>Exam Type:</strong> ' . esc($paperData['examType'] ?? 'Not specified') . ' |
                <strong>Date:</strong> ' . esc($paperData['examDate'] ?? 'Not specified') . '<br>
                <strong>Duration:</strong> ' . esc($paperData['duration'] ?? '3') . ' Hours |
                <strong>Maximum Marks:</strong> ' . esc($paperData['totalMarks'] ?? '100') . '
            </div>
        </div>';

        // Instructions
        if (!empty($paperData['instructions'])) {
            $html .= '<div class="instructions">
                <h3>Instructions to Students:</h3>
                <pre>' . esc($paperData['instructions']) . '</pre>
            </div>';
        }

        // Sections and Questions
        if (!empty($paperData['selectedQuestions'])) {
            // Group questions by section
            $questionsBySection = [];
            foreach ($paperData['selectedQuestions'] as $question) {
                $sectionName = $question['section_name'] ?? 'Unknown Section';
                if (!isset($questionsBySection[$sectionName])) {
                    $questionsBySection[$sectionName] = [];
                }
                $questionsBySection[$sectionName][] = $question;
            }

            foreach ($questionsBySection as $sectionName => $questions) {
                $sectionMarks = array_sum(array_column($questions, 'marks'));

                $html .= '<div class="section">
                    <div class="section-header">
                        <div class="section-title">' . esc($sectionName) . '</div>
                        <div class="section-info">' . count($questions) . ' Questions | ' . $sectionMarks . ' Marks</div>
                    </div>';

                foreach ($questions as $index => $question) {
                    $html .= '<div class="question">
                        <div class="question-marks">' . esc($question['marks']) . ' Mark' . ($question['marks'] > 1 ? 's' : '') . '</div>
                        <div class="question-number">' . ($index + 1) . '.</div>
                        <div class="question-text">' . esc($question['text'] ?? $question['question_text'] ?? '') . '</div>';

                    if (!empty($question['difficulty']) || !empty($question['chapter'])) {
                        $html .= '<div class="question-meta">';
                        if (!empty($question['difficulty'])) {
                            $html .= 'Difficulty: ' . esc($question['difficulty']) . ' ';
                        }
                        if (!empty($question['chapter'])) {
                            $html .= '| Chapter: ' . esc($question['chapter']);
                        }
                        $html .= '</div>';
                    }

                    $html .= '</div>';
                }

                $html .= '</div>';
            }
        }

        $html .= '<div class="no-print" style="margin-top: 30px; text-align: center;">
            <button onclick="window.print()" style="padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 14px;">
                Print Question Paper
            </button>
        </div>

</body>
</html>';

        return $html;
    }

    /**
     * Get question paper data for editing
     */
    public function getQuestionPaperForEdit($paperId)
    {
        // Check authentication
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $schoolId = $this->getSchoolId();

        // Debug logging
        log_message('info', "Edit request - Paper ID: $paperId, School ID: $schoolId");

        try {
            $db = \Config\Database::connect();

            // Get question paper details
            $paper = $db->table('question_papers qp')
                       ->select('qp.*')
                       ->where('qp.id', $paperId)
                       ->where('qp.school_id', $schoolId)
                       // Allow editing of both draft and published papers
                       ->whereIn('qp.status', ['draft', 'published'])
                       ->get()
                       ->getRowArray();

            log_message('info', "Paper found: " . ($paper ? 'Yes' : 'No'));

            if ($paper) {
                log_message('info', "Paper details: " . json_encode($paper));
                log_message('info', "Paper subject specifically: " . ($paper['subject'] ?? 'NULL'));
                log_message('info', "Paper standard specifically: " . ($paper['standard'] ?? 'NULL'));
            }

            if (!$paper) {
                // Check if paper exists with different status or school
                $anyPaper = $db->table('question_papers')
                              ->where('id', $paperId)
                              ->get()
                              ->getRowArray();

                if ($anyPaper) {
                    log_message('info', "Paper exists but status: {$anyPaper['status']}, school_id: {$anyPaper['school_id']}");
                    if ($anyPaper['school_id'] != $schoolId) {
                        return $this->response->setJSON([
                            'success' => false,
                            'message' => 'Access denied: Question paper belongs to different school'
                        ]);
                    } else {
                        return $this->response->setJSON([
                            'success' => false,
                            'message' => 'Question paper cannot be edited (status: ' . $anyPaper['status'] . ')'
                        ]);
                    }
                } else {
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => 'Question paper not found'
                    ]);
                }
            }

            // Get questions for this paper
            $questions = $db->table('paper_questions pq')
                           ->select('pq.*, q.question_text, q.difficulty, q.chapter_name, q.subject, q.standard, q.question_type')
                           ->join('questions q', 'q.id = pq.question_id', 'left')
                           ->where('pq.paper_id', $paperId)
                           ->get()
                           ->getResultArray();

            log_message('info', "Questions found: " . count($questions));

            // Format questions for editing
            $formattedQuestions = [];
            foreach ($questions as $question) {
                $formattedQuestions[] = [
                    'id' => $question['question_id'] ?? null,
                    'text' => $question['question_text'] ?? '',
                    'marks' => $question['marks'] ?? 0,
                    'difficulty' => $question['difficulty'] ?? '',
                    'chapter' => $question['chapter_name'] ?? '',
                    'subject' => $question['subject'] ?? '',
                    'standard' => $question['standard'] ?? '',
                    'question_type' => $question['question_type'] ?? '',
                    'section_name' => $question['section_name'] ?? 'Section A' // Default section
                ];
            }

            // Log the final response data
            log_message('info', "Final response paper data: " . json_encode($paper));
            log_message('info', "Final response paper subject: " . ($paper['subject'] ?? 'NULL'));

            return $this->response->setJSON([
                'success' => true,
                'data' => [
                    'paper' => $paper,
                    'questions' => $formattedQuestions
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Get question paper for edit error: ' . $e->getMessage());
            log_message('error', 'Stack trace: ' . $e->getTraceAsString());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to load question paper for editing: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Update existing question paper
     */
    public function updateQuestionPaper($paperId)
    {
        // Check authentication
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $schoolId = $this->getSchoolId();

        try {
            $request = $this->request->getJSON(true);

            if (!$request) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'No data provided'
                ]);
            }

            $db = \Config\Database::connect();

            // Verify paper exists and is editable
            $existingPaper = $db->table('question_papers')
                               ->where('id', $paperId)
                               ->where('school_id', $schoolId)
                               ->where('status', 'draft')
                               ->get()
                               ->getRowArray();

            if (!$existingPaper) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Question paper not found or cannot be edited'
                ]);
            }

            // Start transaction
            $db->transStart();

            // Update paper details
            $paperData = [
                'title' => $request['title'],
                'subject' => $request['subject'],
                'standard' => $request['standard'],
                'academic_year' => $request['academicYear'],
                'exam_type' => $request['examType'],
                'exam_date' => $request['examDate'],
                'duration' => $request['duration'],
                'total_marks' => $request['totalMarks'],
                'instructions' => $request['instructions'],
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $db->table('question_papers')
              ->where('id', $paperId)
              ->update($paperData);

            // Delete existing questions
            $db->table('paper_questions')
              ->where('paper_id', $paperId)
              ->delete();

            // Insert updated questions
            if (!empty($request['selectedQuestions'])) {
                foreach ($request['selectedQuestions'] as $question) {
                    $questionData = [
                        'paper_id' => $paperId,
                        'question_id' => $question['id'],
                        'section_name' => $question['section_name'],
                        'marks' => $question['marks'],
                        'created_at' => date('Y-m-d H:i:s')
                    ];

                    $db->table('paper_questions')->insert($questionData);
                }
            }

            // Complete transaction
            $db->transComplete();

            if ($db->transStatus() === false) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to update question paper'
                ]);
            }

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Question paper updated successfully',
                'paper_id' => $paperId
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Update question paper error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to update question paper'
            ]);
        }
    }

    /**
     * Get school settings
     */
    public function getSchoolSettings()
    {
        // Check authentication
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $schoolId = $this->getSchoolId();

        try {
            // Get school data
            $school = $this->schoolModel->find($schoolId);

            if (!$school) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'School not found'
                ]);
            }

            // Get settings from database
            $dbSettings = $this->schoolSettingsModel->getSchoolSettings($schoolId);

            // Prepare settings data with school profile
            $settings = [
                'school_profile' => [
                    'name' => $school['name'],
                    'email' => $school['email'],
                    'phone' => $school['phone'] ?? '',
                    'address' => $school['address'] ?? '',
                    'status' => $school['status']
                ]
            ];

            // Merge database settings
            $settings = array_merge($settings, $dbSettings);

            // Ensure all categories exist with defaults if not in database
            if (!isset($settings['question_bank'])) {
                $settings['question_bank'] = [
                    'question_require_approval' => true,
                    'question_auto_approve_senior' => false,
                    'question_notification_enabled' => true,
                    'question_auto_approval_days' => 7
                ];
            }

            if (!isset($settings['user_management'])) {
                $settings['user_management'] = [
                    'user_registration_method' => 'admin-only',
                    'user_email_verification' => true,
                    'user_self_profile_update' => false,
                    'user_default_role' => 'teacher'
                ];
            }

            if (!isset($settings['notifications'])) {
                $settings['notifications'] = [
                    'notification_email_new_questions' => true,
                    'notification_email_approvals' => true,
                    'notification_email_registrations' => false,
                    'notification_email_maintenance' => true,
                    'notification_email_weekly_summary' => false,
                    'notification_app_badges' => true,
                    'notification_app_sounds' => true,
                    'notification_app_desktop' => false,
                    'notification_retention_days' => 30
                ];
            }

            if (!isset($settings['system'])) {
                $settings['system'] = [
                    'system_timezone' => 'Asia/Kolkata',
                    'system_date_format' => 'DD/MM/YYYY',
                    'system_language' => 'en',
                    'system_maintenance_mode' => false,
                    'system_data_retention_months' => 12,
                    'system_auto_backup' => true,
                    'system_backup_frequency' => 'daily'
                ];
            }

            return $this->response->setJSON([
                'success' => true,
                'data' => $settings
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Error fetching school settings: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'An error occurred while fetching settings'
            ]);
        }
    }

    /**
     * Save all settings
     */
    public function saveAllSettings()
    {
        // Check authentication
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $schoolId = $this->getSchoolId();

        try {
            // Get all settings data from request
            $settingsData = $this->request->getJSON(true);

            // Update school profile if provided
            if (isset($settingsData['school_profile'])) {
                $profileData = $settingsData['school_profile'];
                unset($profileData['status']); // Don't allow status changes from settings

                $this->schoolModel->update($schoolId, $profileData);

                // Update session
                if (isset($profileData['name'])) {
                    session()->set('school_name', $profileData['name']);
                }
            }

            // Save other settings to the settings table
            $settingsToSave = $settingsData;
            unset($settingsToSave['school_profile']); // Remove school profile as it's handled separately

            if (!empty($settingsToSave)) {
                $this->schoolSettingsModel->updateSettings($schoolId, $settingsToSave);
            }

            return $this->response->setJSON([
                'success' => true,
                'message' => 'All settings saved successfully'
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Error saving school settings: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'An error occurred while saving settings'
            ]);
        }
    }

    /**
     * Export selected questions to CSV
     */
    public function exportQuestions()
    {
        // Check authentication
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $questionIds = $this->request->getPost('question_ids');

        if (empty($questionIds) || !is_array($questionIds)) {
            return redirect()->back()->with('error', 'No questions selected for export');
        }

        try {
            // Get questions with staff details
            $questions = $this->questionModel
                ->select('questions.*, users.name as staff_name, users.email as staff_email')
                ->join('users', 'users.id = questions.staff_id')
                ->whereIn('questions.id', $questionIds)
                ->where('questions.school_id', $this->schoolId)
                ->findAll();

            if (empty($questions)) {
                return redirect()->back()->with('error', 'No questions found for export');
            }

            // Generate CSV content
            $csvContent = $this->generateQuestionsCsv($questions);

            // Set headers for download
            $filename = 'questions_export_' . date('Y-m-d_H-i-s') . '.csv';

            return $this->response
                ->setHeader('Content-Type', 'text/csv')
                ->setHeader('Content-Disposition', 'attachment; filename="' . $filename . '"')
                ->setHeader('Cache-Control', 'no-cache, must-revalidate')
                ->setBody($csvContent);

        } catch (\Exception $e) {
            log_message('error', 'Export questions error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to export questions');
        }
    }

    /**
     * Generate CSV content for questions
     */
    private function generateQuestionsCsv($questions)
    {
        $csv = [];

        // CSV Headers
        $csv[] = [
            'ID',
            'Question Text',
            'Subject',
            'Standard',
            'Question Type',
            'Difficulty',
            'Marks',
            'Chapter',
            'Chapter Name',
            'Topic Name',
            'Option A',
            'Option B',
            'Option C',
            'Option D',
            'Correct Answer',
            'Answer/Explanation',
            'Status',
            'Staff Name',
            'Staff Email',
            'Admin Feedback',
            'Created At',
            'Reviewed At'
        ];

        // Add question data
        foreach ($questions as $question) {
            $csv[] = [
                $question['id'],
                $this->cleanCsvField($question['question_text']),
                $question['subject'],
                'Class ' . $question['standard'],
                $this->getQuestionTypeText($question['question_type']),
                ucfirst($question['difficulty']),
                $question['marks'],
                $question['chapter'] ?? '',
                $question['chapter_name'] ?? '',
                $question['topic_name'] ?? '',
                $this->cleanCsvField($question['option_a'] ?? ''),
                $this->cleanCsvField($question['option_b'] ?? ''),
                $this->cleanCsvField($question['option_c'] ?? ''),
                $this->cleanCsvField($question['option_d'] ?? ''),
                $question['correct_answer'] ?? '',
                $this->cleanCsvField($question['answer'] ?? ''),
                ucfirst($question['status']),
                $question['staff_name'],
                $question['staff_email'],
                $this->cleanCsvField($question['admin_feedback'] ?? ''),
                $question['created_at'],
                $question['reviewed_at'] ?? ''
            ];
        }

        // Convert to CSV string
        $output = fopen('php://temp', 'r+');
        foreach ($csv as $row) {
            fputcsv($output, $row);
        }
        rewind($output);
        $csvString = stream_get_contents($output);
        fclose($output);

        return $csvString;
    }

    /**
     * Clean CSV field to prevent injection and formatting issues
     */
    private function cleanCsvField($field)
    {
        if (empty($field)) return '';

        // Remove HTML tags
        $field = strip_tags($field);

        // Replace multiple spaces with single space
        $field = preg_replace('/\s+/', ' ', $field);

        // Trim whitespace
        $field = trim($field);

        return $field;
    }

    /**
     * Get human-readable question type text
     */
    private function getQuestionTypeText($type)
    {
        switch ($type) {
            case 'multiple_choice':
                return 'Multiple Choice';
            case 'short_answer':
                return 'Short Answer';
            case 'long_answer':
                return 'Long Answer';
            case 'essay':
                return 'Essay';
            default:
                return ucfirst($type);
        }
    }

    /**
     * Update staff profile
     */
    public function updateStaff()
    {
        try {
            // Set proper headers
            $this->response->setHeader('Content-Type', 'application/json');

            // Check authentication
            if (!session()->get('logged_in') || session()->get('role') !== 'schooladmin') {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Authentication required'
                ]);
            }

            // Get JSON input
            $input = $this->request->getJSON(true);

            if (!$input) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Invalid input data'
                ]);
            }

            // Validate required fields
            $requiredFields = ['staff_id', 'name', 'email', 'designation', 'status'];
            foreach ($requiredFields as $field) {
                if (empty($input[$field])) {
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => "Field '{$field}' is required"
                    ]);
                }
            }

            // Validate email format
            if (!filter_var($input['email'], FILTER_VALIDATE_EMAIL)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Invalid email format'
                ]);
            }

            // Get school ID from session
            $schoolId = session()->get('school_id');
            if (!$schoolId) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'School ID not found in session'
                ]);
            }

            // Load user model
            $userModel = new \App\Models\UserModel();

            // Check if staff member exists and belongs to this school
            $staff = $userModel->where('id', $input['staff_id'])
                              ->where('school_id', $schoolId)
                              ->where('is_deleted', false)
                              ->first();

            if (!$staff) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Staff member not found'
                ]);
            }

            // Check if email is already taken by another user
            $existingUser = $userModel->where('email', $input['email'])
                                    ->where('id !=', $input['staff_id'])
                                    ->where('is_deleted', false)
                                    ->first();

            if ($existingUser) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Email is already taken by another user'
                ]);
            }

            // Prepare user table update data (only fields that belong to users table)
            $userUpdateData = [
                'name' => trim($input['name']),
                'email' => trim($input['email']),
                'status' => trim($input['status']),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Update user table
            $userUpdated = $userModel->update($input['staff_id'], $userUpdateData);

            if (!$userUpdated) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to update staff profile'
                ]);
            }

            // Prepare profile data (designation and phone belong to user_profiles table)
            $profileData = [
                'designation' => isset($input['designation']) ? trim($input['designation']) : null,
                'phone' => isset($input['phone']) ? trim($input['phone']) : null,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Update or create user profile
            $profileUpdated = $this->userProfileModel->saveProfile($input['staff_id'], $schoolId, $profileData);

            if ($profileUpdated) {
                // Combine data for response
                $responseData = array_merge($userUpdateData, $profileData);

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Staff profile updated successfully',
                    'data' => $responseData
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to update staff profile details'
                ]);
            }

        } catch (\Exception $e) {
            log_message('error', 'Error updating staff: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Server error occurred'
            ]);
        }
    }


}
