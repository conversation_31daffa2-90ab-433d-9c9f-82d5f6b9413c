<?php

use CodeIgniter\Router\RouteCollection;

$routes->get('/', 'Landing::index');
// 📌 Use this one only if Landing handles register:
$routes->get('/register', 'Landing::register');

// 📌 Or this, if SchoolController handles it (comment one):
// $routes->get('register', 'SchoolController::showRegisterForm');
$routes->post('school/register', 'SchoolController::register');

// 🔐 Email OTP verification
$routes->post('/register/send-otp', 'Register::sendOtp');
$routes->post('/register/verify-otp', 'Register::verifyOtp');

// ✅ Combined login logic
$routes->get('login', 'Auth::schooladminLogin'); // Default login redirects to school admin login
$routes->get('login/superadmin', 'Auth::superadminLogin');
$routes->get('login/schooladmin', 'Auth::schooladminLogin');
$routes->post('auth/login', 'Auth::login');
$routes->get('logout', 'Auth::logout');

// Universal logout route (can be used by all user types)
$routes->get('auth/logout', 'Auth::logout');

// ✅ Dashboard routes
$routes->get('superadmin/dashboard', 'SuperAdmin::dashboard');
$routes->get('schooladmin/dashboard', 'SchoolAdmin::dashboard');
$routes->get('schooladmin/question-papers', 'SchoolAdmin::questionPapers');

// 💳 Subscription Routes
$routes->group('subscription', function($routes) {
    $routes->get('plans', 'Subscription::plans');
    $routes->post('select-plan', 'Subscription::selectPlan');
    $routes->get('current', 'Subscription::current');
    $routes->get('upgrade', 'Subscription::upgrade');
    $routes->post('upgrade', 'Subscription::processUpgrade');
    $routes->post('cancel', 'Subscription::cancel');
    $routes->get('usage', 'Subscription::getUsage');
});

// 💰 Payment Routes (Razorpay Integration)
$routes->group('payment', function($routes) {
    $routes->get('process', 'Payment::process');
    $routes->post('initiate', 'Payment::initiate');
    $routes->post('verify', 'Payment::verify');
    $routes->get('success', 'Payment::success');
    $routes->get('failure', 'Payment::failure');
    $routes->get('history', 'Payment::history');
    $routes->get('transaction-details/(:num)', 'Payment::transactionDetails/$1');
    $routes->get('invoice/(:num)', 'Payment::invoice/$1');
    $routes->post('webhook/(:segment)', 'Payment::webhook/$1');

    // Development webhook testing (remove in production)
    if (ENVIRONMENT === 'development') {
        $routes->get('test-webhook/(:segment)', 'Payment::testWebhook/$1');
        $routes->get('debugCompletePending', 'Payment::debugCompletePending');
        $routes->get('debugSession', 'Payment::debugSession');
    }
});

// ✅ SuperAdmin AJAX approve/reject group
$routes->group('superadmin', ['filter' => 'auth'], function($routes) {
    $routes->get('schools', 'SuperAdmin::schools');
    $routes->post('approve/(:num)', 'SuperAdmin::approveSchool/$1');
    $routes->post('reject/(:num)', 'SuperAdmin::rejectSchool/$1');
    $routes->get('school/(:num)/details', 'SuperAdmin::viewSchoolDetails/$1');

    // User Management Routes
    $routes->get('getUsers', 'SuperAdmin::getUsers');
    $routes->get('getUserDetails/(:num)', 'SuperAdmin::getUserDetails/$1');
    $routes->post('toggleUserStatus/(:num)', 'SuperAdmin::toggleUserStatus/$1');
    $routes->delete('deleteUser/(:num)', 'SuperAdmin::deleteUser/$1');

    // Audit Log Routes
    $routes->get('getAuditLogs', 'SuperAdmin::getAuditLogs');
    $routes->get('getAuditStats', 'SuperAdmin::getAuditStats');
    $routes->get('getSuperAdminLogs', 'SuperAdmin::getSuperAdminLogs');
    $routes->get('exportAuditLogs', 'SuperAdmin::exportAuditLogs');
    $routes->get('getAuditFilterOptions', 'SuperAdmin::getAuditFilterOptions');

    // Dashboard Activity Routes
    $routes->get('getRecentActivities', 'SuperAdmin::getRecentActivities');
    $routes->get('getNotifications', 'SuperAdmin::getNotifications');
});

// ✅ School status views
$routes->group('admin', ['filter' => 'auth'], function($routes) {
    $routes->group('schools', function($routes) {
        $routes->get('pending', 'SchoolController::pending');
        $routes->post('approve/(:num)', 'SchoolController::approve/$1');
        $routes->post('superadmin/reject/(:num)', 'SuperAdmin::rejectSchool/$1');
        $routes->get('active', 'SchoolController::active');
        $routes->get('all', 'SchoolController::all');
    });
});
$routes->get('superadmin/dashboard', 'SuperAdmin::dashboard', ['filter' => 'auth:superadmin']);
$routes->get('schooladmin/dashboard', 'SchoolAdmin::dashboard', ['filter' => 'auth:schooladmin']);

// ✅ SchoolAdmin User Management Routes
$routes->get('schooladmin/getUsers', 'SchoolAdmin::getUsers');
$routes->get('schooladmin/getStaff', 'SchoolAdmin::getStaff');
$routes->post('schooladmin/addUser', 'SchoolAdmin::addUser');
$routes->post('schooladmin/deleteUser/(:num)', 'SchoolAdmin::deleteUser/$1');
$routes->post('schooladmin/restoreUser/(:num)', 'SchoolAdmin::restoreUser/$1');
$routes->get('schooladmin/getDeletedUsers', 'SchoolAdmin::getDeletedUsers');
$routes->get('schooladmin/getUserDetails/(:num)', 'SchoolAdmin::getUserDetails/$1');

// ✅ SchoolAdmin Staff Management Routes
$routes->get('schooladmin/getStaffMember/(:num)', 'SchoolAdmin::getStaffMember/$1');
$routes->post('schooladmin/updateStaffMember/(:num)', 'SchoolAdmin::updateStaffMember/$1');
$routes->post('schooladmin/toggleStaffStatus/(:num)', 'SchoolAdmin::toggleStaffStatus/$1');
$routes->delete('schooladmin/deleteStaffMember/(:num)', 'SchoolAdmin::deleteStaffMember/$1');
$routes->get('schooladmin/testSoftDelete', 'SchoolAdmin::testSoftDelete'); // Remove in production

// ✅ SchoolAdmin Settings Routes
$routes->get('schooladmin/settings', 'SchoolAdmin::settings');
$routes->post('schooladmin/updateSchoolProfile', 'SchoolAdmin::updateSchoolProfile');
$routes->get('schooladmin/getSchoolSettings', 'SchoolAdmin::getSchoolSettings');
$routes->post('schooladmin/saveAllSettings', 'SchoolAdmin::saveAllSettings');

// ✅ SchoolAdmin Question Review Routes
$routes->get('schooladmin/getQuestionsForReview', 'SchoolAdmin::getQuestionsForReview');
$routes->post('schooladmin/approveQuestion/(:num)', 'SchoolAdmin::approveQuestion/$1');
$routes->post('schooladmin/rejectQuestion/(:num)', 'SchoolAdmin::rejectQuestion/$1');
$routes->get('schooladmin/getQuestionDetails/(:num)', 'SchoolAdmin::getQuestionDetails/$1');

// ✅ SchoolAdmin Question Paper Creation Routes
$routes->get('schooladmin/getApprovedQuestions', 'SchoolAdmin::getApprovedQuestions');
$routes->post('schooladmin/createQuestionPaper', 'SchoolAdmin::createQuestionPaper');
$routes->get('schooladmin/getQuestionPapers', 'SchoolAdmin::getQuestionPapers');
$routes->get('schooladmin/getQuestionPaper/(:num)', 'SchoolAdmin::getQuestionPaper/$1');
$routes->delete('schooladmin/deleteQuestionPaper/(:num)', 'SchoolAdmin::deleteQuestionPaper/$1');
$routes->get('schooladmin/testSession', 'SchoolAdmin::testSession');
$routes->post('schooladmin/downloadQuestionPaperPDF', 'SchoolAdmin::downloadQuestionPaperPDF');
$routes->get('schooladmin/downloadQuestionPaperPDF/(:num)', 'SchoolAdmin::downloadQuestionPaperPDF/$1');
$routes->get('schooladmin/getQuestionPaperForEdit/(:num)', 'SchoolAdmin::getQuestionPaperForEdit/$1');
$routes->put('schooladmin/updateQuestionPaper/(:num)', 'SchoolAdmin::updateQuestionPaper/$1');


// Staff Routes
$routes->get('staff/login', 'Staff::login');
$routes->post('staff/authenticate', 'Staff::authenticate');
$routes->get('staff/dashboard', 'Staff::dashboard');
$routes->get('staff/profile', 'Staff::profile');
$routes->post('staff/updateProfile', 'Staff::updateProfile');
$routes->get('staff/logout', 'Auth::logout');

// Staff Question Management Routes
$routes->get('staff/getSubjects', 'Staff::getSubjects');
$routes->post('staff/createQuestion', 'Staff::createQuestion');
$routes->post('staff/saveDraft', 'Staff::saveDraft');
$routes->get('staff/getQuestions', 'Staff::getQuestions');
$routes->get('staff/getQuestionDetails/(:num)', 'Staff::getQuestionDetails/$1');
$routes->post('staff/updateQuestion/(:num)', 'Staff::updateQuestion/$1');
$routes->put('staff/updateQuestion/(:num)', 'Staff::updateQuestion/$1');
$routes->delete('staff/deleteQuestion/(:num)', 'Staff::deleteQuestion/$1');
$routes->get('staff/getSubjectsWithCounts', 'Staff::getSubjectsWithCounts');
$routes->get('staff/getQuestionsBySubject/(:segment)', 'Staff::getQuestionsBySubject/$1');
$routes->get('staff/getDetailedReports', 'Staff::getDetailedReports');
$routes->get('staff/getRecentActivities', 'Staff::getRecentActivities');
$routes->get('staff/testUpdate/(:num)', 'Staff::testUpdate/$1');

// In Routes.php
$routes->post('school/check-email', 'SchoolController::checkEmail');


