<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use CodeIgniter\HTTP\CLIRequest;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Psr\Log\LoggerInterface;

abstract class BaseController extends Controller
{
    /**
     * Instance of the main Request object.
     *
     * @var CLIRequest|IncomingRequest
     */
    protected $request;

    /**
     * Helpers to be available in all controllers.
     *
     * @var list<string>
     */
    protected $helpers = ['url', 'form', 'session'];

    /**
     * @return void
     */
    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        // Do Not Edit This Line
        parent::initController($request, $response, $logger);

        // Load session service
        $this->session = \Config\Services::session();

        // 🟩 Multi-tenant support: Inject `school_id` into global request service
        if ($this->session->has('school_id')) {
            service('request')->school_id = $this->session->get('school_id');
        } else {
            service('request')->school_id = null;
        }

        // You can preload models here if needed in all controllers
        // Example: $this->userModel = new \App\Models\UserModel();
    }
}
