<?php

namespace App\Controllers;

use App\Models\SchoolModel;
use App\Models\UserModel;
use App\Models\UserProfileModel;
use App\Models\AuditLogModel;
use App\Models\ActivityLogModel;
use App\Models\SuperAdminLogModel;
use App\Services\AuditLogger;

class SuperAdmin extends BaseController
{
    protected $schoolModel;
    protected $userModel;
    protected $userProfileModel;
    protected $auditLogModel;
    protected $activityLogModel;
    protected $superAdminLogModel;
    protected $auditLogger;

    public function __construct()
    {
        $this->schoolModel = new SchoolModel();
        $this->userModel = new UserModel();
        $this->userProfileModel = new UserProfileModel();
        $this->auditLogModel = new AuditLogModel();
        $this->activityLogModel = new ActivityLogModel();
        $this->superAdminLogModel = new SuperAdminLogModel();
        $this->auditLogger = new AuditLogger();
    }

    public function dashboard()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
            return redirect()->to('/login/superadmin')->with('error', 'Please login first.');
        }

        // Get user statistics
        $totalUsers = $this->userModel->countAllResults();
        $schoolAdmins = $this->userModel->join('schools', 'schools.id = users.school_id')
                                      ->where('users.is_deleted', 0)
                                      ->countAllResults();
        $staffMembers = $this->userModel->join('user_roles', 'user_roles.user_id = users.id')
                                       ->join('roles', 'roles.id = user_roles.role_id')
                                       ->where('roles.name !=', 'Admin')
                                       ->where('users.is_deleted', 0)
                                       ->countAllResults();

        $data = [
            'schools' => $this->schoolModel->findAll(),
            'pendingSchools' => $this->schoolModel->where('status', 'inactive')->findAll(),
            'totalUsers' => $totalUsers,
            'schoolAdmins' => $schoolAdmins,
            'staffMembers' => $staffMembers
        ];

        return view('superadmin/dashboard', $data);
    }

    // In app/Controllers/SuperAdmin.php

public function approveSchool($id)
{
    if (!$this->validate(['id' => 'required|is_natural_no_zero'])) {
        return $this->response->setStatusCode(400)->setJSON([
            'success' => false,
            'message' => 'Invalid school ID'
        ]);
    }

    $school = $this->schoolModel->find($id);
    if (!$school) {
        return $this->response->setJSON(['success' => false, 'message' => 'School not found']);
    }

    $updated = $this->schoolModel->update($id, ['status' => 'active']);

    if ($updated) {
        try {
            $userId = session()->get('user_id');

            // Log the audit action
            $this->auditLogger->logApproval(
                'school',
                $id,
                $school['name'],
                $userId,
                $id,
                "Super admin approved school registration"
            );

            // Log super admin action
            $this->auditLogger->logSuperAdminAction(
                'approve_school',
                $userId,
                "Approved school registration: {$school['name']}",
                $id,
                null,
                ['status' => $school['status']],
                ['status' => 'active'],
                'high'
            );
        } catch (\Exception $e) {
            log_message('error', "Failed to log school approval audit: " . $e->getMessage());
        }
    }

    return $this->response->setJSON([
        'success' => $updated,
        'message' => $updated ? 'School approved successfully' : 'Failed to approve school'
    ]);
}

public function rejectSchool($id)
{
    $reason = $this->request->getPost('reason') ?? $this->request->getJSON(true)['reason'] ?? null;

    if (!$reason) {
        return $this->response->setStatusCode(400)->setJSON([
            'success' => false,
            'message' => 'Reason for rejection is required'
        ]);
    }

    $school = $this->schoolModel->find($id);
    if (!$school) {
        return $this->response->setJSON(['success' => false, 'message' => 'School not found']);
    }

    $updated = $this->schoolModel->update($id, [
        'status' => 'rejected',
        'rejection_reason' => $reason
    ]);

    if ($updated) {
        // Log the audit action
        $this->auditLogger->logRejection(
            'school',
            $id,
            $school['name'],
            $reason,
            session()->get('user_id'),
            $id
        );

        // Log super admin action
        $this->auditLogger->logSuperAdminAction(
            'reject_school',
            session()->get('user_id'),
            "Rejected school registration: {$school['name']} - Reason: {$reason}",
            $id,
            null,
            ['status' => $school['status']],
            ['status' => 'rejected', 'rejection_reason' => $reason],
            'high'
        );
    }

    return $this->response->setJSON([
        'success' => $updated,
        'message' => $updated ? 'School rejected successfully' : 'Failed to reject school'
    ]);
}
public function viewSchoolDetails($id)
{
    if (!session()->get('isSuperAdmin')) {
        return redirect()->to('/login')->with('error', 'Access denied.');
    }

    $school = $this->schoolModel->find($id);

    if (!$school) {
        throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound("School not found");
    }

    return view('superadmin/view_school', ['school' => $school]);
}

    /**
     * Get all users for user management
     */
    public function getUsers()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ]);
        }

        try {
            // Get users with school information and profiles
            $users = $this->userModel
                ->select('users.*, schools.name as school_name, user_profiles.designation, user_profiles.phone')
                ->join('schools', 'schools.id = users.school_id', 'left')
                ->join('user_profiles', 'user_profiles.user_id = users.id', 'left')
                ->where('users.is_deleted', 0)
                ->orderBy('users.created_at', 'DESC')
                ->findAll();

            return $this->response->setJSON([
                'success' => true,
                'users' => $users
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to fetch users: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get user details by ID
     */
    public function getUserDetails($userId)
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ]);
        }

        try {
            $user = $this->userModel
                ->select('users.*, schools.name as school_name, user_profiles.designation, user_profiles.phone')
                ->join('schools', 'schools.id = users.school_id', 'left')
                ->join('user_profiles', 'user_profiles.user_id = users.id', 'left')
                ->where('users.id', $userId)
                ->where('users.is_deleted', 0)
                ->first();

            if (!$user) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'User not found'
                ]);
            }

            return $this->response->setJSON([
                'success' => true,
                'user' => $user
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to fetch user details: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Update user status (activate/deactivate)
     */
    public function toggleUserStatus($userId)
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ]);
        }

        try {
            $user = $this->userModel->find($userId);
            if (!$user) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'User not found'
                ]);
            }

            $newStatus = ($user['status'] === 'active') ? 'inactive' : 'active';
            $updated = $this->userModel->update($userId, ['status' => $newStatus]);

            if ($updated) {
                // Log the audit action
                $this->auditLogger->logUpdate(
                    'user',
                    $userId,
                    $user['name'],
                    ['status' => $user['status']],
                    ['status' => $newStatus],
                    session()->get('user_id'),
                    null,
                    "Super admin changed user status from {$user['status']} to {$newStatus}"
                );

                // Log super admin action
                $this->auditLogger->logSuperAdminAction(
                    'toggle_user_status',
                    session()->get('user_id'),
                    "Changed user status for {$user['name']} from {$user['status']} to {$newStatus}",
                    $user['school_id'],
                    $userId,
                    ['status' => $user['status']],
                    ['status' => $newStatus],
                    'medium'
                );
            }

            return $this->response->setJSON([
                'success' => $updated,
                'message' => $updated ? 'User status updated successfully' : 'Failed to update user status',
                'new_status' => $newStatus
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to update user status: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Delete user (soft delete)
     */
    public function deleteUser($userId)
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ]);
        }

        try {
            $user = $this->userModel->find($userId);
            if (!$user) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'User not found'
                ]);
            }

            // Soft delete the user
            $updated = $this->userModel->update($userId, [
                'is_deleted' => 1,
                'deleted_at' => date('Y-m-d H:i:s')
            ]);

            if ($updated) {
                // Log the audit action
                $this->auditLogger->logDelete(
                    'user',
                    $userId,
                    $user['name'],
                    $user,
                    session()->get('user_id'),
                    $user['school_id'],
                    "Super admin deleted user: {$user['name']}"
                );

                // Log super admin action
                $this->auditLogger->logSuperAdminAction(
                    'delete_user',
                    session()->get('user_id'),
                    "Deleted user: {$user['name']} ({$user['email']})",
                    $user['school_id'],
                    $userId,
                    $user,
                    null,
                    'high'
                );
            }

            return $this->response->setJSON([
                'success' => $updated,
                'message' => $updated ? 'User deleted successfully' : 'Failed to delete user'
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to delete user: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get audit logs with filters and pagination
     */
    public function getAuditLogs()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ]);
        }

        try {
            $request = $this->request;
            $page = (int)($request->getGet('page') ?? 1);
            $perPage = (int)($request->getGet('per_page') ?? 50);

            $filters = [
                'school_id' => $request->getGet('school_id'),
                'user_id' => $request->getGet('user_id'),
                'action' => $request->getGet('action'),
                'entity_type' => $request->getGet('entity_type'),
                'severity' => $request->getGet('severity'),
                'status' => $request->getGet('status'),
                'date_from' => $request->getGet('date_from'),
                'date_to' => $request->getGet('date_to'),
                'search' => $request->getGet('search')
            ];

            // Remove empty filters
            $filters = array_filter($filters, function($value) {
                return $value !== null && $value !== '';
            });

            $result = $this->auditLogModel->getAuditLogs($filters, $page, $perPage);

            return $this->response->setJSON([
                'success' => true,
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to fetch audit logs: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get audit log statistics
     */
    public function getAuditStats()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ]);
        }

        try {
            $request = $this->request;
            $schoolId = $request->getGet('school_id');
            $dateFrom = $request->getGet('date_from');
            $dateTo = $request->getGet('date_to');

            $stats = $this->auditLogModel->getAuditStats($schoolId, $dateFrom, $dateTo);

            return $this->response->setJSON([
                'success' => true,
                'stats' => $stats
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to fetch audit statistics: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get super admin logs
     */
    public function getSuperAdminLogs()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ]);
        }

        try {
            $request = $this->request;
            $page = (int)($request->getGet('page') ?? 1);
            $perPage = (int)($request->getGet('per_page') ?? 50);

            $filters = [
                'admin_user_id' => $request->getGet('admin_user_id'),
                'action' => $request->getGet('action'),
                'target_school_id' => $request->getGet('target_school_id'),
                'severity' => $request->getGet('severity'),
                'date_from' => $request->getGet('date_from'),
                'date_to' => $request->getGet('date_to'),
                'search' => $request->getGet('search')
            ];

            // Remove empty filters
            $filters = array_filter($filters, function($value) {
                return $value !== null && $value !== '';
            });

            $result = $this->superAdminLogModel->getSuperAdminLogs($filters, $page, $perPage);

            return $this->response->setJSON([
                'success' => true,
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to fetch super admin logs: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Export audit logs to CSV
     */
    public function exportAuditLogs()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
            return redirect()->to('/login/superadmin')->with('error', 'Access denied');
        }

        try {
            $request = $this->request;

            $filters = [
                'school_id' => $request->getGet('school_id'),
                'user_id' => $request->getGet('user_id'),
                'action' => $request->getGet('action'),
                'entity_type' => $request->getGet('entity_type'),
                'severity' => $request->getGet('severity'),
                'status' => $request->getGet('status'),
                'date_from' => $request->getGet('date_from'),
                'date_to' => $request->getGet('date_to'),
                'search' => $request->getGet('search')
            ];

            // Remove empty filters
            $filters = array_filter($filters, function($value) {
                return $value !== null && $value !== '';
            });

            // Get all logs (no pagination for export)
            $result = $this->auditLogModel->getAuditLogs($filters, 1, 10000);
            $logs = $result['logs'];

            // Set headers for CSV download
            $filename = 'audit_logs_' . date('Y-m-d_H-i-s') . '.csv';
            header('Content-Type: text/csv');
            header('Content-Disposition: attachment; filename="' . $filename . '"');

            // Create file pointer
            $output = fopen('php://output', 'w');

            // Add CSV headers
            fputcsv($output, [
                'ID', 'Date/Time', 'School', 'User', 'Action', 'Entity Type',
                'Entity Name', 'Description', 'Severity', 'Status', 'IP Address'
            ]);

            // Add data rows
            foreach ($logs as $log) {
                fputcsv($output, [
                    $log['id'],
                    $log['created_at'],
                    $log['school_name'] ?? 'N/A',
                    $log['user_name'] ?? 'System',
                    $log['action'],
                    $log['entity_type'],
                    $log['entity_name'] ?? 'N/A',
                    $log['description'],
                    $log['severity'],
                    $log['status'],
                    $log['ip_address'] ?? 'N/A'
                ]);
            }

            fclose($output);
            exit;
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to export audit logs: ' . $e->getMessage());
        }
    }

    /**
     * Get filter options for audit logs
     */
    public function getAuditFilterOptions()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ]);
        }

        try {
            $actions = $this->auditLogModel->getDistinctActions();
            $entityTypes = $this->auditLogModel->getDistinctEntityTypes();
            $schools = $this->schoolModel->select('id, name')->findAll();

            return $this->response->setJSON([
                'success' => true,
                'options' => [
                    'actions' => $actions,
                    'entity_types' => $entityTypes,
                    'schools' => $schools,
                    'severities' => ['low', 'medium', 'high', 'critical'],
                    'statuses' => ['success', 'failed', 'warning']
                ]
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to fetch filter options: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get recent activities for SuperAdmin dashboard
     */
    public function getRecentActivities()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ]);
        }

        try {
            // Get recent super admin logs
            $recentLogs = $this->superAdminLogModel->getRecentSuperAdminLogs(10);

            // Get recent school registrations
            $recentSchools = $this->schoolModel->select('id, name, created_at, status')
                                              ->orderBy('created_at', 'DESC')
                                              ->limit(5)
                                              ->findAll();

            // Get recent user registrations
            $recentUsers = $this->userModel->select('users.id, users.name, users.email, users.created_at, schools.name as school_name')
                                          ->join('schools', 'schools.id = users.school_id', 'left')
                                          ->where('users.is_deleted', 0)
                                          ->orderBy('users.created_at', 'DESC')
                                          ->limit(5)
                                          ->findAll();

            $activities = [];

            // Process super admin logs
            foreach ($recentLogs as $log) {
                $activities[] = [
                    'type' => 'admin_action',
                    'title' => $this->formatLogTitle($log['action']),
                    'description' => $log['description'] ?: $this->formatLogDescription($log),
                    'time' => $log['created_at'],
                    'icon' => $this->getLogIcon($log['action']),
                    'color' => $this->getLogColor($log['severity']),
                    'severity' => $log['severity']
                ];
            }

            // Process recent schools
            foreach ($recentSchools as $school) {
                $activities[] = [
                    'type' => 'school',
                    'title' => 'New school registered',
                    'description' => $school['name'] . ' joined the platform',
                    'time' => $school['created_at'],
                    'icon' => 'fas fa-school',
                    'color' => 'blue',
                    'severity' => 'medium'
                ];
            }

            // Process recent users
            foreach ($recentUsers as $user) {
                $activities[] = [
                    'type' => 'user',
                    'title' => 'New user registered',
                    'description' => $user['name'] . ' joined ' . ($user['school_name'] ?: 'the platform'),
                    'time' => $user['created_at'],
                    'icon' => 'fas fa-user-plus',
                    'color' => 'green',
                    'severity' => 'low'
                ];
            }

            // Sort activities by time (most recent first)
            usort($activities, function($a, $b) {
                return strtotime($b['time']) - strtotime($a['time']);
            });

            // Limit to 15 most recent activities
            $activities = array_slice($activities, 0, 15);

            return $this->response->setJSON([
                'success' => true,
                'activities' => $activities
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to fetch recent activities: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Format log title based on action
     */
    private function formatLogTitle($action)
    {
        $titles = [
            'school_created' => 'School Created',
            'school_updated' => 'School Updated',
            'school_deleted' => 'School Deleted',
            'user_created' => 'User Created',
            'user_updated' => 'User Updated',
            'user_deleted' => 'User Deleted',
            'subscription_updated' => 'Subscription Updated',
            'system_settings_updated' => 'System Settings Updated',
            'login' => 'Admin Login',
            'logout' => 'Admin Logout'
        ];

        return $titles[$action] ?? ucwords(str_replace('_', ' ', $action));
    }

    /**
     * Format log description
     */
    private function formatLogDescription($log)
    {
        if ($log['target_school_name']) {
            return 'Action performed on ' . $log['target_school_name'];
        } elseif ($log['target_user_name']) {
            return 'Action performed on user ' . $log['target_user_name'];
        } elseif ($log['admin_name']) {
            return 'Action performed by ' . $log['admin_name'];
        }

        return 'System action performed';
    }

    /**
     * Get icon for log action
     */
    private function getLogIcon($action)
    {
        $icons = [
            'school_created' => 'fas fa-school',
            'school_updated' => 'fas fa-edit',
            'school_deleted' => 'fas fa-trash',
            'user_created' => 'fas fa-user-plus',
            'user_updated' => 'fas fa-user-edit',
            'user_deleted' => 'fas fa-user-minus',
            'subscription_updated' => 'fas fa-credit-card',
            'system_settings_updated' => 'fas fa-cogs',
            'login' => 'fas fa-sign-in-alt',
            'logout' => 'fas fa-sign-out-alt'
        ];

        return $icons[$action] ?? 'fas fa-info-circle';
    }

    /**
     * Get color for log severity
     */
    private function getLogColor($severity)
    {
        $colors = [
            'low' => 'green',
            'medium' => 'blue',
            'high' => 'yellow',
            'critical' => 'red'
        ];

        return $colors[$severity] ?? 'gray';
    }


}