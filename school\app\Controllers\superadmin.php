<?php

namespace App\Controllers;

use App\Models\SchoolModel;
use App\Models\UserModel;
use App\Models\UserProfileModel;
use App\Models\AuditLogModel;
use App\Models\ActivityLogModel;
use App\Models\SuperAdminLogModel;
use App\Services\AuditLogger;

class SuperAdmin extends BaseController
{
    protected $schoolModel;
    protected $userModel;
    protected $userProfileModel;
    protected $auditLogModel;
    protected $activityLogModel;
    protected $superAdminLogModel;
    protected $auditLogger;

    public function __construct()
    {
        $this->schoolModel = new SchoolModel();
        $this->userModel = new UserModel();
        $this->userProfileModel = new UserProfileModel();
        $this->auditLogModel = new AuditLogModel();
        $this->activityLogModel = new ActivityLogModel();
        $this->superAdminLogModel = new SuperAdminLogModel();
        $this->auditLogger = new AuditLogger();
    }

    public function dashboard()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
            return redirect()->to('/login/superadmin')->with('error', 'Please login first.');
        }

        // Get user statistics
        $totalUsers = $this->userModel->countAllResults();
        $schoolAdmins = $this->userModel->join('schools', 'schools.id = users.school_id')
                                      ->where('users.is_deleted', 0)
                                      ->countAllResults();
        $staffMembers = $this->userModel->join('user_roles', 'user_roles.user_id = users.id')
                                       ->join('roles', 'roles.id = user_roles.role_id')
                                       ->where('roles.name !=', 'Admin')
                                       ->where('users.is_deleted', 0)
                                       ->countAllResults();

        $data = [
            'schools' => $this->schoolModel->findAll(),
            'pendingSchools' => $this->schoolModel->where('status', 'inactive')->findAll(),
            'totalUsers' => $totalUsers,
            'schoolAdmins' => $schoolAdmins,
            'staffMembers' => $staffMembers
        ];

        return view('superadmin/dashboard', $data);
    }

    // In app/Controllers/SuperAdmin.php

public function approveSchool($id)
{
    if (!$this->validate(['id' => 'required|is_natural_no_zero'])) {
        return $this->response->setStatusCode(400)->setJSON([
            'success' => false,
            'message' => 'Invalid school ID'
        ]);
    }

    $school = $this->schoolModel->find($id);
    if (!$school) {
        return $this->response->setJSON(['success' => false, 'message' => 'School not found']);
    }

    $updated = $this->schoolModel->update($id, ['status' => 'active']);

    if ($updated) {
        try {
            $userId = session()->get('user_id');
            log_message('info', "SuperAdmin approval - User ID: " . ($userId ?? 'NULL'));

            // Log the audit action
            $this->auditLogger->logApproval(
                'school',
                $id,
                $school['name'],
                $userId,
                $id,
                "Super admin approved school registration"
            );

            // Log super admin action
            $this->auditLogger->logSuperAdminAction(
                'approve_school',
                $userId,
                "Approved school registration: {$school['name']}",
                $id,
                null,
                ['status' => $school['status']],
                ['status' => 'active'],
                'high'
            );

            log_message('info', "Audit logging completed for school approval");
        } catch (\Exception $e) {
            log_message('error', "Audit logging failed: " . $e->getMessage());
        }
    }

    return $this->response->setJSON([
        'success' => $updated,
        'message' => $updated ? 'School approved successfully' : 'Failed to approve school'
    ]);
}

public function rejectSchool($id)
{
    $reason = $this->request->getPost('reason') ?? $this->request->getJSON(true)['reason'] ?? null;

    if (!$reason) {
        return $this->response->setStatusCode(400)->setJSON([
            'success' => false,
            'message' => 'Reason for rejection is required'
        ]);
    }

    $school = $this->schoolModel->find($id);
    if (!$school) {
        return $this->response->setJSON(['success' => false, 'message' => 'School not found']);
    }

    $updated = $this->schoolModel->update($id, [
        'status' => 'rejected',
        'rejection_reason' => $reason
    ]);

    if ($updated) {
        // Log the audit action
        $this->auditLogger->logRejection(
            'school',
            $id,
            $school['name'],
            $reason,
            session()->get('user_id'),
            $id
        );

        // Log super admin action
        $this->auditLogger->logSuperAdminAction(
            'reject_school',
            session()->get('user_id'),
            "Rejected school registration: {$school['name']} - Reason: {$reason}",
            $id,
            null,
            ['status' => $school['status']],
            ['status' => 'rejected', 'rejection_reason' => $reason],
            'high'
        );
    }

    return $this->response->setJSON([
        'success' => $updated,
        'message' => $updated ? 'School rejected successfully' : 'Failed to reject school'
    ]);
}
public function viewSchoolDetails($id)
{
    if (!session()->get('isSuperAdmin')) {
        return redirect()->to('/login')->with('error', 'Access denied.');
    }

    $school = $this->schoolModel->find($id);

    if (!$school) {
        throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound("School not found");
    }

    return view('superadmin/view_school', ['school' => $school]);
}

    /**
     * Get all users for user management
     */
    public function getUsers()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ]);
        }

        try {
            // Get users with school information and profiles
            $users = $this->userModel
                ->select('users.*, schools.name as school_name, user_profiles.designation, user_profiles.phone')
                ->join('schools', 'schools.id = users.school_id', 'left')
                ->join('user_profiles', 'user_profiles.user_id = users.id', 'left')
                ->where('users.is_deleted', 0)
                ->orderBy('users.created_at', 'DESC')
                ->findAll();

            return $this->response->setJSON([
                'success' => true,
                'users' => $users
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to fetch users: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get user details by ID
     */
    public function getUserDetails($userId)
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ]);
        }

        try {
            $user = $this->userModel
                ->select('users.*, schools.name as school_name, user_profiles.designation, user_profiles.phone')
                ->join('schools', 'schools.id = users.school_id', 'left')
                ->join('user_profiles', 'user_profiles.user_id = users.id', 'left')
                ->where('users.id', $userId)
                ->where('users.is_deleted', 0)
                ->first();

            if (!$user) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'User not found'
                ]);
            }

            return $this->response->setJSON([
                'success' => true,
                'user' => $user
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to fetch user details: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Update user status (activate/deactivate)
     */
    public function toggleUserStatus($userId)
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ]);
        }

        try {
            $user = $this->userModel->find($userId);
            if (!$user) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'User not found'
                ]);
            }

            $newStatus = ($user['status'] === 'active') ? 'inactive' : 'active';
            $updated = $this->userModel->update($userId, ['status' => $newStatus]);

            if ($updated) {
                // Log the audit action
                $this->auditLogger->logUpdate(
                    'user',
                    $userId,
                    $user['name'],
                    ['status' => $user['status']],
                    ['status' => $newStatus],
                    session()->get('user_id'),
                    null,
                    "Super admin changed user status from {$user['status']} to {$newStatus}"
                );

                // Log super admin action
                $this->auditLogger->logSuperAdminAction(
                    'toggle_user_status',
                    session()->get('user_id'),
                    "Changed user status for {$user['name']} from {$user['status']} to {$newStatus}",
                    $user['school_id'],
                    $userId,
                    ['status' => $user['status']],
                    ['status' => $newStatus],
                    'medium'
                );
            }

            return $this->response->setJSON([
                'success' => $updated,
                'message' => $updated ? 'User status updated successfully' : 'Failed to update user status',
                'new_status' => $newStatus
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to update user status: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Delete user (soft delete)
     */
    public function deleteUser($userId)
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ]);
        }

        try {
            $user = $this->userModel->find($userId);
            if (!$user) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'User not found'
                ]);
            }

            // Soft delete the user
            $updated = $this->userModel->update($userId, [
                'is_deleted' => 1,
                'deleted_at' => date('Y-m-d H:i:s')
            ]);

            if ($updated) {
                // Log the audit action
                $this->auditLogger->logDelete(
                    'user',
                    $userId,
                    $user['name'],
                    $user,
                    session()->get('user_id'),
                    $user['school_id'],
                    "Super admin deleted user: {$user['name']}"
                );

                // Log super admin action
                $this->auditLogger->logSuperAdminAction(
                    'delete_user',
                    session()->get('user_id'),
                    "Deleted user: {$user['name']} ({$user['email']})",
                    $user['school_id'],
                    $userId,
                    $user,
                    null,
                    'high'
                );
            }

            return $this->response->setJSON([
                'success' => $updated,
                'message' => $updated ? 'User deleted successfully' : 'Failed to delete user'
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to delete user: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get audit logs with filters and pagination
     */
    public function getAuditLogs()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ]);
        }

        try {
            $request = $this->request;
            $page = (int)($request->getGet('page') ?? 1);
            $perPage = (int)($request->getGet('per_page') ?? 50);

            $filters = [
                'school_id' => $request->getGet('school_id'),
                'user_id' => $request->getGet('user_id'),
                'action' => $request->getGet('action'),
                'entity_type' => $request->getGet('entity_type'),
                'severity' => $request->getGet('severity'),
                'status' => $request->getGet('status'),
                'date_from' => $request->getGet('date_from'),
                'date_to' => $request->getGet('date_to'),
                'search' => $request->getGet('search')
            ];

            // Remove empty filters
            $filters = array_filter($filters, function($value) {
                return $value !== null && $value !== '';
            });

            $result = $this->auditLogModel->getAuditLogs($filters, $page, $perPage);

            return $this->response->setJSON([
                'success' => true,
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to fetch audit logs: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get audit log statistics
     */
    public function getAuditStats()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ]);
        }

        try {
            $request = $this->request;
            $schoolId = $request->getGet('school_id');
            $dateFrom = $request->getGet('date_from');
            $dateTo = $request->getGet('date_to');

            $stats = $this->auditLogModel->getAuditStats($schoolId, $dateFrom, $dateTo);

            return $this->response->setJSON([
                'success' => true,
                'stats' => $stats
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to fetch audit statistics: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get super admin logs
     */
    public function getSuperAdminLogs()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ]);
        }

        try {
            $request = $this->request;
            $page = (int)($request->getGet('page') ?? 1);
            $perPage = (int)($request->getGet('per_page') ?? 50);

            $filters = [
                'admin_user_id' => $request->getGet('admin_user_id'),
                'action' => $request->getGet('action'),
                'target_school_id' => $request->getGet('target_school_id'),
                'severity' => $request->getGet('severity'),
                'date_from' => $request->getGet('date_from'),
                'date_to' => $request->getGet('date_to'),
                'search' => $request->getGet('search')
            ];

            // Remove empty filters
            $filters = array_filter($filters, function($value) {
                return $value !== null && $value !== '';
            });

            $result = $this->superAdminLogModel->getSuperAdminLogs($filters, $page, $perPage);

            return $this->response->setJSON([
                'success' => true,
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to fetch super admin logs: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Export audit logs to CSV
     */
    public function exportAuditLogs()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
            return redirect()->to('/login/superadmin')->with('error', 'Access denied');
        }

        try {
            $request = $this->request;

            $filters = [
                'school_id' => $request->getGet('school_id'),
                'user_id' => $request->getGet('user_id'),
                'action' => $request->getGet('action'),
                'entity_type' => $request->getGet('entity_type'),
                'severity' => $request->getGet('severity'),
                'status' => $request->getGet('status'),
                'date_from' => $request->getGet('date_from'),
                'date_to' => $request->getGet('date_to'),
                'search' => $request->getGet('search')
            ];

            // Remove empty filters
            $filters = array_filter($filters, function($value) {
                return $value !== null && $value !== '';
            });

            // Get all logs (no pagination for export)
            $result = $this->auditLogModel->getAuditLogs($filters, 1, 10000);
            $logs = $result['logs'];

            // Set headers for CSV download
            $filename = 'audit_logs_' . date('Y-m-d_H-i-s') . '.csv';
            header('Content-Type: text/csv');
            header('Content-Disposition: attachment; filename="' . $filename . '"');

            // Create file pointer
            $output = fopen('php://output', 'w');

            // Add CSV headers
            fputcsv($output, [
                'ID', 'Date/Time', 'School', 'User', 'Action', 'Entity Type',
                'Entity Name', 'Description', 'Severity', 'Status', 'IP Address'
            ]);

            // Add data rows
            foreach ($logs as $log) {
                fputcsv($output, [
                    $log['id'],
                    $log['created_at'],
                    $log['school_name'] ?? 'N/A',
                    $log['user_name'] ?? 'System',
                    $log['action'],
                    $log['entity_type'],
                    $log['entity_name'] ?? 'N/A',
                    $log['description'],
                    $log['severity'],
                    $log['status'],
                    $log['ip_address'] ?? 'N/A'
                ]);
            }

            fclose($output);
            exit;
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to export audit logs: ' . $e->getMessage());
        }
    }

    /**
     * Get filter options for audit logs
     */
    public function getAuditFilterOptions()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ]);
        }

        try {
            $actions = $this->auditLogModel->getDistinctActions();
            $entityTypes = $this->auditLogModel->getDistinctEntityTypes();
            $schools = $this->schoolModel->select('id, name')->findAll();

            return $this->response->setJSON([
                'success' => true,
                'options' => [
                    'actions' => $actions,
                    'entity_types' => $entityTypes,
                    'schools' => $schools,
                    'severities' => ['low', 'medium', 'high', 'critical'],
                    'statuses' => ['success', 'failed', 'warning']
                ]
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to fetch filter options: ' . $e->getMessage()
            ]);
        }
    }


}