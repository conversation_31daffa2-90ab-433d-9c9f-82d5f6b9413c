<?php

namespace App\Controllers;

use App\Models\SchoolModel;

class SuperAdmin extends BaseController
{
    protected $schoolModel;

    public function __construct()
    {
        $this->schoolModel = new SchoolModel();
    }

    public function dashboard()
    {
    if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
        return redirect()->to('/login/superadmin')->with('error', 'Please login first.');
    }
      $data = [
            'schools' => $this->schoolModel->findAll(),
            'pendingSchools' => $this->schoolModel->where('status', 'inactive')->findAll()
        ];

        return view('superadmin/dashboard', $data);
    }

    // In app/Controllers/SuperAdmin.php

public function approveSchool($id)
{
    if (!$this->validate(['id' => 'required|is_natural_no_zero'])) {
        return $this->response->setStatusCode(400)->setJSON([
            'success' => false, 
            'message' => 'Invalid school ID'
        ]);
    }

    $updated = $this->schoolModel->update($id, ['status' => 'active']);
    
    return $this->response->setJSON([
        'success' => $updated,
        'message' => $updated ? 'School approved successfully' : 'Failed to approve school'
    ]);
}

public function rejectSchool($id)
{
    $reason = $this->request->getPost('reason') ?? $this->request->getJSON(true)['reason'] ?? null;
    
    if (!$reason) {
        return $this->response->setStatusCode(400)->setJSON([
            'success' => false,
            'message' => 'Reason for rejection is required'
        ]);
    }
    
    $updated = $this->schoolModel->update($id, [
        'status' => 'rejected',
        'rejection_reason' => $reason
    ]);
    
    return $this->response->setJSON([
        'success' => $updated,
        'message' => $updated ? 'School rejected successfully' : 'Failed to reject school'
    ]);
}
public function viewSchoolDetails($id)
{
    if (!session()->get('isSuperAdmin')) {
        return redirect()->to('/login')->with('error', 'Access denied.');
    }

    $school = $this->schoolModel->find($id);

    if (!$school) {
        throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound("School not found");
    }

    return view('superadmin/view_school', ['school' => $school]);
}


}