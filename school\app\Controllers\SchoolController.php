<?php
namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\SchoolModel;

class SchoolController extends BaseController
{
    public function showRegisterForm()
    {
        return view('school_register');
    }

    public function register()
{
    $validation = \Config\Services::validation();
    $validation->setRules([
        'name' => 'required|min_length[3]',
        'email' => 'required|valid_email|is_unique[schools.email]',
        'password' => 'required|min_length[8]',
        'plan_id' => 'required|numeric',
        'address' => 'required|min_length[10]', // Ensure address is required
        'phone' => 'required|min_length[10]'
    ]);

    if (!$validation->withRequest($this->request)->run()) {
        return redirect()->back()
            ->withInput()
            ->with('errors', $validation->getErrors());
    }

        $model = new SchoolModel();

        // Check if email already exists (additional check)
        if ($model->where('email', $this->request->getPost('email'))->first()) {
            return redirect()->back()->withInput()->with('error', 'Email address is already registered');
        }

        try {
            // Get plan details
            $planId = $this->request->getPost('plan_id');
            $planModel = new \App\Models\PlanModel();
            $plan = $planModel->find($planId);

            if (!$plan) {
                return redirect()->back()->withInput()->with('error', 'Invalid plan selected');
            }

            $data = [
                'name'     => $this->request->getPost('name'),
                'email'    => $this->request->getPost('email'),
                'password' => password_hash($this->request->getPost('password'), PASSWORD_DEFAULT),
                'plan_id'  => $planId,
                'address'  => $this->request->getPost('address'),
                'phone'    => $this->request->getPost('phone'),
                'status'   => 'inactive',
            ];

            $schoolId = $model->insert($data);

            if ($schoolId) {
                // Create subscription based on plan
                $subscriptionService = new \App\Services\SubscriptionService();

                if ($plan['name'] === 'free') {
                    // Free plan - create trial subscription
                    $subscriptionService->createSchoolSubscription($schoolId, 'free', 'monthly', true);
                    $message = 'Registration successful! Your free trial is ready. Please wait for admin approval.';
                } else {
                    // Paid plan - create pending subscription
                    $subscriptionService->createSchoolSubscription($schoolId, $plan['name'], 'monthly', false);
                    $message = 'Registration successful! Please complete payment after admin approval.';
                }

                return redirect()->to('/register')->with('success', $message);
            } else {
                return redirect()->back()->withInput()->with('error', 'Registration failed. Please try again.');
            }
            
        } catch (\Exception $e) {
            // Catch database exceptions
            log_message('error', 'Registration error: ' . $e->getMessage());
            
            // Check for duplicate email error specifically
            if (strpos($e->getMessage(), 'Duplicate entry') !== false && strpos($e->getMessage(), 'email') !== false) {
                return redirect()->back()->withInput()->with('error', 'Email address is already registered');
            }
            
            return redirect()->back()->withInput()->with('error', 'Registration failed. Please try again.');
        }
    }

    // ... rest of your existing methods ...

    // Add these methods to your SchoolController
public function pending()
{
    $schoolModel = new SchoolModel();
    $pendingSchools = $schoolModel->getPendingSchools();
    
    return $this->response->setJSON([
        'success' => true,
        'schools' => $pendingSchools
    ]);
}
public function all()
{
    $schoolModel = new SchoolModel();
    $schools = $schoolModel->orderBy('created_at', 'DESC')->findAll();

    return $this->response->setJSON([
        'success' => true,
        'schools' => $schools
    ]);
}

public function active()
{
    $schoolModel = new SchoolModel();
    $activeSchools = $schoolModel->where('status', 'active')->orderBy('created_at', 'DESC')->findAll();

    return $this->response->setJSON([
        'success' => true,
        'schools' => $activeSchools
    ]);
}
public function approve($id)
{
    $schoolModel = new SchoolModel();
    
    if ($schoolModel->approveSchool($id)) {
        return $this->response->setJSON(['success' => true]);
    }
    
    return $this->response->setJSON(['success' => false, 'message' => 'Failed to approve school']);
}
public function rejectSchool($id)
{
    $reason = $this->request->getPost('reason') ?? $this->request->getJSON(true)['reason'] ?? null;

    if (!$reason) {
        return $this->response->setStatusCode(400)->setJSON([
            'success' => false,
            'message' => 'Reason for rejection is required'
        ]);
    }

    $updated = $this->schoolModel->update($id, [
        'status' => 'rejected',
        'rejection_reason' => $reason
    ]);

    return $this->response->setJSON([
        'success' => $updated,
        'message' => $updated ? 'School rejected successfully' : 'Failed to reject school'
    ]);
}

// In SchoolController.php
public function checkEmail()
{
    $email = $this->request->getJSON()->email;
    $model = new SchoolModel();
    $exists = $model->where('email', $email)->first() !== null;
    
    return $this->response->setJSON(['exists' => $exists]);
}
}
