<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>QuestionBank Pro - School Registration</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    .step-slide { display: none; }
    .step-slide.active { display: block; animation: fadeIn 0.3s ease-in-out; }
    @keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }
    .otp-input { width: 40px; height: 40px; text-align: center; font-size: 18px; }
    .progress-step { width: 30px; height: 30px; }
    .input-error { border-color: #f87171 !important; }
    .error-message { color: #f87171; font-size: 0.875rem; margin-top: 0.25rem; }
    .success-message { color: #10b981; font-size: 0.875rem; margin-top: 0.25rem; }
  </style>
</head>
<body class="bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 min-h-screen flex items-center justify-center p-4">
  <div class="w-full max-w-2xl bg-white rounded-xl shadow-2xl overflow-hidden">
    <div class="bg-gradient-to-r from-indigo-600 to-purple-600 px-6 py-5 text-center">
      <h1 class="text-3xl font-bold text-white">QuestionBank Pro</h1>
      <p class="text-indigo-100 mt-1">Register your school account</p>
    </div>

    <!-- Progress Steps -->
    <div class="flex justify-center py-4 bg-gray-50">
      <div class="flex items-center">
        <div class="flex flex-col items-center mx-4">
          <div id="step1-indicator" class="progress-step rounded-full bg-indigo-600 text-white flex items-center justify-center">
            <i class="fas fa-check"></i>
          </div>
          <span class="text-xs mt-1 font-medium">School Info</span>
        </div>
        <div class="h-1 w-16 bg-gray-300 rounded-full">
          <div id="step1-progress" class="h-full bg-indigo-600 rounded-full w-0"></div>
        </div>
        <div class="flex flex-col items-center mx-4">
          <div id="step2-indicator" class="progress-step rounded-full bg-gray-300 text-gray-600 flex items-center justify-center">
            <span>2</span>
          </div>
          <span class="text-xs mt-1 font-medium text-gray-500">Plan Selection</span>
        </div>
        <div class="h-1 w-16 bg-gray-300 rounded-full">
          <div id="step2-progress" class="h-full bg-gray-300 rounded-full w-0"></div>
        </div>
        <div class="flex flex-col items-center mx-4">
          <div id="step3-indicator" class="progress-step rounded-full bg-gray-300 text-gray-600 flex items-center justify-center">
            <span>3</span>
          </div>
          <span class="text-xs mt-1 font-medium text-gray-500">Password</span>
        </div>
      </div>
    </div>

    <div class="px-8 py-6">
      <?php if (session()->getFlashdata('success')): ?>
        <div class="mb-4 p-3 bg-green-100 text-green-700 border border-green-300 rounded-lg flex items-center">
          <i class="fas fa-check-circle mr-2"></i>
          <?= session()->getFlashdata('success') ?>
        </div>
      <?php endif; ?>

      <form id="registrationForm" method="post" action="<?= base_url('school/register') ?>" class="space-y-5">
        <!-- Step 1: School Information -->
        <div id="step-1" class="step-slide active">
          <div>
            <label for="schoolName" class="block text-sm font-medium text-gray-700 mb-1">School Name <span class="text-red-500">*</span></label>
            <input type="text" id="schoolName" name="name" placeholder="Enter your school name" 
                   class="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition"
                   value="<?= old('name') ?>">
            <div id="schoolName-error" class="error-message hidden">Please enter your school name</div>
          <?php if (session()->getFlashdata('errors') && array_key_exists('name', session()->getFlashdata('errors'))): ?>
              <div class="error-message"><?= session()->getFlashdata('errors')['name'] ?></div>
            <?php endif; ?>
          </div>

          <div>
            <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address <span class="text-red-500">*</span></label>
            <div class="relative">
              <input type="email" id="email" name="email" placeholder="<EMAIL>" 
                     class="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition pr-24"
                     value="<?= old('email') ?>">
              <button type="button" id="sendOtpBtn" 
                      class="absolute right-2 top-2 px-3 py-1.5 bg-indigo-600 text-white text-sm rounded-md hover:bg-indigo-700 transition">
                Send OTP
              </button>
            </div>
            <div id="email-error" class="error-message hidden">Please enter a valid email address</div>
            <?php if (session()->getFlashdata('errors') && array_key_exists('email', session()->getFlashdata('errors'))): ?>
              <div class="error-message"><?= session()->getFlashdata('errors')['email'] ?></div>
            <?php endif; ?>
            <div id="otpSuccess" class="success-message hidden"></div>
          </div>

          <div id="otpContainer" class="hidden mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
            <p class="text-sm text-gray-600 mb-3 flex items-center">
              <i class="fas fa-envelope mr-2"></i> Enter the 6-digit OTP sent to your email:
            </p>
            <div class="flex justify-center space-x-3">
              <?php for ($i = 0; $i < 6; $i++): ?>
                <input type="text" name="otp[]" maxlength="1" 
                       class="otp-input border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition">
              <?php endfor; ?>
            </div>
            <div id="otp-error" class="error-message hidden text-center mt-2">Please enter the complete 6-digit OTP</div>
          </div>

          <div id="verifyOtpSection" class="hidden mt-4 flex justify-center">
            <button type="button" id="verifyOtpBtn" 
                    class="px-5 py-2.5 bg-green-600 text-white rounded-lg hover:bg-green-700 transition flex items-center">
              <i class="fas fa-check-circle mr-2"></i> Verify OTP
            </button>
          </div>

          <div class="mt-6 flex justify-end">
            <button type="button" id="step1-next" 
                    class="px-6 py-2.5 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled>
              Next <i class="fas fa-arrow-right ml-2"></i>
            </button>
          </div>
        </div>

        <!-- Step 2: Plan Selection -->
        <div id="step-2" class="step-slide">
          <div>
            <label for="plan" class="block text-sm font-medium text-gray-700 mb-1">Select Plan <span class="text-red-500">*</span></label>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
              <div class="plan-option border rounded-lg p-4 cursor-pointer hover:border-indigo-400 transition" data-value="1" data-plan="free">
                <h3 class="font-bold text-lg text-gray-700">Free Trial</h3>
                <p class="text-gray-600 text-sm mt-1">30 days free trial</p>
                <p class="text-2xl font-bold mt-2">₹0<span class="text-sm font-normal text-gray-500">/month</span></p>
                <ul class="text-sm text-gray-600 mt-2 space-y-1">
                  <li class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i> Up to 50 questions</li>
                  <li class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i> 5 subjects maximum</li>
                  <li class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i> 5 users maximum</li>
                  <li class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i> Email support</li>
                </ul>
              </div>
              <div class="plan-option border-2 border-indigo-500 bg-indigo-50 rounded-lg p-4 cursor-pointer hover:border-indigo-600 transition relative" data-value="2" data-plan="professional">
                <div class="absolute -top-2 left-1/2 transform -translate-x-1/2">
                  <span class="bg-indigo-500 text-white px-2 py-1 rounded-full text-xs font-semibold">Most Popular</span>
                </div>
                <h3 class="font-bold text-lg text-indigo-700">Professional</h3>
                <p class="text-gray-600 text-sm mt-1">Full features</p>
                <p class="text-2xl font-bold mt-2">₹999<span class="text-sm font-normal text-gray-500">/month</span></p>
                <ul class="text-sm text-gray-600 mt-2 space-y-1">
                  <li class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i> Unlimited questions</li>
                  <li class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i> Unlimited subjects</li>
                  <li class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i> Up to 50 users</li>
                  <li class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i> Export & Print</li>
                  <li class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i> Priority support</li>
                </ul>
              </div>
            </div>
            <input type="hidden" id="plan" name="plan_id" value="<?= old('plan_id') ?>">
            <div id="plan-error" class="error-message hidden">Please select a plan</div>
            <?php if (session()->getFlashdata('errors') && array_key_exists('plan_id', session()->getFlashdata('errors'))): ?>
              <div class="error-message"><?= session()->getFlashdata('errors')['plan_id'] ?></div>
            <?php endif; ?>
          </div>

          <div class="mt-6 flex justify-between">
            <button type="button" onclick="prevStep(2)" 
                    class="px-6 py-2.5 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition flex items-center">
              <i class="fas fa-arrow-left mr-2"></i> Previous
            </button>
            <button type="button" id="step2-next" 
                    class="px-6 py-2.5 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled>
              Next <i class="fas fa-arrow-right ml-2"></i>
            </button>
          </div>
        </div>

        <!-- Step 3: Password Setup -->
        <div id="step-3" class="step-slide">
          <div>
            <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Password <span class="text-red-500">*</span></label>
            <div class="relative">
              <input type="password" id="password" name="password" placeholder="Create a password" 
                     class="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition">
              <i class="fas fa-eye-slash absolute right-3 top-3.5 text-gray-400 cursor-pointer" id="togglePassword"></i>
            </div>
            <div class="text-xs text-gray-500 mt-1">
              <i class="fas fa-info-circle mr-1"></i> Minimum 8 characters with at least 1 number and 1 special character
            </div>
            <div id="password-error" class="error-message hidden"></div>
            <?php if (session()->getFlashdata('errors') && array_key_exists('password', session()->getFlashdata('errors'))): ?>
              <div class="error-message"><?= session()->getFlashdata('errors')['password'] ?></div>
            <?php endif; ?>
          </div>

          <div>
            <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-1">Confirm Password <span class="text-red-500">*</span></label>
            <div class="relative">
              <input type="password" id="confirmPassword" name="confirm_password" placeholder="Confirm password" 
                     class="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition">
              <i class="fas fa-eye-slash absolute right-3 top-3.5 text-gray-400 cursor-pointer" id="toggleConfirmPassword"></i>
            </div>
            <div id="confirmPassword-error" class="error-message hidden">Passwords do not match</div>
          </div>

          <div>
            <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Phone Number <span class="text-red-500">*</span></label>
            <input type="tel" id="phone" name="phone" placeholder="+91 98765 43210" 
                   class="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition"
                   value="<?= old('phone') ?>">
            <div id="phone-error" class="error-message hidden">Please enter a valid phone number</div>
            <?php if (session()->getFlashdata('errors') && array_key_exists('phone', session()->getFlashdata('errors'))): ?>
              <div class="error-message"><?= session()->getFlashdata('errors')['phone'] ?></div>
            <?php endif; ?>
          </div>

          <div>
            <label for="address" class="block text-sm font-medium text-gray-700 mb-1">School Address <span class="text-red-500">*</span></label>
            <textarea id="address" name="address" rows="3" placeholder="Enter full school address" 
                      class="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition"><?= old('address') ?></textarea>
            <div id="address-error" class="error-message hidden">Please enter your school address</div>
            <?php if (session()->getFlashdata('errors') && array_key_exists('address', session()->getFlashdata('errors'))): ?>
              <div class="error-message"><?= session()->getFlashdata('errors')['address'] ?></div>
            <?php endif; ?>
          </div>

          <?php if (session()->getFlashdata('error')): ?>
            <div class="mb-4 p-3 bg-red-100 text-red-700 border border-red-300 rounded-lg flex items-center">
              <i class="fas fa-exclamation-circle mr-2"></i>
              <?= session()->getFlashdata('error') ?>
            </div>
          <?php endif; ?>

          <div class="mt-6 flex justify-between">
            <button type="button" onclick="prevStep(3)" 
                    class="px-6 py-2.5 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition flex items-center">
              <i class="fas fa-arrow-left mr-2"></i> Previous
            </button>
            <button type="submit" id="submitBtn" 
                    class="px-6 py-2.5 bg-green-600 text-white rounded-lg hover:bg-green-700 transition flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled>
              <i class="fas fa-check-circle mr-2"></i> Register School
            </button>
          </div>
        </div>

        <div class="text-center text-sm text-gray-600 mt-6 pt-4 border-t border-gray-200">
          Already have an account? <a href="<?= base_url('login') ?>" class="text-indigo-600 font-medium hover:underline">Sign in here</a>
        </div>
      </form>
    </div>
  </div>

  <script>
    let currentStep = 1;
    let isOtpVerified = false;
    let otpSent = false;

    // Initialize UI
    document.addEventListener('DOMContentLoaded', function() {
      updateProgressIndicator();
      setupEventListeners();

      // Handle plan pre-selection from landing page
      const urlParams = new URLSearchParams(window.location.search);
      const planParam = urlParams.get('plan');
      const selectedPlan = planParam || sessionStorage.getItem('selectedPlan') || 'free';

      // Pre-select the plan if specified
      if (selectedPlan) {
        const planOptions = document.querySelectorAll('.plan-option');
        planOptions.forEach(option => {
          const planValue = option.getAttribute('data-plan');
          if (planValue === selectedPlan) {
            option.click();
          }
        });

        // Clear session storage
        sessionStorage.removeItem('selectedPlan');
      }

      // Check if there are server-side errors and show the relevant step
      <?php if (session()->getFlashdata('errors')): ?>
        const errors = <?= json_encode(session()->getFlashdata('errors')) ?>;
        if (errors.plan_id) {
          showStep(2);
        } else if (errors.password || errors.phone || errors.address) {
          showStep(3);
        }
      <?php endif; ?>
    });

    function showStep(step) {
      document.querySelectorAll('.step-slide').forEach(div => div.classList.remove('active'));
      document.getElementById(`step-${step}`).classList.add('active');
      currentStep = step;
      updateProgressIndicator();
    }

    function nextStep(step) {
      if (validateStep(step)) {
        showStep(step + 1);
      }
    }

    function prevStep(step) {
      showStep(step - 1);
    }

    function updateProgressIndicator() {
      // Update step indicators
      for (let i = 1; i <= 3; i++) {
        const indicator = document.getElementById(`step${i}-indicator`);
        const progress = document.getElementById(`step${i}-progress`);
        
        if (i < currentStep) {
          indicator.innerHTML = '<i class="fas fa-check"></i>';
          indicator.className = 'progress-step rounded-full bg-indigo-600 text-white flex items-center justify-center';
          if (progress) progress.className = 'h-full bg-indigo-600 rounded-full w-full';
        } else if (i === currentStep) {
          indicator.innerHTML = i;
          indicator.className = 'progress-step rounded-full bg-indigo-100 text-indigo-600 border-2 border-indigo-600 flex items-center justify-center';
          if (progress) progress.className = 'h-full bg-indigo-600 rounded-full w-0';
        } else {
          indicator.innerHTML = i;
          indicator.className = 'progress-step rounded-full bg-gray-300 text-gray-600 flex items-center justify-center';
          if (progress) progress.className = 'h-full bg-gray-300 rounded-full w-0';
        }
      }
      
      // Special case for progress between steps
      if (currentStep === 2) {
        document.getElementById('step1-progress').className = 'h-full bg-indigo-600 rounded-full w-full';
      } else if (currentStep === 3) {
        document.getElementById('step1-progress').className = 'h-full bg-indigo-600 rounded-full w-full';
        document.getElementById('step2-progress').className = 'h-full bg-indigo-600 rounded-full w-full';
      }
    }

    function validateStep(step) {
      let isValid = true;
      
      if (step === 1) {
        const schoolName = document.getElementById('schoolName').value.trim();
        const email = document.getElementById('email').value.trim();
        
        // Validate school name
        if (schoolName === '') {
          document.getElementById('schoolName').classList.add('input-error');
          document.getElementById('schoolName-error').classList.remove('hidden');
          isValid = false;
        } else {
          document.getElementById('schoolName').classList.remove('input-error');
          document.getElementById('schoolName-error').classList.add('hidden');
        }
        
        // Validate email
        if (email === '' || !email.includes('@') || !email.includes('.')) {
          document.getElementById('email').classList.add('input-error');
          document.getElementById('email-error').classList.remove('hidden');
          isValid = false;
        } else {
          document.getElementById('email').classList.remove('input-error');
          document.getElementById('email-error').classList.add('hidden');
        }
        
        // Validate OTP if sent but not verified
        if (otpSent && !isOtpVerified) {
          alert('Please verify your OTP before proceeding.');
          isValid = false;
        }
        
        return isValid;
      }
      else if (step === 2) {
        const plan = document.getElementById('plan').value;
        
        // Validate plan selection
        if (plan === '') {
          document.getElementById('plan-error').classList.remove('hidden');
          isValid = false;
        } else {
          document.getElementById('plan-error').classList.add('hidden');
        }
        
        return isValid;
      }
      else if (step === 3) {
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        const phone = document.getElementById('phone').value.trim();
        const address = document.getElementById('address').value.trim();
        
        // Validate password
        if (password.length < 8 || !/\d/.test(password) || !/[!@#$%^&*]/.test(password)) {
          document.getElementById('password').classList.add('input-error');
          document.getElementById('password-error').textContent = 'Password must be at least 8 characters with 1 number and 1 special character';
          document.getElementById('password-error').classList.remove('hidden');
          isValid = false;
        } else {
          document.getElementById('password').classList.remove('input-error');
          document.getElementById('password-error').classList.add('hidden');
        }
        
        // Validate password match
        if (password !== confirmPassword) {
          document.getElementById('confirmPassword').classList.add('input-error');
          document.getElementById('confirmPassword-error').classList.remove('hidden');
          isValid = false;
        } else {
          document.getElementById('confirmPassword').classList.remove('input-error');
          document.getElementById('confirmPassword-error').classList.add('hidden');
        }
        
        // Validate phone
        if (phone === '') {
          document.getElementById('phone').classList.add('input-error');
          document.getElementById('phone-error').classList.remove('hidden');
          isValid = false;
        } else {
          document.getElementById('phone').classList.remove('input-error');
          document.getElementById('phone-error').classList.add('hidden');
        }
        
        // Validate address
        if (address === '') {
          document.getElementById('address').classList.add('input-error');
          document.getElementById('address-error').classList.remove('hidden');
          isValid = false;
        } else {
          document.getElementById('address').classList.remove('input-error');
          document.getElementById('address-error').classList.add('hidden');
        }
        
        return isValid;
      }
      
      return true;
    }

    function setupEventListeners() {
      // Step 1 validation
      document.getElementById('schoolName').addEventListener('input', checkStep1Validity);
      document.getElementById('email').addEventListener('input', checkStep1Validity);
      
      // Step 2 validation
      document.getElementById('plan').addEventListener('change', checkStep2Validity);
      
      // Step 3 validation
      document.getElementById('password').addEventListener('input', checkStep3Validity);
      document.getElementById('confirmPassword').addEventListener('input', checkStep3Validity);
      document.getElementById('phone').addEventListener('input', checkStep3Validity);
      document.getElementById('address').addEventListener('input', checkStep3Validity);
      
      // Plan selection
      document.querySelectorAll('.plan-option').forEach(option => {
        option.addEventListener('click', function() {
          document.querySelectorAll('.plan-option').forEach(opt => {
            opt.classList.remove('border-indigo-500', 'bg-indigo-50');
          });
          this.classList.add('border-indigo-500', 'bg-indigo-50');
          document.getElementById('plan').value = this.getAttribute('data-value');
          checkStep2Validity();
        });
      });
      
      // Password visibility toggle
      document.getElementById('togglePassword').addEventListener('click', function() {
        const passwordInput = document.getElementById('password');
        const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordInput.setAttribute('type', type);
        this.classList.toggle('fa-eye-slash');
        this.classList.toggle('fa-eye');
      });
      
      document.getElementById('toggleConfirmPassword').addEventListener('click', function() {
        const confirmInput = document.getElementById('confirmPassword');
        const type = confirmInput.getAttribute('type') === 'password' ? 'text' : 'password';
        confirmInput.setAttribute('type', type);
        this.classList.toggle('fa-eye-slash');
        this.classList.toggle('fa-eye');
      });
      
      // OTP input handling - Auto-tab functionality
      document.querySelectorAll('input[name="otp[]"]').forEach((input, index, all) => {
        input.addEventListener('input', (e) => {
          if (e.target.value.length === 1 && index < all.length - 1) {
            all[index + 1].focus();
          }
        });
        
        input.addEventListener('keydown', (e) => {
          if (e.key === 'Backspace' && e.target.value.length === 0 && index > 0) {
            all[index - 1].focus();
          }
        });
      });

      // Send OTP Button
      document.getElementById('sendOtpBtn').addEventListener('click', function () {
        const email = document.getElementById('email').value;
        
        // Clear previous messages
        document.getElementById('email-error').classList.add('hidden');
        document.getElementById('otpSuccess').classList.add('hidden');
        
        // Basic email validation
        if (!email.includes('@') || !email.includes('.')) {
            document.getElementById('email-error').textContent = 'Please enter a valid email address';
            document.getElementById('email-error').classList.remove('hidden');
            return;
        }

        const btn = this;
        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Checking...';

        // First check if email exists
        fetch('<?= base_url('school/check-email') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({ email: email })
        })
        .then(response => response.json())
        .then(data => {
            if (data.exists) {
                // Email already registered
                btn.disabled = false;
                btn.innerHTML = 'Send OTP';
                document.getElementById('email-error').textContent = 'This email is already registered';
                document.getElementById('email-error').classList.remove('hidden');
                document.getElementById('email').classList.add('input-error');
            } else {
                // Email not registered, proceed with OTP
                btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Sending...';
                
                fetch('<?= base_url('register/send-otp') ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({ email: email })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        btn.classList.add('hidden');
                        document.getElementById('otpContainer').classList.remove('hidden');
                        document.getElementById('verifyOtpSection').classList.remove('hidden');
                        document.getElementById('otpSuccess').innerHTML = '<i class="fas fa-check-circle mr-1"></i> OTP sent successfully!';
                        document.getElementById('otpSuccess').classList.remove('hidden');
                        otpSent = true;
                        checkStep1Validity();
                        
                        // Auto-fill OTP for development (remove in production)
                        if (data.debug_otp) {
                            const otpInputs = document.querySelectorAll('input[name="otp[]"]');
                            const otpDigits = data.debug_otp.split('');
                            otpInputs.forEach((input, index) => {
                                input.value = otpDigits[index] || '';
                            });
                        }
                    } else {
                        btn.disabled = false;
                        btn.innerHTML = 'Send OTP';
                        alert(data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    btn.disabled = false;
                    btn.innerHTML = 'Send OTP';
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            btn.disabled = false;
            btn.innerHTML = 'Send OTP';
        });
      });

      // Verify OTP Button
      document.getElementById('verifyOtpBtn').addEventListener('click', function () {
        const email = document.getElementById('email').value;
        const otpInputs = document.querySelectorAll('input[name="otp[]"]');
        let enteredOtp = '';
        otpInputs.forEach(input => enteredOtp += input.value);

        if (enteredOtp.length !== 6) {
            document.getElementById('otp-error').textContent = 'Please enter complete 6-digit OTP';
            document.getElementById('otp-error').classList.remove('hidden');
            return;
        }

        const btn = this;
        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Verifying...';
        
        // Clear any previous error
        document.getElementById('otp-error').classList.add('hidden');
        
        fetch('<?= base_url('register/verify-otp') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({ email: email, otp: enteredOtp })
        })
        .then(res => res.json())
        .then(data => {
            if (data.status === 'success') {
                isOtpVerified = true;
                btn.innerHTML = '<i class="fas fa-check-circle mr-1"></i> Verified';
                
                // Update success message
                const otpSuccess = document.getElementById('otpSuccess');
                otpSuccess.innerHTML = '<i class="fas fa-check-circle mr-1"></i> OTP verified successfully!';
                otpSuccess.classList.remove('hidden');
                
                // Lock the email field after verification
                const emailInput = document.getElementById('email');
                emailInput.readOnly = true;
                emailInput.classList.add('bg-gray-100');
                
                // Hide OTP-related elements
                document.getElementById('otpContainer').classList.add('hidden');
                document.getElementById('verifyOtpSection').classList.add('hidden');
                
                // Enable next button
                checkStep1Validity();
            } else {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-check-circle mr-1"></i> Verify OTP';
                document.getElementById('otp-error').textContent = data.message || 'Invalid OTP code';
                document.getElementById('otp-error').classList.remove('hidden');
                otpInputs.forEach(input => input.value = '');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-check-circle mr-1"></i> Verify OTP';
            alert('Failed to verify OTP. Please try again.');
        });
      });
      
      // Next button for step 1
      document.getElementById('step1-next').addEventListener('click', function() {
        nextStep(1);
      });
      
      // Next button for step 2
      document.getElementById('step2-next').addEventListener('click', function() {
        nextStep(2);
      });
      
      // Email blur check
      document.getElementById('email').addEventListener('blur', function() {
          const email = this.value.trim();
          if (email.includes('@') && email.includes('.')) {
              fetch('<?= base_url('school/check-email') ?>', {
                  method: 'POST',
                  headers: {
                      'Content-Type': 'application/json',
                      'X-Requested-With': 'XMLHttpRequest'
                  },
                  body: JSON.stringify({ email: email })
              })
              .then(response => response.json())
              .then(data => {
                  if (data.exists) {
                      document.getElementById('email-error').textContent = 'Email is already registered';
                      document.getElementById('email-error').classList.remove('hidden');
                      document.getElementById('email').classList.add('input-error');
                      document.getElementById('sendOtpBtn').disabled = true;
                  }
              });
          }
      });
    }

    function checkStep1Validity() {
      const schoolName = document.getElementById('schoolName').value.trim();
      const email = document.getElementById('email').value.trim();
      const nextBtn = document.getElementById('step1-next');
      
      if (schoolName !== '' && email !== '' && email.includes('@') && email.includes('.') && (otpSent ? isOtpVerified : true)) {
        nextBtn.disabled = false;
      } else {
        nextBtn.disabled = true;
      }
    }

    function checkStep2Validity() {
      const plan = document.getElementById('plan').value;
      const nextBtn = document.getElementById('step2-next');
      
      if (plan !== '') {
        nextBtn.disabled = false;
      } else {
        nextBtn.disabled = true;
      }
    }

    function checkStep3Validity() {
      const password = document.getElementById('password').value;
      const confirmPassword = document.getElementById('confirmPassword').value;
      const phone = document.getElementById('phone').value.trim();
      const address = document.getElementById('address').value.trim();
      const submitBtn = document.getElementById('submitBtn');
      
      if (password.length >= 8 && 
          /\d/.test(password) && 
          /[!@#$%^&*]/.test(password) && 
          password === confirmPassword && 
          phone !== '' && 
          address !== '') {
        submitBtn.disabled = false;
      } else {
        submitBtn.disabled = true;
      }
    }
  </script>
</body>
</html>