<?php

namespace App\Models;

use CodeIgniter\Model;

class QuestionModel extends Model
{
    protected $table = 'questions';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $protectFields = true;
    protected $allowedFields = [
        'school_id',
        'staff_id',
        'standard',
        'subject',
        'chapter',
        'chapter_name',
        'topic_name',
        'question_type',
        'difficulty',
        'marks',
        'question_text',
        'option_a',
        'option_b',
        'option_c',
        'option_d',
        'correct_answer',
        'answer',
        'status',
        'admin_feedback',
        'reviewed_by',
        'reviewed_at'
    ];

    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    protected array $casts = [];
    protected array $castHandlers = [];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'school_id' => 'required|integer',
        'staff_id' => 'required|integer',
        'standard' => 'required|integer|greater_than[0]|less_than[13]',
        'subject' => 'required|string|max_length[100]',
        'question_type' => 'required|in_list[multiple_choice,short_answer,long_answer,essay]',
        'difficulty' => 'required|in_list[easy,medium,hard]',
        'marks' => 'required|integer|greater_than[0]|less_than[21]',
        'question_text' => 'required|string',
        'status' => 'in_list[draft,pending,approved,rejected]'
    ];

    protected $validationMessages = [
        'standard' => [
            'greater_than' => 'Standard must be between 1 and 12',
            'less_than' => 'Standard must be between 1 and 12'
        ],
        'marks' => [
            'greater_than' => 'Marks must be between 1 and 20',
            'less_than' => 'Marks must be between 1 and 20'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = [];
    protected $afterInsert = [];
    protected $beforeUpdate = [];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = [];
    protected $afterDelete = [];

    /**
     * Get questions by staff member
     */
    public function getQuestionsByStaff($staffId, $schoolId, $filters = [])
    {
        $builder = $this->where('staff_id', $staffId)
                        ->where('school_id', $schoolId);

        if (!empty($filters['subject'])) {
            $builder->where('subject', $filters['subject']);
        }

        if (!empty($filters['status'])) {
            $builder->where('status', $filters['status']);
        }

        if (!empty($filters['standard'])) {
            $builder->where('standard', $filters['standard']);
        }

        return $builder->orderBy('created_at', 'DESC')->findAll();
    }

    /**
     * Get questions for admin review
     */
    public function getQuestionsForReview($schoolId, $status = 'pending', $subject = null)
    {
        $builder = $this->select('questions.*, users.name as staff_name')
                        ->join('users', 'users.id = questions.staff_id')
                        ->where('questions.school_id', $schoolId);

        // Handle status filter
        if ($status !== 'all') {
            $builder->where('questions.status', $status);
        } else {
            // For 'all', show all submitted questions (exclude drafts)
            $builder->whereIn('questions.status', ['pending', 'approved', 'rejected']);
        }

        // Handle subject filter
        if ($subject) {
            $builder->where('questions.subject', $subject);
        }

        return $builder->orderBy('questions.reviewed_at', 'DESC')
                      ->orderBy('questions.created_at', 'DESC')
                      ->findAll();
    }

    /**
     * Get questions for admin review with pagination
     */
    public function getQuestionsForReviewPaginated($schoolId, $filters = [], $page = 1, $perPage = 10)
    {
        $builder = $this->select('questions.*, COALESCE(users.name, "Unknown") as staff_name')
                        ->join('users', 'users.id = questions.staff_id', 'left')
                        ->where('questions.school_id', $schoolId);

        // Handle status filter
        $status = $filters['status'] ?? 'all';
        if ($status !== 'all') {
            $builder->where('questions.status', $status);
        } else {
            // For 'all', show all submitted questions (exclude drafts)
            $builder->whereIn('questions.status', ['pending', 'approved', 'rejected']);
        }

        // Handle subject filter
        if (!empty($filters['subject'])) {
            $builder->where('questions.subject', $filters['subject']);
        }

        // Handle standard filter
        if (!empty($filters['standard'])) {
            $builder->where('questions.standard', $filters['standard']);
        }

        // Handle question type filter
        if (!empty($filters['type'])) {
            $builder->where('questions.question_type', $filters['type']);
        }

        // Handle search filter
        if (!empty($filters['search'])) {
            $searchTerm = $filters['search'];
            $builder->groupStart()
                    ->like('questions.question_text', $searchTerm)
                    ->orLike('questions.chapter', $searchTerm)
                    ->orLike('questions.chapter_name', $searchTerm)
                    ->orLike('questions.topic_name', $searchTerm)
                    ->orLike('users.name', $searchTerm)
                    ->groupEnd();
        }

        // Get total count
        $total = $builder->countAllResults(false);

        // Get paginated results
        $offset = ($page - 1) * $perPage;
        $questions = $builder->orderBy('questions.created_at', 'DESC')
                           ->orderBy('questions.id', 'DESC')
                           ->limit($perPage, $offset)
                           ->findAll();

        return [
            'questions' => $questions,
            'total' => $total
        ];
    }

    /**
     * Get review statistics for admin dashboard
     */
    public function getReviewStats($schoolId)
    {
        $stats = [
            'pending' => $this->where('school_id', $schoolId)
                             ->where('status', 'pending')
                             ->countAllResults(),
            'approved' => $this->where('school_id', $schoolId)
                              ->where('status', 'approved')
                              ->countAllResults(),
            'rejected' => $this->where('school_id', $schoolId)
                              ->where('status', 'rejected')
                              ->countAllResults()
        ];

        $stats['total'] = $stats['pending'] + $stats['approved'] + $stats['rejected'];

        return $stats;
    }

    /**
     * Get question statistics for staff
     */
    public function getStaffStats($staffId, $schoolId)
    {
        $totalQuestions = $this->where('staff_id', $staffId)
                              ->where('school_id', $schoolId)
                              ->countAllResults();

        $stats = [
            'total_questions' => $totalQuestions,
            'questions_created' => $totalQuestions, // For backward compatibility
            'pending_reviews' => $this->where('staff_id', $staffId)
                                     ->where('school_id', $schoolId)
                                     ->where('status', 'pending')
                                     ->countAllResults(),
            'pending_questions' => $this->where('staff_id', $staffId)
                                       ->where('school_id', $schoolId)
                                       ->where('status', 'pending')
                                       ->countAllResults(),
            'approved_questions' => $this->where('staff_id', $staffId)
                                        ->where('school_id', $schoolId)
                                        ->where('status', 'approved')
                                        ->countAllResults(),
            'rejected_questions' => $this->where('staff_id', $staffId)
                                        ->where('school_id', $schoolId)
                                        ->where('status', 'rejected')
                                        ->countAllResults(),
            'draft_questions' => $this->where('staff_id', $staffId)
                                     ->where('school_id', $schoolId)
                                     ->where('status', 'draft')
                                     ->countAllResults()
        ];

        return $stats;
    }

    /**
     * Get question statistics for admin
     */
    public function getAdminStats($schoolId)
    {
        $stats = [
            'total_questions' => $this->where('school_id', $schoolId)->countAllResults(),
            'pending_reviews' => $this->where('school_id', $schoolId)
                                     ->where('status', 'pending')
                                     ->countAllResults(),
            'approved_questions' => $this->where('school_id', $schoolId)
                                        ->where('status', 'approved')
                                        ->countAllResults(),
            'rejected_questions' => $this->where('school_id', $schoolId)
                                        ->where('status', 'rejected')
                                        ->countAllResults()
        ];

        return $stats;
    }

    /**
     * Approve question
     */
    public function approveQuestion($questionId, $adminId, $feedback = null)
    {
        return $this->update($questionId, [
            'status' => 'approved',
            'admin_feedback' => $feedback,
            'reviewed_by' => $adminId,
            'reviewed_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Reject question
     */
    public function rejectQuestion($questionId, $adminId, $feedback)
    {
        return $this->update($questionId, [
            'status' => 'rejected',
            'admin_feedback' => $feedback,
            'reviewed_by' => $adminId,
            'reviewed_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Submit question for review
     */
    public function submitForReview($questionId)
    {
        return $this->update($questionId, [
            'status' => 'pending'
        ]);
    }

}
